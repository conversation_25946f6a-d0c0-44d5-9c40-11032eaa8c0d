package com.ammar.medical.presentation.ui.screens.category

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Category
import com.ammar.medical.data.repository.CategoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class CategoryManagementUiState {
    object Loading : CategoryManagementUiState()
    data class Success(
        val categories: List<Category>,
        val showDialog: Boolean = false,
        val editingCategory: Category? = null,
        val error: String? = null
    ) : CategoryManagementUiState()
    data class Error(val message: String) : CategoryManagementUiState()
}

@HiltViewModel
class CategoryManagementViewModel @Inject constructor(
    private val categoryRepository: CategoryRepository
) : ViewModel() {
    private val _uiState = MutableStateFlow<CategoryManagementUiState>(CategoryManagementUiState.Loading)
    val uiState: StateFlow<CategoryManagementUiState> = _uiState.asStateFlow()

    init {
        loadCategories()
    }

    private fun loadCategories() {
        viewModelScope.launch {
            try {
                categoryRepository.categories.collect { categories ->
                    _uiState.value = CategoryManagementUiState.Success(categories)
                }
            } catch (e: Exception) {
                _uiState.value = CategoryManagementUiState.Error(e.message ?: "Failed to load categories")
            }
        }
    }

    fun showAddDialog() {
        val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return
        _uiState.value = currentState.copy(showDialog = true, editingCategory = null)
    }

    fun showEditDialog(category: Category) {
        val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return
        _uiState.value = currentState.copy(showDialog = true, editingCategory = category)
    }

    fun hideDialog() {
        val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return
        _uiState.value = currentState.copy(showDialog = false, editingCategory = null, error = null)
    }

    fun addCategory(name: String, color: Long, icon: Category.CategoryIcon) {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return@launch
                
                // Validate name
                if (name.isBlank()) {
                    _uiState.value = currentState.copy(error = "Category name is required")
                    return@launch
                }
                
                // Check for duplicates
                if (currentState.categories.any { it.name.equals(name, ignoreCase = true) }) {
                    _uiState.value = currentState.copy(error = "A category with this name already exists")
                    return@launch
                }

                categoryRepository.addCategory(name, color, icon)
                _uiState.value = currentState.copy(showDialog = false, error = null)
            } catch (e: Exception) {
                val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return@launch
                _uiState.value = currentState.copy(error = e.message ?: "Failed to add category")
            }
        }
    }

    fun updateCategory(category: Category) {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return@launch
                
                // Validate name
                if (category.name.isBlank()) {
                    _uiState.value = currentState.copy(error = "Category name is required")
                    return@launch
                }
                
                // Check for duplicates (excluding the current category)
                if (currentState.categories.any { 
                    it.id != category.id && it.name.equals(category.name, ignoreCase = true)
                }) {
                    _uiState.value = currentState.copy(error = "A category with this name already exists")
                    return@launch
                }

                categoryRepository.updateCategory(category)
                _uiState.value = currentState.copy(showDialog = false, error = null)
            } catch (e: Exception) {
                val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return@launch
                _uiState.value = currentState.copy(error = e.message ?: "Failed to update category")
            }
        }
    }

    fun deleteCategory(categoryId: Long) {
        viewModelScope.launch {
            try {
                categoryRepository.deleteCategory(categoryId)
            } catch (e: Exception) {
                val currentState = _uiState.value as? CategoryManagementUiState.Success ?: return@launch
                _uiState.value = currentState.copy(error = e.message ?: "Failed to delete category")
            }
        }
    }
} 