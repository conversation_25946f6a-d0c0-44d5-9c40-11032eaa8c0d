package com.ammar.medical.presentation.ui.screens.document

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.DocumentRepository
import com.ammar.medical.data.repository.TagRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class DocumentEditUiState {
    object Loading : DocumentEditUiState()
    data class Success(
        val document: Document,
        val tagSuggestions: Set<String> = emptySet()
    ) : DocumentEditUiState()
    data class Error(val message: String) : DocumentEditUiState()
}

@HiltViewModel
class DocumentEditViewModel @Inject constructor(
    private val documentRepository: DocumentRepository,
    private val tagRepository: TagRepository
) : ViewModel() {
    private val _uiState = MutableStateFlow<DocumentEditUiState>(DocumentEditUiState.Loading)
    val uiState: StateFlow<DocumentEditUiState> = _uiState.asStateFlow()

    private var currentDocument: Document? = null

    fun loadDocument(documentId: Long) {
        viewModelScope.launch {
            try {
                val document = documentRepository.getDocumentById(documentId)
                if (document != null) {
                    currentDocument = document
                    val tagSuggestions = tagRepository.getTagSuggestions()
                    _uiState.value = DocumentEditUiState.Success(document, tagSuggestions)
                } else {
                    _uiState.value = DocumentEditUiState.Error("Document not found")
                }
            } catch (e: Exception) {
                _uiState.value = DocumentEditUiState.Error(e.message ?: "Failed to load document")
            }
        }
    }

    fun updateTitle(title: String) {
        currentDocument?.let { document ->
            currentDocument = document.copy(title = title)
            _uiState.value = DocumentEditUiState.Success(
                currentDocument!!,
                (uiState.value as? DocumentEditUiState.Success)?.tagSuggestions ?: emptySet()
            )
        }
    }

    fun updateDescription(description: String) {
        currentDocument?.let { document ->
            currentDocument = document.copy(description = description)
            _uiState.value = DocumentEditUiState.Success(
                currentDocument!!,
                (uiState.value as? DocumentEditUiState.Success)?.tagSuggestions ?: emptySet()
            )
        }
    }

    fun updateTags(tags: Set<String>) {
        currentDocument?.let { document ->
            currentDocument = document.copy(tags = tags)
            _uiState.value = DocumentEditUiState.Success(
                currentDocument!!,
                (uiState.value as? DocumentEditUiState.Success)?.tagSuggestions ?: emptySet()
            )
        }
    }

    fun saveDocument() {
        viewModelScope.launch {
            try {
                currentDocument?.let { document ->
                    documentRepository.updateDocument(document)
                    _uiState.value = DocumentEditUiState.Success(
                        document,
                        (uiState.value as? DocumentEditUiState.Success)?.tagSuggestions ?: emptySet()
                    )
                }
            } catch (e: Exception) {
                _uiState.value = DocumentEditUiState.Error(e.message ?: "Failed to save document")
            }
        }
    }
} 