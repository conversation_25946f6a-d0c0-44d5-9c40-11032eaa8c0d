#!/bin/bash

# Source and destination directories
SOURCE_DIR="app/src/main/java/com/example/ammar2"
DEST_DIR="app/src/main/java/io/ammar/medical"

# Ensure destination directory exists
mkdir -p "$DEST_DIR"

# Function to copy and update a single file
process_file() {
    local source_file="$1"
    local rel_path="${source_file#$SOURCE_DIR/}"
    local dest_file="$DEST_DIR/$rel_path"
    local dir_path=$(dirname "$dest_file")
    
    # Create destination directory if it doesn't exist
    mkdir -p "$dir_path"
    
    # Copy and update package declaration
    sed 's/package com\.example\.ammar2/package io.ammar.medical/g' "$source_file" > "$dest_file"
    echo "Processed: $rel_path"
}

# Process directories recursively
process_directory() {
    local dir="$1"
    
    for item in "$dir"/*; do
        if [ -d "$item" ]; then
            # It's a directory, process it recursively
            process_directory "$item"
        elif [ -f "$item" ]; then
            # It's a file, update and copy it
            if [[ "$item" == *.kt || "$item" == *.java ]]; then
                process_file "$item"
            else
                # Just copy non-Java/Kotlin files as is
                local rel_path="${item#$SOURCE_DIR/}"
                local dest_file="$DEST_DIR/$rel_path"
                local dir_path=$(dirname "$dest_file")
                mkdir -p "$dir_path"
                cp "$item" "$dest_file"
                echo "Copied: $rel_path"
            fi
        fi
    done
}

# Start processing from the source directory
process_directory "$SOURCE_DIR"

echo "Package migration complete. Files were copied to $DEST_DIR"
echo "Now update all import statements in the new files." 