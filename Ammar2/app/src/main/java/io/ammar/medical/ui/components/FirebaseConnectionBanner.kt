package io.ammar.medical.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CloudOff
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * A banner that displays Firebase connection status and provides options to retry or work offline
 */
@Composable
fun FirebaseConnectionBanner(
    modifier: Modifier = Modifier,
    isNetworkConnected: StateFlow<Boolean>,
    isFirebaseReady: StateFlow<Boolean>,
    onRetry: () -> Unit,
    onWorkOffline: () -> Unit
) {
    val context = LocalContext.current
    val networkConnected by isNetworkConnected.collectAsState()
    val firebaseReady by isFirebaseReady.collectAsState()
    
    var showBanner by remember { mutableStateOf(false) }
    var showRetryButton by remember { mutableStateOf(true) }
    var message by remember { mutableStateOf("") }
    var bannerColor by remember { mutableStateOf(Color.Red.copy(alpha = 0.8f)) }
    
    // Add a delay before showing the banner to avoid false positives during startup
    var startupDelayComplete by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        // Wait 3 seconds before showing any connection issues to allow Firebase to initialize
        delay(3000)
        startupDelayComplete = true
    }
    
    // Determine banner visibility and message based on network and Firebase status
    LaunchedEffect(networkConnected, firebaseReady, startupDelayComplete) {
        // Only show connection issues after startup delay
        if (!startupDelayComplete) {
            showBanner = false
            return@LaunchedEffect
        }
        
        if (!networkConnected) {
            showBanner = true
            showRetryButton = true
            message = "No internet connection. Some features may be unavailable."
            bannerColor = Color.Red.copy(alpha = 0.8f)
        } else if (!firebaseReady) {
            showBanner = true
            showRetryButton = true
            message = "Some Firebase services are not fully connected. Tap for details."
            bannerColor = Color(0xFFFFA500).copy(alpha = 0.8f) // Orange color
        } else {
            // If everything is working, show a success message briefly then hide
            showBanner = true
            showRetryButton = false
            message = "Connected to Firebase successfully!"
            bannerColor = Color.Green.copy(alpha = 0.8f)
            
            // Hide the success banner after a delay
            delay(3000)
            showBanner = false
        }
    }
    
    AnimatedVisibility(
        visible = showBanner,
        enter = expandVertically() + fadeIn(),
        exit = shrinkVertically() + fadeOut()
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .background(bannerColor)
                .padding(8.dp),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (!networkConnected) Icons.Default.CloudOff else Icons.Default.Warning,
                    contentDescription = "Connection Status",
                    tint = Color.White,
                    modifier = Modifier.padding(end = 8.dp)
                )
                
                Text(
                    text = message,
                    color = Color.White,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.weight(1f)
                )
            }
            
            if (showRetryButton) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Button(
                        onClick = onRetry,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White.copy(alpha = 0.2f),
                            contentColor = Color.White
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Retry",
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        Text("Retry Connection")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    TextButton(
                        onClick = onWorkOffline,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color.White
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Work Offline")
                    }
                }
            }
        }
    }
} 