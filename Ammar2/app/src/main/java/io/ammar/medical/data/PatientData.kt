package io.ammar.medical.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Data class representing patient information
 */
@Parcelize
data class PatientData(
    val id: String = "",
    val name: String = "",
    val age: Int = 0,
    val gender: String = "",
    val email: String = "",
    val phone: String = "",
    val medicalRecordNumber: String = "",
    val doctorId: String = "",
    val doctorEmail: String = "",
    val lastUpdated: Date = Date(),
    val medicalConditions: List<String> = emptyList(),
    val medications: List<String> = emptyList(),
    val allergies: List<String> = emptyList()
) : Parcelable

/**
 * Data class representing a medical report analysis
 */
@Parcelize
data class MedicalAnalysis(
    val id: String = "",
    val patientId: String = "",
    val doctorId: String = "",
    val createdAt: Date = Date(),
    val analysisText: String = "",
    val fileCount: Int = 0,
    val fileTypes: List<String> = emptyList(),
    val criticalFindings: List<String> = emptyList(),
    val recommendations: List<String> = emptyList(),
    val isReviewed: Boolean = false,
    val doctorNotes: String = "",
    val originalExtractedTexts: List<String> = emptyList(),
    val isArabic: Boolean = false
) : Parcelable 