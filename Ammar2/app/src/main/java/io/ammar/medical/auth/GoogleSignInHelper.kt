package io.ammar.medical.auth

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.SetOptions
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.TimeoutException
import com.google.firebase.auth.FirebaseUser

class GoogleSignInHelper(private val context: Context) {
    private val TAG = "GoogleSignInHelper"
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    private lateinit var googleSignInClient: GoogleSignInClient
    
    init {
        try {
            initializeGoogleSignIn()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Google Sign In client", e)
            Log.e(TAG, "Error details:", e)
        }
    }
    
    private fun initializeGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestProfile()
            .requestId()
            .requestIdToken("241058691077-59hdd354eiojvgskur253568u6e8le4d.apps.googleusercontent.com") // Android client ID
            .build()
        
        googleSignInClient = GoogleSignIn.getClient(context, gso)
        Log.d(TAG, "Google Sign In client initialized successfully")
    }
    
    fun getSignInIntent() = if (::googleSignInClient.isInitialized) {
        googleSignInClient.signInIntent
    } else {
        try {
            // Try to initialize again if it failed before
            initializeGoogleSignIn()
            googleSignInClient.signInIntent
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get sign in intent", e)
            throw IllegalStateException("Google Sign In client not initialized")
        }
    }
    
    private fun isNetworkAvailable(): Boolean {
        return NetworkUtils.isNetworkAvailable(context)
    }
    
    suspend fun handleSignInResult(idToken: String): AuthResult {
        try {
            if (!isNetworkAvailable()) {
                Log.e(TAG, "Network unavailable during sign in")
                return AuthResult(false, errorMessage = "Network unavailable. Please check your internet connection and try again.")
            }
            
            Log.d(TAG, "Handling Google sign in result with token: ${idToken.take(10)}...")
            val credential = GoogleAuthProvider.getCredential(idToken, null)
            
            // Add timeout to Firebase authentication
            val authResult = withTimeoutOrNull(15000) { // 15 seconds timeout
                auth.signInWithCredential(credential).await()
            } ?: throw TimeoutException("Authentication timed out. Please try again.")
            
            val firebaseUser = authResult.user
            if (firebaseUser != null) {
                Log.d(TAG, "Firebase user authenticated: ${firebaseUser.uid}")
                // Check if user exists in Firestore
                try {
                    // Use the safe document getter
                    val userDocRef = db.collection("users").document(firebaseUser.uid)
                    val (userDoc, isFromCache) = FirebaseUtils.safeGetDocument(userDocRef)
                    
                    val user = if (userDoc != null && userDoc.exists()) {
                        Log.d(TAG, "Existing user found in Firestore ${if (isFromCache) "(from cache)" else "(from server)"}")
                        // Existing user - update last login
                        val existingUser = userDoc.toObject(User::class.java)
                        existingUser?.copy(
                            lastLogin = System.currentTimeMillis()
                        ) ?: createNewUser(firebaseUser)
                    } else {
                        Log.d(TAG, "Creating new user in Firestore")
                        // New user - create profile
                        createNewUser(firebaseUser)
                    }

                    // Save/Update user in Firestore with retry mechanism
                    try {
                        saveUserToFirestore(user, firebaseUser.uid)
                        return AuthResult(true, user)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to save user data to Firestore", e)
                        // Still return success since Firebase Auth succeeded
                        return AuthResult(true, user, "Authenticated but failed to update user data")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Firestore operation failed", e)
                    // Still return success since Firebase Auth succeeded
                    val user = createNewUser(firebaseUser)
                    return AuthResult(
                        true, 
                        user,
                        "Authenticated but Firestore is unavailable"
                    )
                }
            }
            Log.e(TAG, "Failed to get user info after authentication")
            return AuthResult(false, errorMessage = "Failed to get user info")
        } catch (e: Exception) {
            Log.e(TAG, "Google sign in failed", e)
            Log.e(TAG, "Error details:", e)
            
            val errorMessage = when {
                e is TimeoutException -> "Authentication timed out. Please try again later."
                e.message?.contains("network", ignoreCase = true) == true -> "Network error. Please check your internet connection."
                e.message?.contains("DEVELOPER_ERROR") == true -> "Authentication configuration error. Please contact support."
                e.message?.contains("INVALID_ACCOUNT") == true -> "Invalid Google account. Please try with a different account."
                e.message?.contains("AUTHENTICATION_FAILED") == true -> "Authentication failed. Please try again later."
                else -> e.message ?: "Unknown error occurred"
            }
            
            return AuthResult(false, errorMessage = errorMessage)
        }
    }
    
    private fun createNewUser(firebaseUser: FirebaseUser): User {
        return User(
            id = firebaseUser.uid,
            username = firebaseUser.displayName ?: firebaseUser.email?.substringBefore("@") ?: "",
            email = firebaseUser.email ?: "",
            hashedPassword = "", // Not needed for Google Sign In
            salt = "", // Not needed for Google Sign In
            createdAt = System.currentTimeMillis(),
            lastLogin = System.currentTimeMillis()
        )
    }
    
    private suspend fun saveUserToFirestore(user: User, userId: String, retryCount: Int = 0): Boolean {
        if (retryCount >= 3) {
            Log.e(TAG, "Failed to save user data after 3 retries")
            return false
        }
        
        return try {
            withContext(Dispatchers.IO) {
                // Use SetOptions.merge() to avoid overwriting existing data
                db.collection("users").document(userId)
                    .set(user, SetOptions.merge())
                    .await()
                Log.d(TAG, "User data saved to Firestore")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save user data to Firestore (attempt ${retryCount + 1})", e)
            
            // Only retry if it's a network-related issue and we have network
            if (isNetworkAvailable() && retryCount < 3 && 
                (e.message?.contains("network", ignoreCase = true) == true || 
                 e.message?.contains("timeout", ignoreCase = true) == true)) {
                
                // Exponential backoff
                val delayMs = 1000L * (1 shl retryCount)
                Log.d(TAG, "Retrying in $delayMs ms...")
                withContext(Dispatchers.IO) {
                    kotlinx.coroutines.delay(delayMs)
                }
                
                saveUserToFirestore(user, userId, retryCount + 1)
            } else {
                false
            }
        }
    }
    
    suspend fun signOut() {
        try {
            auth.signOut()
            if (::googleSignInClient.isInitialized) {
                googleSignInClient.signOut().await()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error signing out", e)
            Log.e(TAG, "Error details:", e)
        }
    }
} 