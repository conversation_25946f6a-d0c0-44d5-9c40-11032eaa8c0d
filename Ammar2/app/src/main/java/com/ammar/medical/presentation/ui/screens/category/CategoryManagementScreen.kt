package com.ammar.medical.presentation.ui.screens.category

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.ammar.medical.R
import com.ammar.medical.data.model.Category
import com.ammar.medical.presentation.components.CommonTopAppBar
import com.ammar.medical.presentation.components.LoadingIndicator

@Composable
fun CategoryManagementScreen(
    onNavigateBack: () -> Unit,
    viewModel: CategoryManagementViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            CommonTopAppBar(
                title = stringResource(R.string.categories),
                navigationIcon = Icons.Default.ArrowBack,
                onNavigationClick = onNavigateBack,
                actions = {
                    IconButton(onClick = { viewModel.showAddDialog() }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = stringResource(R.string.add_category)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (uiState) {
                is CategoryManagementUiState.Loading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is CategoryManagementUiState.Success -> {
                    val state = uiState as CategoryManagementUiState.Success
                    CategoryList(
                        categories = state.categories,
                        onEditCategory = viewModel::showEditDialog,
                        onDeleteCategory = viewModel::deleteCategory
                    )

                    if (state.showDialog) {
                        CategoryDialog(
                            category = state.editingCategory,
                            error = state.error,
                            onDismiss = viewModel::hideDialog,
                            onSave = { name, color, icon ->
                                if (state.editingCategory != null) {
                                    viewModel.updateCategory(
                                        state.editingCategory.copy(
                                            name = name,
                                            color = color,
                                            icon = icon
                                        )
                                    )
                                } else {
                                    viewModel.addCategory(name, color, icon)
                                }
                            }
                        )
                    }
                }
                is CategoryManagementUiState.Error -> {
                    Text(
                        text = (uiState as CategoryManagementUiState.Error).message,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun CategoryList(
    categories: List<Category>,
    onEditCategory: (Category) -> Unit,
    onDeleteCategory: (Long) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(categories) { category ->
            CategoryItem(
                category = category,
                onEdit = { onEditCategory(category) },
                onDelete = { onDeleteCategory(category.id) }
            )
        }
    }
}

@Composable
private fun CategoryItem(
    category: Category,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = Color(category.color),
                        shape = MaterialTheme.shapes.small
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = category.icon.icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            Text(
                text = category.name,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.weight(1f)
            )

            IconButton(onClick = onEdit) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = stringResource(R.string.edit_category)
                )
            }

            IconButton(onClick = { showDeleteDialog = true }) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = stringResource(R.string.delete_category)
                )
            }
        }
    }

    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text(stringResource(R.string.delete_category)) },
            text = { Text(stringResource(R.string.delete_confirmation_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteDialog = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text(stringResource(R.string.delete))
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

@Composable
private fun CategoryDialog(
    category: Category?,
    error: String?,
    onDismiss: () -> Unit,
    onSave: (String, Long, Category.CategoryIcon) -> Unit
) {
    var name by remember { mutableStateOf(category?.name ?: "") }
    var selectedColor by remember { mutableStateOf(category?.color ?: Category.PREDEFINED[0].color) }
    var selectedIcon by remember { mutableStateOf(category?.icon ?: Category.CategoryIcon.FOLDER) }
    var showIconPicker by remember { mutableStateOf(false) }
    var showColorPicker by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                stringResource(
                    if (category == null) R.string.add_category else R.string.edit_category
                )
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text(stringResource(R.string.category_name)) },
                    isError = error != null,
                    supportingText = error?.let { { Text(it) } },
                    modifier = Modifier.fillMaxWidth()
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { showIconPicker = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = selectedIcon.icon,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.select_icon))
                    }

                    OutlinedButton(
                        onClick = { showColorPicker = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .background(
                                    color = Color(selectedColor),
                                    shape = MaterialTheme.shapes.small
                                )
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.select_color))
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onSave(name, selectedColor, selectedIcon) }
            ) {
                Text(stringResource(R.string.save))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )

    if (showIconPicker) {
        AlertDialog(
            onDismissRequest = { showIconPicker = false },
            title = { Text(stringResource(R.string.select_icon)) },
            text = {
                LazyColumn {
                    items(Category.CategoryIcon.values()) { icon ->
                        ListItem(
                            headlineContent = { Text(icon.name) },
                            leadingContent = {
                                Icon(
                                    imageVector = icon.icon,
                                    contentDescription = null
                                )
                            },
                            modifier = Modifier.clickable {
                                selectedIcon = icon
                                showIconPicker = false
                            }
                        )
                    }
                }
            },
            confirmButton = {}
        )
    }

    if (showColorPicker) {
        AlertDialog(
            onDismissRequest = { showColorPicker = false },
            title = { Text(stringResource(R.string.select_color)) },
            text = {
                LazyColumn {
                    items(MaterialTheme.colorScheme.run {
                        listOf(
                            primary,
                            secondary,
                            tertiary,
                            error,
                            primaryContainer,
                            secondaryContainer,
                            tertiaryContainer,
                            errorContainer
                        )
                    }) { color ->
                        ListItem(
                            headlineContent = { Text("") },
                            leadingContent = {
                                Box(
                                    modifier = Modifier
                                        .size(32.dp)
                                        .background(
                                            color = color,
                                            shape = MaterialTheme.shapes.small
                                        )
                                )
                            },
                            modifier = Modifier.clickable {
                                selectedColor = color.toArgb().toLong()
                                showColorPicker = false
                            }
                        )
                    }
                }
            },
            confirmButton = {}
        )
    }
} 