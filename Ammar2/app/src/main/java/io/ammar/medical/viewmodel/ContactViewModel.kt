package io.ammar.medical.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.ammar.medical.data.Contact
import io.ammar.medical.repository.ContactRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for managing contacts
 */
class ContactViewModel : ViewModel() {
    private val repository = ContactRepository()
    
    // Contacts state
    private val _contacts = MutableStateFlow<List<Contact>>(emptyList())
    val contacts: StateFlow<List<Contact>> = _contacts.asStateFlow()
    
    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // Error state
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // Success message state
    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()
    
    /**
     * Load all contacts for the current user
     */
    fun loadContacts() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            repository.getContacts()
                .onSuccess { contactsList ->
                    _contacts.value = contactsList
                    _isLoading.value = false
                }
                .onFailure { exception ->
                    _error.value = exception.message ?: "Failed to load contacts"
                    _isLoading.value = false
                }
        }
    }
    
    /**
     * Add a new contact
     */
    fun addContact(uid: String, name: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            _successMessage.value = null
            
            repository.addContact(uid, name)
                .onSuccess { contact ->
                    // Add the new contact to the list
                    val currentList = _contacts.value.toMutableList()
                    currentList.add(0, contact) // Add at the beginning
                    _contacts.value = currentList
                    
                    _successMessage.value = "Contact added successfully"
                    _isLoading.value = false
                }
                .onFailure { exception ->
                    _error.value = exception.message ?: "Failed to add contact"
                    _isLoading.value = false
                }
        }
    }
    
    /**
     * Delete a contact
     */
    fun deleteContact(contact: Contact) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            _successMessage.value = null
            
            repository.deleteContact(contact)
                .onSuccess {
                    // Remove the contact from the list
                    val currentList = _contacts.value.toMutableList()
                    currentList.remove(contact)
                    _contacts.value = currentList
                    
                    _successMessage.value = "Contact deleted successfully"
                    _isLoading.value = false
                }
                .onFailure { exception ->
                    _error.value = exception.message ?: "Failed to delete contact"
                    _isLoading.value = false
                }
        }
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * Clear success message
     */
    fun clearSuccessMessage() {
        _successMessage.value = null
    }
} 