package io.ammar.medical.auth

import java.util.UUID

data class User(
    val id: String = UUID.randomUUID().toString(),
    val username: String,
    val email: String,
    val hashedPassword: String,
    val salt: String,
    val createdAt: Long = System.currentTimeMillis(),
    val lastLogin: Long? = null
)

data class AuthResult(
    val success: <PERSON><PERSON><PERSON>,
    val user: User? = null,
    val errorMessage: String? = null
)

data class LoginCredentials(
    val username: String,
    val password: String
)

data class RegisterCredentials(
    val username: String,
    val email: String,
    val password: String,
    val confirmPassword: String
) 