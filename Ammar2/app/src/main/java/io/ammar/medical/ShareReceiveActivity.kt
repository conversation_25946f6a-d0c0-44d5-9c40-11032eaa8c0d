package io.ammar.medical
import io.ammar.medical.R
import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.webkit.MimeTypeMap
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.Dp
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import coil.compose.rememberAsyncImagePainter
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import com.google.ai.client.generativeai.type.generationConfig
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStreamReader
import java.util.UUID
import java.util.zip.ZipInputStream
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.shape.CircleShape
import java.io.IOException
import android.content.ClipData
import android.content.ClipboardManager
import android.net.http.HttpResponseCache
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import com.google.zxing.integration.android.IntentIntegrator
import com.google.zxing.integration.android.IntentResult
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.TextSnippet
import androidx.compose.material.icons.filled.Description
import kotlinx.coroutines.delay
import org.json.JSONObject
import java.util.concurrent.TimeoutException

class ShareReceiveActivity : ComponentActivity() {
    private val TAG = "ShareReceiveActivity"
    
    // Temporary URI for camera capture
    var tempImageUri: Uri? = null
    
    // Store PC server URL
    var pcServerUrl: String? = null
    
    // PC connected flag
    var isPcConnected: Boolean = false
    
    // QR Code scanner result launcher
    lateinit var qrCodeLauncher: ActivityResultLauncher<ScanOptions>
    
    // Gemini API model for text analysis
    private val geminiModel = GenerativeModel(
        modelName = "gemini-2.0-flash",
        apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk" // Same API key as in MedicalAnalyzerViewModel
    )

    // Camera launcher
    lateinit var cameraLauncher: ActivityResultLauncher<Uri>
    
    // Permission launcher
    lateinit var permissionLauncher: ActivityResultLauncher<String>
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize camera launcher
        cameraLauncher = registerForActivityResult(ActivityResultContracts.TakePicture()) { success ->
            if (success) {
                setContent {
                    MaterialTheme {
                        ShareReceiveScreen(SharedContent.Image(tempImageUri))
                    }
                }
            }
        }

        // Initialize permission launcher
        permissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                // Permission granted, launch camera
                val imageFile = File(
                    cacheDir,
                    "camera_photo_${System.currentTimeMillis()}.jpg"
                )
                tempImageUri = FileProvider.getUriForFile(
                    this,
                    "${packageName}.fileprovider",
                    imageFile
                )
                cameraLauncher.launch(tempImageUri)
            } else {
                // Permission denied
                Toast.makeText(
                    this,
                    "Camera permission is required to take photos",
                    Toast.LENGTH_LONG
                ).show()
            }
        }

        // Initialize QR code scanner launcher
        qrCodeLauncher = registerForActivityResult(ScanContract()) { result ->
            if (result.contents != null) {
                Log.d(TAG, "QR Code scanned: ${result.contents}")
                
                val qrContent = result.contents
                
                // Extract server URL from QR code
                try {
                    if (qrContent.startsWith("PC_")) {
                        // This is a PC connection code from the sync script
                        val sessionId = qrContent.substringBefore(":")
                        pcServerUrl = "http://${getString(R.string.server_ip)}:5000/send_text/default"
                        isPcConnected = true
                        Toast.makeText(this, "Connected to PC Transfer Service", Toast.LENGTH_SHORT).show()
                        Log.d(TAG, "Connected to PC with session ID: $sessionId")
                    } else if (qrContent.startsWith("http://") || qrContent.startsWith("https://")) {
                        // This is a regular URL
                        pcServerUrl = qrContent
                        isPcConnected = true
                        Toast.makeText(this, "Connected to PC: $qrContent", Toast.LENGTH_SHORT).show()
                    } else {
                        // Try to interpret as a direct IP address
                        pcServerUrl = "http://${qrContent}:5000/send_text/default"
                        isPcConnected = true
                        Toast.makeText(this, "Connected to PC at $qrContent", Toast.LENGTH_SHORT).show()
                    }
                    
                    // Update UI with the new PC connection
                    setContent {
                        MaterialTheme {
                            ShareReceiveScreen(sharedContent = null)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing QR code", e)
                    Toast.makeText(this, "Error connecting to PC: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }

        // Get the shared content
        val sharedContent = when {
            intent?.action == Intent.ACTION_SEND -> {
                when {
                    intent.type?.startsWith("image/") == true -> {
                        val uri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                        SharedContent.Image(uri)
                    }
                    intent.type == "text/plain" -> {
                        val text = intent.getStringExtra(Intent.EXTRA_TEXT)
                        SharedContent.Text(text)
                    }
                    intent.type?.startsWith("application/pdf") == true -> {
                        val uri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                        SharedContent.Pdf(uri)
                    }
                    intent.type == "application/zip" || intent.type == "application/x-zip-compressed" -> {
                        val uri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                        SharedContent.Zip(uri)
                    }
                    intent.type != null && intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM) != null -> {
                        // Handle other file types
                        val uri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                        val mimeType = intent.type ?: getMimeType(uri)
                        SharedContent.File(uri, mimeType)
                    }
                    else -> {
                        Log.d(TAG, "Unsupported content type: ${intent.type}")
                        null
                    }
                }
            }
            else -> null
        }

        Log.d(TAG, "Received shared content: $sharedContent")

        setContent {
            MaterialTheme {
                ShareReceiveScreen(sharedContent)
            }
        }
    }
    
    private fun getMimeType(uri: Uri?): String {
        if (uri == null) return "application/octet-stream"
        
        val contentResolver = contentResolver
        return contentResolver.getType(uri) ?: run {
            val fileExtension = MimeTypeMap.getFileExtensionFromUrl(uri.toString())
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(fileExtension) ?: "application/octet-stream"
        }
    }
    
    // Function to extract zip file and find text, image, and PDF files
    // This function extracts all types of files from a ZIP archive and processes them:
    // - Text files are read directly
    // - Images are processed with Gemini AI for text extraction
    // - PDFs are noted (actual extraction would require a PDF library)
    // Returns combined text from all processed files
    suspend fun extractZipAndFindTextFile(
        uri: Uri,
        onFileFound: (String) -> Unit,
        onFileProcessed: (String, String) -> Unit
    ): String? = withContext(Dispatchers.IO) {
        try {
            val contentResolver = contentResolver
            val inputStream = contentResolver.openInputStream(uri) ?: return@withContext null
            val zipInputStream = ZipInputStream(inputStream)
            val cacheDir = cacheDir
            val extractedTexts = mutableListOf<String>()
            val extractedFiles = mutableListOf<Pair<File, String>>() // File and mimetype
            val fileTypes = mutableListOf<String>()
            
            // First extract all files from the ZIP
            var entry = zipInputStream.nextEntry
            while (entry != null) {
                val fileName = entry.name
                onFileFound(fileName)
                
                if (!entry.isDirectory) {
                    // Create a temporary file
                    val tempFile = File(cacheDir, fileName.substringAfterLast("/"))
                    tempFile.outputStream().use { output ->
                        zipInputStream.copyTo(output)
                    }
                    
                    // Determine file type based on extension
                    val fileExtension = fileName.substringAfterLast(".", "").toLowerCase()
                    val mimeType = when (fileExtension) {
                        "txt", "csv", "md", "json" -> "text/${fileExtension}"
                        "jpg", "jpeg", "png", "gif", "bmp" -> "image/${fileExtension}"
                        "pdf" -> "application/pdf"
                        else -> MimeTypeMap.getSingleton().getMimeTypeFromExtension(fileExtension) ?: "application/octet-stream"
                    }
                    
                    // Store file with its mime type
                    extractedFiles.add(Pair(tempFile, mimeType))
                    
                    // Determine file type for analysis
                    val fileType = when {
                        mimeType.startsWith("text/") -> {
                            when {
                                fileName.contains("lab", ignoreCase = true) -> "Lab Report"
                                fileName.contains("report", ignoreCase = true) -> "Medical Report"
                                fileName.contains("history", ignoreCase = true) -> "Patient History"
                                fileName.contains("result", ignoreCase = true) -> "Test Results"
                                else -> "Text Document"
                            }
                        }
                        mimeType.startsWith("image/") -> {
                            when {
                                fileName.contains("xray", ignoreCase = true) || 
                                fileName.contains("x-ray", ignoreCase = true) -> "X-Ray Image"
                                fileName.contains("scan", ignoreCase = true) -> "Medical Scan"
                                fileName.contains("mri", ignoreCase = true) -> "MRI Scan"
                                fileName.contains("ct", ignoreCase = true) -> "CT Scan"
                                fileName.contains("ultrasound", ignoreCase = true) -> "Ultrasound"
                                else -> "Medical Image"
                            }
                        }
                        mimeType == "application/pdf" -> {
                            when {
                                fileName.contains("lab", ignoreCase = true) -> "Lab Report (PDF)"
                                fileName.contains("report", ignoreCase = true) -> "Medical Report (PDF)"
                                fileName.contains("history", ignoreCase = true) -> "Patient History (PDF)"
                                fileName.contains("result", ignoreCase = true) -> "Test Results (PDF)"
                                else -> "Medical Document (PDF)"
                            }
                        }
                        else -> "Unknown Medical Document"
                    }
                    fileTypes.add(fileType)
                }
                
                zipInputStream.closeEntry()
                entry = zipInputStream.nextEntry
            }
            
            // Process the extracted files
            extractedFiles.forEachIndexed { index, (file, mimeType) ->
                try {
                    val extractedText = when {
                        mimeType.startsWith("text/") -> {
                            // For text files, just read the content
                            file.readText()
                        }
                        mimeType.startsWith("image/") -> {
                            // For images, process with Gemini AI
                            processImageWithGemini(file)
                        }
                        mimeType == "application/pdf" -> {
                            // For PDFs, just note that it's a PDF (would need a PDF library to extract text)
                            "PDF file detected: ${file.name}. PDF content processing would require specific libraries."
                        }
                        else -> {
                            "Unsupported file type: $mimeType for file ${file.name}"
                        }
                    }
                    
                    if (extractedText.isNotEmpty()) {
                        extractedTexts.add(extractedText)
                        onFileProcessed(file.name, extractedText)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing file: ${file.name}", e)
                }
            }
            
            // Combine all extracted texts
            if (extractedTexts.isNotEmpty()) {
                // Get Gemini model for analysis
                val geminiModel = GenerativeModel(
                    modelName = "gemini-2.0-flash",
                    apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk",
                    generationConfig = generationConfig {
                        temperature = 0.7f
                        topK = 40
                        topP = 0.95f
                        maxOutputTokens = 2048
                    }
                )
                
                // Combine all extracted text for analysis
                val allExtractedText = extractedTexts.mapIndexed { index, text ->
                    val fileType = if (index < fileTypes.size) fileTypes[index] else "Document"
                    "$fileType ${index + 1}: $text"
                }.joinToString("\n\n")
                
                // Create prompt for analysis similar to MedicalAnalysisViewModel
                val prompt = """
                    You are a medical assistant analyzing patient medical reports, lab results, and radiology reports.
                    Analyze the following text extracted from multiple medical files.
                    
                    Provide a comprehensive medical analysis that includes:
                    
                    1. SUMMARY: A brief 2-3 sentence overview of the patient's condition
                    2. CRITICAL FINDINGS: Highlight any abnormal or concerning values that require immediate attention (mark these with 🚨)
                    3. KEY METRICS: List important lab values, vital signs, or measurements with their normal ranges
                    4. DIAGNOSIS: Potential diagnoses based on the findings
                    5. RECOMMENDATIONS: Suggested next steps, additional tests, or treatments
                    
                    Format your response in a clear, structured way that a doctor can quickly review.
                    Be concise but thorough, and prioritize the most clinically significant information.
                    
                    Here is the extracted text from all medical files:
                    
                    $allExtractedText
                """.trimIndent()
                
                // Generate analysis
                val response = geminiModel.generateContent(prompt)
                val analysisText = response.text
                
                return@withContext analysisText ?: allExtractedText
            }

            return@withContext if (extractedTexts.isNotEmpty()) extractedTexts.joinToString("\n\n---\n\n") else null
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting zip file", e)
            return@withContext "Error extracting ZIP file: ${e.message}"
        }
    }
    
    // Function to analyze text with Gemini
    suspend fun analyzeTextWithGemini(text: String): String = withContext(Dispatchers.IO) {
        try {
            val prompt = """
                Analyze the following text and provide a comprehensive summary:
                
                $text
                
                Please include:
                1. Main topics or themes
                2. Key points and insights
                3. Any important data or statistics
                4. Overall sentiment or tone
                5. Recommendations or conclusions (if applicable)
                
                Format your response in a clear, structured manner.
            """.trimIndent()
            
            // Create content with just the text prompt
            val content = content {
                text(prompt)
            }
            
            val response = geminiModel.generateContent(content)
            return@withContext response.text ?: "No analysis available"
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing text with Gemini", e)
            return@withContext "Error analyzing text: ${e.message}"
        }
    }
    
    // Function to read text from a file URI
    suspend fun readTextFile(uri: Uri): String? = withContext(Dispatchers.IO) {
        try {
            val contentResolver = contentResolver
            val inputStream = contentResolver.openInputStream(uri) ?: return@withContext null
            val reader = BufferedReader(InputStreamReader(inputStream))
            val stringBuilder = StringBuilder()
            var line: String?
            
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            
            reader.close()
            inputStream.close()
            
            return@withContext stringBuilder.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Error reading text file", e)
            return@withContext "Error reading text file: ${e.message}"
        }
    }
    
    // Function to extract text from image using Gemini
    suspend fun extractTextFromImage(uri: Uri): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting image text extraction")
            
            // Get input stream from URI
            val inputStream = contentResolver.openInputStream(uri)
            if (inputStream == null) {
                Log.e(TAG, "Failed to open input stream for image")
                return@withContext "Error: Could not open image file"
            }
            
            // Convert input stream to bitmap
            val bitmap = android.graphics.BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            if (bitmap == null) {
                Log.e(TAG, "Failed to decode bitmap from input stream")
                return@withContext "Error: Could not decode image"
            }
            
            // Create content for Gemini with the bitmap and prompt
            val prompt = "Extract all text visible in this image. Format it clearly and preserve the structure."
            val content = content {
                text(prompt)
                image(bitmap)
            }
            
            // Generate content with Gemini
            val response = geminiModel.generateContent(content)
            
            val extractedText = response.text ?: "No text extracted"
            Log.d(TAG, "Text extraction complete: ${extractedText.take(100)}...")
            
            return@withContext extractedText
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting text from image", e)
            return@withContext "Error extracting text from image: ${e.message}"
        }
    }
    
    // Function to extract text from PDF using Gemini
    suspend fun extractTextFromPdf(uri: Uri): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting PDF text extraction")
            
            // For PDF files, we'll use Gemini's ability to process PDFs directly
            // Get input stream from URI
            val inputStream = contentResolver.openInputStream(uri)
            if (inputStream == null) {
                Log.e(TAG, "Failed to open input stream for PDF")
                return@withContext "Error: Could not open PDF file"
            }
            
            // Create a temporary file to store the PDF
            val tempFile = File(cacheDir, "temp_${System.currentTimeMillis()}.pdf")
            val outputStream = FileOutputStream(tempFile)
            
            // Copy the input stream to the temporary file
            val buffer = ByteArray(1024)
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            
            outputStream.close()
            inputStream.close()
            
            // Now we have the PDF as a file, we can create content for Gemini
            // Since Gemini doesn't directly support PDF files in the API, we'll use a different approach
            // We'll ask Gemini to extract text from the first few pages as images
            
            // For simplicity, we'll use a text prompt explaining we're working with a PDF
            val prompt = """
                I'm working with a PDF file but can't directly send it to you.
                The PDF filename is: ${getFileName(applicationContext, uri)}
                
                Please provide instructions on how I can best extract text from this PDF file
                and then analyze it. I'm looking for a comprehensive approach that will:
                
                1. Extract all text content from the PDF
                2. Preserve the structure and formatting as much as possible
                3. Handle any potential tables, charts, or images with text
                
                After extraction, I want to analyze the content for key information.
            """.trimIndent()
            
            // Create content with just the text prompt
            val content = content {
                text(prompt)
            }
            
            val response = geminiModel.generateContent(content)
            val instructions = response.text ?: "No instructions available"
            
            // For now, we'll return the instructions as our result
            // In a real implementation, you would use a PDF library like PDFBox or iText
            // to extract the text from the PDF file
            
            return@withContext "PDF processing instructions:\n\n$instructions\n\nNote: For full PDF text extraction, consider implementing a PDF library like PDFBox or iText in your app."
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting text from PDF", e)
            return@withContext "Error extracting text from PDF: ${e.message}"
        }
    }

    private fun uploadFile(uri: Uri?, folder: String, extension: String, callback: (Boolean, String) -> Unit) {
        if (uri == null) {
            callback(false, "Invalid file URI")
            return
        }
        
        val storage = FirebaseStorage.getInstance()
        val auth = FirebaseAuth.getInstance()
        val userId = auth.currentUser?.uid ?: "anonymous"
        
        val fileRef = storage.reference
            .child(folder)
            .child(userId)
            .child("${UUID.randomUUID()}.$extension")

        fileRef.putFile(uri)
            .addOnSuccessListener {
                callback(true, "File uploaded successfully!")
            }
            .addOnFailureListener {
                callback(false, "Failed to upload file: ${it.message}")
            }
    }

    // Function to save extracted text to Firebase
    suspend fun saveExtractedText(
        text: String,
        sourceType: String,
        fileName: String? = null
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val auth = FirebaseAuth.getInstance()
            
            // Try to sign in anonymously if not signed in
            if (auth.currentUser == null) {
                try {
                    val authResult = auth.signInAnonymously().await()
                    Log.d(TAG, "Anonymous sign in successful: ${authResult.user?.uid}")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to sign in anonymously", e)
                    return@withContext Result.failure(Exception("Authentication failed: ${e.message}"))
                }
            }

            val userId = auth.currentUser?.uid ?: return@withContext Result.failure(Exception("Not signed in"))
            val db = FirebaseFirestore.getInstance()
            
            // Create user document if it doesn't exist
            try {
                val userDoc = db.collection("users").document(userId)
                if (!userDoc.get().await().exists()) {
                    val userData = hashMapOf(
                        "username" to "Anonymous User",
                        "isAnonymous" to true,
                        "createdAt" to System.currentTimeMillis(),
                        "lastLogin" to System.currentTimeMillis()
                    )
                    userDoc.set(userData).await()
                    Log.d(TAG, "Created new user document for $userId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error creating user document", e)
                // Continue even if user document creation fails
            }
            
            val documentId = UUID.randomUUID().toString()
            val currentTime = System.currentTimeMillis()
            val oneHourInMillis = 60 * 60 * 1000 // 1 hour in milliseconds
            val expirationTime = currentTime + oneHourInMillis
            
            val extractedTextData = hashMapOf(
                "userId" to userId,
                "text" to text,
                "sourceType" to sourceType,
                "fileName" to fileName,
                "timestamp" to currentTime,
                "documentId" to documentId,
                "expirationTime" to expirationTime // Add expiration time (1 hour from now)
            )
            
            try {
                // Save to Firestore
                db.collection("extracted_texts")
                    .document(documentId)
                    .set(extractedTextData)
                    .await()
                
                Log.d(TAG, "Successfully saved extracted text with ID: $documentId (expires in 1 hour)")
                return@withContext Result.success(documentId)
            } catch (e: Exception) {
                val errorMessage = when {
                    e.message?.contains("permission denied", ignoreCase = true) == true -> 
                        "Permission denied. Please check Firebase rules and ensure you're properly authenticated."
                    e.message?.contains("network", ignoreCase = true) == true -> 
                        "Network error. Please check your internet connection."
                    e.message?.contains("PERMISSION_DENIED", ignoreCase = true) == true ->
                        "Firebase permission denied. Please ensure you have the correct access rights."
                    else -> "Error: ${e.message}"
                }
                Log.e(TAG, "Failed to save extracted text", e)
                return@withContext Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error in saveExtractedText", e)
            return@withContext Result.failure(e)
        }
    }

    // Function to get user sync info
    suspend fun getUserSyncInfo(): String = withContext(Dispatchers.IO) {
        val auth = FirebaseAuth.getInstance()
        val userId = auth.currentUser?.uid ?: return@withContext "Not signed in"
        
        return@withContext """
            Firebase Sync Information
            ------------------------
            User ID: $userId
            Anonymous: ${auth.currentUser?.isAnonymous}
            Last Sync: ${java.util.Date()}
            
            IMPORTANT: Extracted texts will expire after 1 hour.
            Run the sync script as soon as possible to retrieve your data.
            
            Use this User ID in the sync script to access your extracted texts.
            Keep this ID safe as it's your only way to access your data!
        """.trimIndent()
    }

    /**
     * Process an image file with Gemini AI to extract text
     */
    private suspend fun processImageWithGemini(imageFile: File): String {
        return try {
            // Create bitmap from file
            val bitmap = android.graphics.BitmapFactory.decodeFile(imageFile.absolutePath)
                ?: return "Could not decode image: ${imageFile.name}"
            
            // Initialize Gemini model
            val geminiModel = GenerativeModel(
                modelName = "gemini-2.0-flash",
                apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk"
            )
            
            // Create content with image and prompt for medical analysis
            val prompt = """
                Extract and structure all the text from this medical document.
                Include all visible text, numbers, medical terminology, and data.
                Format the output in a clear, structured way.
                If there are any medical terms, lab values, or important findings, make sure to include them.
                Include all numerical values and their units exactly as they appear.
                If there are normal ranges specified for lab values, include them.
            """.trimIndent()
            
            val imageContent = content {
                image(bitmap)
                text(prompt)
            }
            
            // Generate content
            val response = geminiModel.generateContent(imageContent)
            
            // Return extracted text or error message
            response.text ?: "No text could be extracted from image: ${imageFile.name}"
        } catch (e: Exception) {
            Log.e(TAG, "Error processing image with Gemini", e)
            "Error processing image ${imageFile.name}: ${e.message}"
        }
    }

    // Add this function near other sharing functions
    fun shareTextDirectly(text: String, context: Context) {
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, text)
            putExtra(Intent.EXTRA_SUBJECT, "Extracted Text")
        }
        
        context.startActivity(Intent.createChooser(shareIntent, "Share Extracted Text"))
    }

    // Function to send text to PC via HTTP
    suspend fun sendTextToPC(text: String, serverUrl: String): Result<String> = withContext(Dispatchers.IO) {
        var retries = 0
        val maxRetries = 3
        
        while (retries < maxRetries) {
            try {
                Log.d(TAG, "Sending text to PC (attempt ${retries+1}/$maxRetries): $serverUrl")
                
                // Parse the URL and modify if necessary
                var targetUrl = serverUrl
                if (serverUrl.startsWith("PC_")) {
                    // Handle direct PC_ format from QR
                    val sessionId = serverUrl.substringBefore(":")
                    targetUrl = "http://${getString(R.string.server_ip)}:5000/send_text/$sessionId"
                }
                
                val url = URL(targetUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8")
                connection.setRequestProperty("Accept", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 15000 // 15 second timeout
                connection.readTimeout = 15000   // 15 second timeout
                
                // Create JSON payload with proper JSON escaping using JSONObject
                val jsonObject = JSONObject()
                jsonObject.put("text", text)
                
                val jsonPayload = jsonObject.toString()
                Log.d(TAG, "Sending JSON payload length: ${jsonPayload.length}")
                
                // Send the request
                val outputStream = connection.outputStream
                val writer = OutputStreamWriter(outputStream, "UTF-8")
                writer.write(jsonPayload)
                writer.flush()
                writer.close()
                
                // Check response
                val responseCode = connection.responseCode
                if (responseCode in 200..299) { // Accept any 2xx status code
                    val inputStream = connection.inputStream
                    val response = inputStream.bufferedReader().use { it.readText() }
                    Log.d(TAG, "Server response: $response")
                    inputStream.close()
                    connection.disconnect()
                    return@withContext Result.success("Text sent successfully to PC")
                } else {
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e(TAG, "HTTP Error: $responseCode, Details: $errorResponse")
                    errorStream?.close()
                    connection.disconnect()
                    
                    // If we got a 500 error or other server error, retry
                    if ((responseCode >= 500 || responseCode == 408) && retries < maxRetries - 1) {
                        retries++
                        kotlinx.coroutines.delay(1000L * (retries)) // Increasing backoff delay
                        continue
                    }
                    
                    return@withContext Result.failure(Exception("Failed to send text. HTTP Error: $responseCode"))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending text to PC", e)
                
                // If it's a network-related exception and we have retries left
                if ((e is IOException || e is TimeoutException) && retries < maxRetries - 1) {
                    retries++
                    kotlinx.coroutines.delay(1000L * (retries)) // Increasing backoff delay
                    continue
                }
                
                return@withContext Result.failure(Exception("Connection error: ${e.message}"))
            }
        }
        
        return@withContext Result.failure(Exception("Failed after $maxRetries attempts"))
    }

    // Add methods to save/restore state on configuration changes
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // Save PC connection state
        outState.putString("pcServerUrl", pcServerUrl)
        outState.putBoolean("isPcConnected", isPcConnected)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        // Restore PC connection state
        pcServerUrl = savedInstanceState.getString("pcServerUrl")
        isPcConnected = savedInstanceState.getBoolean("isPcConnected")
    }

    // Function to analyze text with Gemini in Arabic
    suspend fun analyzeTextWithGeminiInArabic(text: String): String? {
        return try {
            val prompt = """
                Please analyze this medical text and provide a detailed explanation in Arabic.
                Include:
                1. A summary of the main findings
                2. Any important medical terms and their meanings
                3. Any concerning or noteworthy values
                4. Recommendations or next steps if applicable
                
                Medical text to analyze:
                $text
            """.trimIndent()
            
            val content = content {
                text(prompt)
            }
            
            val response = geminiModel.generateContent(content)
            response.text
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing text with Gemini in Arabic", e)
            null
        }
    }
}

sealed class SharedContent {
    data class Image(val uri: Uri?) : SharedContent()
    data class Text(val content: String?) : SharedContent()
    data class Pdf(val uri: Uri?) : SharedContent()
    data class Zip(val uri: Uri?) : SharedContent()
    data class File(val uri: Uri?, val mimeType: String?) : SharedContent()
}

@Composable
fun ShareReceiveScreen(sharedContent: SharedContent?) {
    var extractedText by rememberSaveable { mutableStateOf<String?>(null) }
    var textAnalysis by rememberSaveable { mutableStateOf<String?>(null) }
    var isUploading by rememberSaveable { mutableStateOf(false) }
    var uploadStatus by rememberSaveable { mutableStateOf("") }
    var showSyncDialog by rememberSaveable { mutableStateOf(false) }
    var syncInfo by rememberSaveable { mutableStateOf<String?>(null) }
    
    // Add ZIP extraction progress tracking
    var zipProcessedFiles by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var zipCurrentFile by rememberSaveable { mutableStateOf<String?>(null) }
    var showZipProgress by rememberSaveable { mutableStateOf(false) }
    
    // Add PC connection state
    var isPcConnected by rememberSaveable { mutableStateOf(false) }
    var isSendingToPC by rememberSaveable { mutableStateOf(false) }
    var pcConnectionStatus by rememberSaveable { mutableStateOf<String?>(null) }
    
    val context = LocalContext.current
    val activity = context as? ShareReceiveActivity
    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    
    // Get current Firebase user
    val auth = FirebaseAuth.getInstance()
    var currentUser by rememberSaveable { mutableStateOf(auth.currentUser) }
    var userId by rememberSaveable { mutableStateOf(currentUser?.uid ?: "Not signed in") }
    var isAnonymous by rememberSaveable { mutableStateOf(currentUser?.isAnonymous ?: false) }
    var isSigningIn by rememberSaveable { mutableStateOf(false) }

    // Get pcServerUrl from activity
    isPcConnected = activity?.pcServerUrl != null

    // Add LaunchedEffect to handle shared image text extraction
    LaunchedEffect(sharedContent) {
        if (sharedContent != null && activity != null) {
            try {
                isUploading = true
                
                when (sharedContent) {
                    is SharedContent.Image -> {
                        if (sharedContent.uri != null) {
                            uploadStatus = "Extracting text from image..."
                            
                            // Extract text from image
                            val textContent = activity.extractTextFromImage(sharedContent.uri)
                            
                            if (textContent.isNotEmpty() && !textContent.startsWith("Error")) {
                                extractedText = textContent
                                uploadStatus = "Text extracted. Analyzing with Gemini..."
                                
                                // Analyze with Gemini
                                val analysis = activity.analyzeTextWithGemini(textContent)
                                textAnalysis = analysis
                                uploadStatus = "Analysis complete!"
                            } else {
                                uploadStatus = "No text found in the image or extraction failed: $textContent"
                            }
                        }
                    }
                    
                    is SharedContent.Zip -> {
                        if (sharedContent.uri != null) {
                            // Instead of processing here, redirect to MultiImageAnalyzerScreen
                            uploadStatus = "Opening ZIP file browser..."
                            
                            // Launch MultiImageAnalyzerScreen activity with the ZIP uri
                            val intent = Intent(activity, MainActivity::class.java).apply {
                                action = Intent.ACTION_SEND
                                putExtra("REDIRECT_TO_ANALYZER", true)
                                putExtra(Intent.EXTRA_STREAM, sharedContent.uri)
                                type = "application/zip"
                            }
                            activity.startActivity(intent)
                            
                            // Finish this activity as we're redirecting
                            activity.finish()
                            return@LaunchedEffect
                        }
                    }
                    
                    is SharedContent.Pdf -> {
                        if (sharedContent.uri != null) {
                            uploadStatus = "Processing PDF file..."
                            // For now, just add a placeholder message for PDF processing
                            // In a real implementation, you would use a PDF parsing library
                            extractedText = "PDF processing is limited without a dedicated parser."
                            uploadStatus = "PDF processed with limited capabilities."
                        }
                    }
                    
                    is SharedContent.Text -> {
                        sharedContent.content?.let { content ->
                            uploadStatus = "Processing text..."
                            extractedText = content
                            
                            // Analyze with Gemini
                            val analysis = activity.analyzeTextWithGemini(content)
                            textAnalysis = analysis
                            uploadStatus = "Analysis complete!"
                        }
                    }
                    
                    is SharedContent.File -> {
                        if (sharedContent.uri != null) {
                            val mimeType = sharedContent.mimeType ?: "application/octet-stream"
                            uploadStatus = "Processing file of type: $mimeType"
                            
                            // Basic handling based on mime type
                            when {
                                mimeType.startsWith("text/") -> {
                                    // Handle text files
                                    val text = activity.readTextFile(sharedContent.uri)
                                    if (text != null) {
                                        extractedText = text
                                        uploadStatus = "Text extracted. Analyzing with Gemini..."
                                        
                                        // Analyze with Gemini
                                        val analysis = activity.analyzeTextWithGemini(text)
                                        textAnalysis = analysis
                                        uploadStatus = "Analysis complete!"
                                    }
                                }
                                else -> {
                                    uploadStatus = "Unsupported file type: $mimeType"
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                uploadStatus = "Error processing content: ${e.message}"
                Log.e("ShareReceiveActivity", "Error processing shared content", e)
            } finally {
                isUploading = false
            }
        }
    }

    // Show loading indicator while processing
    if (isUploading) {
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(16.dp)
            ) {
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(8.dp))
                Text(text = uploadStatus)
            }
        }
    }

    // Show sync dialog if requested
    if (showSyncDialog) {
        AlertDialog(
            onDismissRequest = { showSyncDialog = false },
            title = { Text("Sync with PC") },
            text = {
                Column {
                    if (syncInfo != null) {
                        Text(
                            text = syncInfo!!,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        
                        OutlinedButton(
                            onClick = {
                                val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                                val clip = android.content.ClipData.newPlainText("Sync Info", syncInfo)
                                clipboard.setPrimaryClip(clip)
                                Toast.makeText(context, "Sync info copied to clipboard", Toast.LENGTH_SHORT).show()
                            },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Copy to Clipboard")
                        }
                    } else {
                        CircularProgressIndicator()
                    }
                }
            },
            confirmButton = {
                Button(onClick = { showSyncDialog = false }) {
                    Text("Close")
                }
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // User ID Card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = "User",
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(16.dp))
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "User ID:",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = userId,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    if (isAnonymous) {
                        Text(
                            text = "(Anonymous User)",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                
                if (currentUser == null) {
                    Button(
                        onClick = {
                            isSigningIn = true
                            coroutineScope.launch {
                                try {
                                    val result = auth.signInAnonymously().await()
                                    currentUser = result.user
                                    userId = currentUser?.uid ?: "Not signed in"
                                    isAnonymous = currentUser?.isAnonymous ?: false
                                    Log.d("ShareReceiveActivity", "Anonymous sign-in successful: $userId")
                                    
                                    // Create user document in Firestore
                                    if (currentUser != null) {
                                        val db = FirebaseFirestore.getInstance()
                                        
                                        // Check if user document already exists
                                        try {
                                            val document = db.collection("users").document(currentUser!!.uid)
                                                .get().await()
                                                
                                            if (!document.exists()) {
                                                // Create a new user document for the anonymous user
                                                val userData = hashMapOf(
                                                    "username" to "Anonymous User",
                                                    "isAnonymous" to true,
                                                    "createdAt" to System.currentTimeMillis(),
                                                    "lastLogin" to System.currentTimeMillis()
                                                )
                                                
                                                db.collection("users").document(currentUser!!.uid)
                                                    .set(userData).await()
                                                Log.d("ShareReceiveActivity", "User document created successfully")
                                            } else {
                                                Log.d("ShareReceiveActivity", "User document already exists")
                                                // Update last login time
                                                db.collection("users").document(currentUser!!.uid)
                                                    .update("lastLogin", System.currentTimeMillis()).await()
                                            }
                                        } catch (e: Exception) {
                                            Log.e("ShareReceiveActivity", "Error managing user document", e)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("ShareReceiveActivity", "Anonymous sign-in failed", e)
                                } finally {
                                    isSigningIn = false
                                }
                            }
                        },
                        enabled = !isSigningIn
                    ) {
                        if (isSigningIn) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("Sign In Anonymously")
                        }
                    }
                }
            }
        }
        
        // Action buttons for image capture/selection
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Camera button
            Button(
                onClick = {
                    activity?.let {
                        // Check for camera permission
                        when {
                            ContextCompat.checkSelfPermission(
                                it,
                                Manifest.permission.CAMERA
                            ) == PackageManager.PERMISSION_GRANTED -> {
                                // Permission already granted, launch camera
                                val imageFile = File(
                                    it.cacheDir,
                                    "camera_photo_${System.currentTimeMillis()}.jpg"
                                )
                                it.tempImageUri = FileProvider.getUriForFile(
                                    it,
                                    "${it.packageName}.fileprovider",
                                    imageFile
                                )
                                activity.cameraLauncher.launch(it.tempImageUri)
                            }
                            else -> {
                                // Request permission
                                activity.permissionLauncher.launch(Manifest.permission.CAMERA)
                            }
                        }
                    }
                },
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    Icons.Default.Camera,
                    contentDescription = "Take Photo",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("Take Photo")
            }
            
            // Add QR Code Scanner button
            Button(
                onClick = {
                    activity?.let {
                        // Launch QR code scanner with optimized settings
                        val scanOptions = ScanOptions().apply {
                            setPrompt("Align QR code within the frame.")
                            setBeepEnabled(true)
                            setOrientationLocked(false)
                            setDesiredBarcodeFormats(ScanOptions.QR_CODE) // Focus only on QR codes
                            setBarcodeImageEnabled(true)
                            setTimeout(30000) // 30 second timeout
                            setCameraId(0) // Use back camera
                        }
                        
                        it.qrCodeLauncher.launch(scanOptions)
                    }
                },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.tertiary
                )
            ) {
                Icon(
                    Icons.Default.QrCodeScanner,
                    contentDescription = "Scan QR Code",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("Connect to PC")
            }
        }

        // If a PC is connected, show the connection info
        if (isPcConnected) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.tertiaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Connected to PC",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "Server: ${activity?.pcServerUrl}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // PC connection status
                    pcConnectionStatus?.let { status ->
                        Text(
                            text = status,
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (status.contains("Error") || status.contains("Failed")) 
                                    MaterialTheme.colorScheme.error
                                else 
                                    MaterialTheme.colorScheme.onTertiaryContainer
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    
                    // Add Send to PC button when text is available
                    if (extractedText != null) {
                        Button(
                            onClick = {
                                activity?.let { act ->
                                    act.pcServerUrl?.let { serverUrl ->
                                        isSendingToPC = true
                                        pcConnectionStatus = "Sending to PC..."
                                        
                                        coroutineScope.launch {
                                            val result = act.sendTextToPC(extractedText!!, serverUrl)
                                            isSendingToPC = false
                                            pcConnectionStatus = result.fold(
                                                onSuccess = { it },
                                                onFailure = { "Error: ${it.message}" }
                                            )
                                        }
                                    }
                                }
                            },
                            enabled = !isSendingToPC,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.tertiary
                            )
                        ) {
                            if (isSendingToPC) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp,
                                    color = MaterialTheme.colorScheme.onTertiary
                                )
                            } else {
                                Icon(Icons.Default.Send, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Send to PC")
                            }
                        }
                    }
                }
            }
        }

        // Modify the action buttons - keep both PC sync and direct sharing options
        if (extractedText != null) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // First row with Copy and Share buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Copy button
                    Button(
                        onClick = {
                            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val clip = ClipData.newPlainText("Extracted Text", extractedText)
                            clipboardManager.setPrimaryClip(clip)
                            Toast.makeText(context, "Text copied to clipboard", Toast.LENGTH_SHORT).show()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.ContentCopy, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Copy")
                    }
                    
                    // Direct Share button
                    Button(
                        onClick = {
                            TextSharing.shareText(extractedText!!, context)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Icon(Icons.Default.Share, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Share")
                    }
                }

                // Arabic explanation button in a new row
                var showArabicDialog by remember { mutableStateOf(false) }
                var arabicAnalysis by remember { mutableStateOf<String?>(null) }
                
                Button(
                    onClick = {
                        // Analyze with Gemini in Arabic
                        coroutineScope.launch {
                            try {
                                uploadStatus = "Generating Arabic explanation..."
                                arabicAnalysis = activity?.analyzeTextWithGeminiInArabic(extractedText!!)
                                showArabicDialog = true
                                uploadStatus = "Arabic explanation complete!"
                            } catch (e: Exception) {
                                uploadStatus = "Error generating Arabic explanation: ${e.message}"
                            }
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Icon(Icons.Default.TextSnippet, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("شرح الكلام بالعربي")
                }

                // Arabic explanation dialog
                if (showArabicDialog) {
                    AlertDialog(
                        onDismissRequest = { showArabicDialog = false },
                        title = { Text("شرح الكلام بالعربي") },
                        text = {
                            Column {
                                if (arabicAnalysis != null) {
                                    Text(
                                        text = arabicAnalysis!!,
                                        modifier = Modifier
                                            .verticalScroll(rememberScrollState())
                                            .padding(vertical = 8.dp)
                                    )
                                } else {
                                    CircularProgressIndicator(
                                        modifier = Modifier.align(Alignment.CenterHorizontally)
                                    )
                                }
                            }
                        },
                        confirmButton = {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Button(
                                    onClick = {
                                        arabicAnalysis?.let {
                                            TextSharing.shareText(it, context, "شرح الكلام بالعربي")
                                        }
                                    },
                                    enabled = arabicAnalysis != null
                                ) {
                                    Icon(Icons.Default.Share, contentDescription = null)
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("Share")
                                }
                                Button(
                                    onClick = { showArabicDialog = false }
                                ) {
                                    Text("Close")
                                }
                            }
                        }
                    )
                }
            }
        }

        // Show captured image and extracted text
        activity?.tempImageUri?.let { uri ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Show captured image
                    Image(
                        painter = rememberAsyncImagePainter(uri),
                        contentDescription = "Captured Image",
                        modifier = Modifier
                            .size(200.dp)
                            .padding(bottom = 16.dp)
                    )
                    
                    // Show extracted text if available
                    extractedText?.let { text ->
                        TextContentBox(
                            title = "Extracted Text (${text.length} characters):",
                            content = text,
                            maxHeight = 250.dp
                        )
                    }
                    
                    // Show analysis if available
                    textAnalysis?.let { analysis ->
                        TextContentBox(
                            title = "Analysis:",
                            content = analysis,
                            maxHeight = 350.dp
                        )
                    }
                }
            }
        }

        when (sharedContent) {
            is SharedContent.Image -> {
                if (sharedContent.uri != null) {
                    Image(
                        painter = rememberAsyncImagePainter(sharedContent.uri),
                        contentDescription = "Shared Image",
                        modifier = Modifier
                            .size(200.dp)
                            .padding(16.dp)
                    )
                    Text(
                        text = "Image file",
                        modifier = Modifier.padding(8.dp)
                    )
                    
                    // Show extracted text if available
                    extractedText?.let { text ->
                        TextContentBox(
                            title = "Extracted Text (${text.length} characters):",
                            content = text,
                            maxHeight = 250.dp
                        )
                    }
                    
                    // Show analysis if available
                    textAnalysis?.let { analysis ->
                        TextContentBox(
                            title = "Gemini Analysis:",
                            content = analysis,
                            maxHeight = 350.dp
                        )
                    }
                } else {
                    Text("Invalid image URI")
                }
            }
            is SharedContent.Text -> {
                Icon(
                    imageVector = Icons.Default.TextSnippet,
                    contentDescription = "Text",
                    modifier = Modifier.size(100.dp)
                )
                Text(
                    text = sharedContent.content ?: "No text content",
                    modifier = Modifier.padding(16.dp)
                )
                
                // Show analysis if available
                textAnalysis?.let { analysis ->
                    TextContentBox(
                        title = "Gemini Analysis:",
                        content = analysis,
                        maxHeight = 350.dp
                    )
                }
            }
            is SharedContent.Pdf -> {
                Icon(
                    imageVector = Icons.Default.PictureAsPdf,
                    contentDescription = "PDF",
                    modifier = Modifier.size(100.dp)
                )
                Text(
                    text = "PDF Document",
                    modifier = Modifier.padding(16.dp)
                )
                sharedContent.uri?.let { uri ->
                    Text(
                        text = "File: ${getFileName(context, uri)}",
                        modifier = Modifier.padding(8.dp)
                    )
                }
                
                // Show extracted text if available
                extractedText?.let { text ->
                    TextContentBox(
                        title = "Extracted Text (${text.length} characters):",
                        content = text,
                        maxHeight = 250.dp
                    )
                }
                
                // Show analysis if available
                textAnalysis?.let { analysis ->
                    TextContentBox(
                        title = "Gemini Analysis:",
                        content = analysis,
                        maxHeight = 350.dp
                    )
                }
            }
            is SharedContent.Zip -> {
                Icon(
                    imageVector = Icons.Default.Archive,
                    contentDescription = "ZIP Archive",
                    modifier = Modifier.size(100.dp)
                )
                Text(
                    text = "ZIP Archive",
                    modifier = Modifier.padding(16.dp)
                )
                sharedContent.uri?.let { uri ->
                    Text(
                        text = "File: ${getFileName(context, uri)}",
                        modifier = Modifier.padding(8.dp)
                    )
                }
                
                // Show ZIP extraction progress
                if (showZipProgress) {
                    FileExtractionProgressBox(
                        processedFiles = zipProcessedFiles,
                        currentFile = zipCurrentFile,
                        extractionStatus = uploadStatus
                    )
                }
                
                // Show extracted text if available
                extractedText?.let { text ->
                    TextContentBox(
                        title = "Extracted Text (${text.length} characters):",
                        content = text,
                        maxHeight = 250.dp
                    )
                }
                
                // Show analysis if available
                textAnalysis?.let { analysis ->
                    TextContentBox(
                        title = "Gemini Analysis:",
                        content = analysis,
                        maxHeight = 350.dp
                    )
                }
            }
            is SharedContent.File -> {
                Icon(
                    imageVector = Icons.Default.Description,
                    contentDescription = "File",
                    modifier = Modifier.size(100.dp)
                )
                Text(
                    text = "File Type: ${sharedContent.mimeType ?: "Unknown"}",
                    modifier = Modifier.padding(16.dp)
                )
                sharedContent.uri?.let { uri ->
                    Text(
                        text = "File: ${getFileName(context, uri)}",
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
            null -> {
                Text(
                    text = "No content shared or unsupported content type",
                    modifier = Modifier.padding(16.dp)
                )
            }
        }

        Button(
            onClick = {
                // Return to main activity
                val intent = Intent(context, MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                context.startActivity(intent)
            },
            modifier = Modifier.padding(16.dp)
        ) {
            Text("Return to App")
        }
    }
}

private fun uploadFile(uri: Uri?, folder: String, extension: String, callback: (Boolean, String) -> Unit) {
    if (uri == null) {
        callback(false, "Invalid file URI")
        return
    }
    
    val storage = FirebaseStorage.getInstance()
    val auth = FirebaseAuth.getInstance()
    val userId = auth.currentUser?.uid ?: "anonymous"
    
    val fileRef = storage.reference
        .child(folder)
        .child(userId)
        .child("${UUID.randomUUID()}.$extension")

    fileRef.putFile(uri)
        .addOnSuccessListener {
            callback(true, "File uploaded successfully!")
        }
        .addOnFailureListener {
            callback(false, "Failed to upload file: ${it.message}")
        }
}

private fun getFileName(context: Context, uri: Uri): String {
    val contentResolver = context.contentResolver
    val cursor = contentResolver.query(uri, null, null, null, null)
    
    return cursor?.use {
        val nameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
        it.moveToFirst()
        if (nameIndex != -1 && !it.isNull(nameIndex)) {
            it.getString(nameIndex)
        } else {
            "Unknown file"
        }
    } ?: "Unknown file"
}

private fun getFileExtension(context: Context, uri: Uri?): String {
    if (uri == null) return "bin"
    
    val fileName = getFileName(context, uri)
    return fileName.substringAfterLast('.', "bin")
}

@Composable
fun FileExtractionProgressBox(
    processedFiles: List<String>,
    currentFile: String?,
    extractionStatus: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "ZIP Extraction Progress",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // Show current status
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Text(
                    text = "Status: ",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = extractionStatus,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            // Show current file being processed
            if (currentFile != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 4.dp)
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(16.dp)
                            .padding(end = 8.dp),
                        strokeWidth = 2.dp
                    )
                    Text(
                        text = "Processing: $currentFile",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            // Show processed files
            if (processedFiles.isNotEmpty()) {
                Text(
                    text = "Processed Files (${processedFiles.size}):",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                )
                
                val scrollState = rememberScrollState()
                
                Column(
                    modifier = Modifier
                        .heightIn(max = 150.dp)
                        .verticalScroll(scrollState)
                ) {
                    processedFiles.forEachIndexed { index, file ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(vertical = 2.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Processed",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "${index + 1}. $file",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        }
    }
}

// Companion object for static sharing functionality
object TextSharing {
    fun shareText(text: String, context: Context, subject: String = "Extracted Text") {
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, text)
            putExtra(Intent.EXTRA_SUBJECT, subject)
        }
        
        context.startActivity(Intent.createChooser(shareIntent, "Share Text"))
    }
}

// Add a reusable composable for displaying text content
@Composable
fun TextContentBox(title: String, content: String, maxHeight: Dp = Dp.Unspecified) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        if (maxHeight != Dp.Unspecified) {
                            Modifier.heightIn(min = 100.dp, max = maxHeight)
                        } else {
                            Modifier.wrapContentHeight()
                        }
                    )
            ) {
                val scrollState = rememberScrollState()
                
                Text(
                    text = content,
                    modifier = Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                        .verticalScroll(scrollState),
                    style = MaterialTheme.typography.bodyMedium
                )
                
                // Add scroll indicators
                if (scrollState.canScrollForward || scrollState.canScrollBackward) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(4.dp)
                            .size(24.dp)
                            .background(
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "Scrollable content",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
} 