package io.ammar.medical.viewmodel

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * ViewModel for the Medical Analyzer screen
 */
class MedicalAnalyzerViewModel : ViewModel() {
    // Medical files state
    private val _medicalFiles = MutableStateFlow<List<String>>(emptyList())
    val medicalFiles: StateFlow<List<String>> = _medicalFiles.asStateFlow()
    
    // Holistic analysis state
    private val _holisticAnalysis = MutableStateFlow<String?>(null)
    val holisticAnalysis: StateFlow<String?> = _holisticAnalysis.asStateFlow()
    
    // Analyzing state
    private val _isAnalyzing = MutableStateFlow(false)
    val isAnalyzing: StateFlow<Boolean> = _isAnalyzing.asStateFlow()
} 