package io.ammar.medical.auth

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import android.util.Patterns
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.security.MessageDigest
import java.security.SecureRandom
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec

class AuthService(private val context: Context) {
    private val TAG = "AuthService"
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()

    private val securePreferences: SharedPreferences = EncryptedSharedPreferences.create(
        context,
        "secure_user_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn

    init {
        // Listen for Firebase Auth state changes
        auth.addAuthStateListener { firebaseAuth ->
            val user = firebaseAuth.currentUser
            if (user != null) {
                Log.d(TAG, "Firebase Auth state changed - User logged in: ${user.uid}")
            } else {
                Log.d(TAG, "Firebase Auth state changed - User logged out")
                _currentUser.value = null
                _isLoggedIn.value = false
            }
        }
    }

    private suspend fun updateUserState(firebaseUser: FirebaseUser?) {
        // First check if we have a bypass user in SharedPreferences
        val isBypassUser = securePreferences.getBoolean("is_bypass_user", false)
        
        if (isBypassUser) {
            Log.d(TAG, "Found bypass user in preferences, restoring state")
            try {
                val userId = securePreferences.getString("bypass_user_id", null)
                val username = securePreferences.getString("bypass_username", null)
                val email = securePreferences.getString("bypass_email", null)
                val createdAt = securePreferences.getLong("bypass_created_at", 0)
                val lastLogin = securePreferences.getLong("bypass_last_login", 0)
                
                if (userId != null && username != null && email != null) {
                    val user = User(
                        id = userId,
                        username = username,
                        email = email,
                        hashedPassword = "",
                        salt = "",
                        createdAt = createdAt,
                        lastLogin = lastLogin
                    )
                    _currentUser.value = user
                    _isLoggedIn.value = true
                    Log.d(TAG, "Bypass user state restored - username: ${user.username}")
                    return
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error restoring bypass user from preferences", e)
                // Continue with normal flow if restoration fails
            }
        }
        
        if (firebaseUser == null) {
            // Only clear state if we're not a bypass user
            if (!isBypassUser) {
                Log.d(TAG, "Updating user state - No Firebase user")
                _currentUser.value = null
                _isLoggedIn.value = false
            } else {
                Log.d(TAG, "Skipping state reset for bypass user even though Firebase user is null")
            }
            return
        }

        try {
            Log.d(TAG, "Fetching user data from Firestore for: ${firebaseUser.uid}")
            // Get additional user data from Firestore using the safe method
            val userDocRef = firestore.collection("users").document(firebaseUser.uid)
            val (userDoc, isFromCache) = io.ammar.medical.utils.FirebaseUtils.safeGetDocument(userDocRef)
            
            if (userDoc != null && userDoc.exists()) {
                Log.d(TAG, "User document exists in Firestore ${if (isFromCache) "(from cache)" else "(from server)"}")
                val user = User(
                    id = firebaseUser.uid,
                    username = userDoc.getString("username") ?: "",
                    email = firebaseUser.email ?: "",
                    hashedPassword = "", // We don't store passwords
                    salt = "", // We don't need salt with Firebase Auth
                    createdAt = userDoc.getLong("createdAt") ?: System.currentTimeMillis(),
                    lastLogin = userDoc.getLong("lastLogin")
                )
                _currentUser.value = user
                _isLoggedIn.value = true
                Log.d(TAG, "User state updated - isLoggedIn: true, username: ${user.username}")
            } else if (isFromCache) {
                // If we only have cache data but it doesn't exist, we might be offline with a new user
                // Create a basic user object with available data
                Log.d(TAG, "No Firestore document found in cache, creating basic user from Firebase Auth")
                val user = User(
                    id = firebaseUser.uid,
                    username = firebaseUser.displayName ?: firebaseUser.email?.substringBefore("@") ?: "",
                    email = firebaseUser.email ?: "",
                    hashedPassword = "",
                    salt = "",
                    createdAt = System.currentTimeMillis()
                )
                _currentUser.value = user
                _isLoggedIn.value = true
                Log.d(TAG, "Created basic user from Firebase Auth - username: ${user.username}")
            } else {
                Log.d(TAG, "No Firestore document found for user")
                _currentUser.value = null
                _isLoggedIn.value = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user state", e)
            // Don't clear user state if we're offline - keep last known state
            if (_currentUser.value == null) {
                // If we don't have a current user, try to create a basic one from Firebase
                try {
                    val user = User(
                        id = firebaseUser.uid,
                        username = firebaseUser.displayName ?: firebaseUser.email?.substringBefore("@") ?: "",
                        email = firebaseUser.email ?: "",
                        hashedPassword = "",
                        salt = "",
                        createdAt = System.currentTimeMillis()
                    )
                    _currentUser.value = user
                    _isLoggedIn.value = true
                    Log.d(TAG, "Created fallback user from Firebase Auth after error - username: ${user.username}")
                } catch (innerEx: Exception) {
                    Log.e(TAG, "Failed to create fallback user", innerEx)
                    _currentUser.value = null
                    _isLoggedIn.value = false
                }
            }
        }
    }

    suspend fun refreshUserState() {
        Log.d(TAG, "Refreshing user state")
        
        // Check if we have a bypass user first
        val isBypassUser = securePreferences.getBoolean("is_bypass_user", false)
        if (isBypassUser) {
            Log.d(TAG, "Bypass user detected, skipping Firebase refresh")
            try {
                // Restore bypass user from preferences
                val userId = securePreferences.getString("bypass_user_id", null)
                val username = securePreferences.getString("bypass_username", null)
                val email = securePreferences.getString("bypass_email", null)
                val createdAt = securePreferences.getLong("bypass_created_at", 0)
                val lastLogin = securePreferences.getLong("bypass_last_login", 0)
                
                if (userId != null && username != null && email != null) {
                    val user = User(
                        id = userId,
                        username = username,
                        email = email,
                        hashedPassword = "",
                        salt = "",
                        createdAt = createdAt,
                        lastLogin = lastLogin
                    )
                    
                    // Update state flows directly
                    _currentUser.value = user
                    _isLoggedIn.value = true
                    
                    Log.d(TAG, "Bypass user state restored directly - username: ${user.username}")
                    return
                } else {
                    Log.w(TAG, "Bypass user flag set but user data missing from preferences")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error restoring bypass user from preferences", e)
            }
        }
        
        // Only proceed with Firebase user if not a bypass user or if bypass user restoration failed
        val currentFirebaseUser = auth.currentUser
        updateUserState(currentFirebaseUser)
    }

    suspend fun register(credentials: RegisterCredentials): AuthResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting registration for: ${credentials.email}")
            // Validate input
            if (!isValidEmail(credentials.email)) {
                return@withContext AuthResult(false, errorMessage = "Invalid email format")
            }
            if (credentials.password != credentials.confirmPassword) {
                return@withContext AuthResult(false, errorMessage = "Passwords do not match")
            }
            if (credentials.password.length < 8) {
                return@withContext AuthResult(false, errorMessage = "Password must be at least 8 characters")
            }

            // Create user with Firebase Auth
            val authResult = auth.createUserWithEmailAndPassword(credentials.email, credentials.password).await()
            val firebaseUser = authResult.user ?: throw Exception("Failed to create user")
            Log.d(TAG, "Firebase user created: ${firebaseUser.uid}")

            // Create user document in Firestore
            val user = User(
                id = firebaseUser.uid,
                username = credentials.username,
                email = credentials.email,
                hashedPassword = "", // We don't store passwords with Firebase
                salt = "", // We don't need salt with Firebase
                createdAt = System.currentTimeMillis()
            )

            // Save additional user data to Firestore
            firestore.collection("users").document(firebaseUser.uid)
                .set(mapOf(
                    "username" to user.username,
                    "createdAt" to user.createdAt,
                    "lastLogin" to System.currentTimeMillis()
                )).await()
            Log.d(TAG, "User data saved to Firestore")

            updateUserState(firebaseUser)
            return@withContext AuthResult(true, user)
        } catch (e: Exception) {
            Log.e(TAG, "Registration failed", e)
            return@withContext AuthResult(false, errorMessage = e.message ?: "Registration failed")
        }
    }

    suspend fun login(credentials: LoginCredentials): AuthResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Attempting login for: ${credentials.username}")
            
            // Check network connectivity first
            val context = <EMAIL>
            val isNetworkAvailable = io.ammar.medical.utils.NetworkUtils.isNetworkAvailable(context)
            
            if (!isNetworkAvailable) {
                Log.w(TAG, "Network unavailable during login attempt")
                return@withContext AuthResult(
                    false, 
                    errorMessage = "Network unavailable. Please check your internet connection and try again."
                )
            }
            
            // Sign in with Firebase Auth
            val authResult = auth.signInWithEmailAndPassword(credentials.username, credentials.password).await()
            val firebaseUser = authResult.user ?: throw Exception("Failed to sign in")
            Log.d(TAG, "Firebase login successful: ${firebaseUser.uid}")

            try {
                // Update last login time in Firestore
                firestore.collection("users").document(firebaseUser.uid)
                    .update("lastLogin", System.currentTimeMillis())
                    .await()
                Log.d(TAG, "Last login time updated in Firestore")
            } catch (e: Exception) {
                // Don't fail the login if we can't update the last login time
                Log.w(TAG, "Failed to update last login time", e)
            }

            updateUserState(firebaseUser)
            return@withContext AuthResult(true, _currentUser.value)
        } catch (e: Exception) {
            Log.e(TAG, "Login failed", e)
            val errorMessage = when {
                e.message?.contains("network", ignoreCase = true) == true -> 
                    "Network error. Please check your internet connection."
                e.message?.contains("password", ignoreCase = true) == true -> 
                    "Invalid password. Please try again."
                e.message?.contains("user", ignoreCase = true) == true -> 
                    "User not found. Please check your email or register."
                e.message?.contains("too many", ignoreCase = true) == true -> 
                    "Too many failed attempts. Please try again later."
                else -> "Invalid email or password"
            }
            return@withContext AuthResult(false, errorMessage = errorMessage)
        }
    }

    fun logout() {
        Log.d(TAG, "Logging out user")
        auth.signOut()
        
        // Clear bypass user data
        securePreferences.edit().apply {
            remove("bypass_user_id")
            remove("bypass_username")
            remove("bypass_email")
            remove("bypass_created_at")
            remove("bypass_last_login")
            remove("is_bypass_user")
            apply()
        }
        Log.d(TAG, "Cleared bypass user data from preferences")
        
        _currentUser.value = null
        _isLoggedIn.value = false
        Log.d(TAG, "Logout complete")
    }

    // For testing purposes only
    fun bypassAuth(user: User) {
        Log.d(TAG, "Bypassing auth with test user")
        
        // Store the user in SharedPreferences for persistence
        try {
            securePreferences.edit().apply {
                putString("bypass_user_id", user.id)
                putString("bypass_username", user.username)
                putString("bypass_email", user.email)
                putLong("bypass_created_at", user.createdAt)
                putLong("bypass_last_login", System.currentTimeMillis())
                putBoolean("is_bypass_user", true)
                apply()
            }
            Log.d(TAG, "Bypass user data saved to secure preferences")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save bypass user to preferences", e)
        }
        
        // Update the state flows
        _currentUser.value = user
        _isLoggedIn.value = true
    }

    fun setupBiometric(activity: FragmentActivity, onSuccess: () -> Unit) {
        val biometricManager = BiometricManager.from(context)
        
        when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
            BiometricManager.BIOMETRIC_SUCCESS -> {
                val executor = ContextCompat.getMainExecutor(context)
                val biometricPrompt = BiometricPrompt(activity, executor,
                    object : BiometricPrompt.AuthenticationCallback() {
                        override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                            super.onAuthenticationSucceeded(result)
                            securePreferences.edit().putBoolean("biometric_enabled", true).apply()
                            onSuccess()
                        }
                    })

                val promptInfo = BiometricPrompt.PromptInfo.Builder()
                    .setTitle("Enable Biometric Login")
                    .setSubtitle("Log in with your biometric credential")
                    .setNegativeButtonText("Cancel")
                    .build()

                biometricPrompt.authenticate(promptInfo)
            }
            else -> {
                // Biometric authentication not available
            }
        }
    }

    private fun generateSalt(): ByteArray {
        val random = SecureRandom()
        val salt = ByteArray(16)
        random.nextBytes(salt)
        return salt
    }

    private fun hashPassword(password: String, salt: ByteArray): String {
        val spec = PBEKeySpec(password.toCharArray(), salt, 65536, 128)
        val factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1")
        val hash = factory.generateSecret(spec).encoded
        return Base64.encodeToString(hash, Base64.DEFAULT)
    }

    private fun generateAuthToken(): String {
        val random = SecureRandom()
        val bytes = ByteArray(32)
        random.nextBytes(bytes)
        return Base64.encodeToString(bytes, Base64.DEFAULT)
    }

    private fun saveAuthToken(username: String, token: String) {
        securePreferences.edit().apply {
            putString("username", username)
            putString("auth_token", token)
            apply()
        }
    }

    private fun isValidEmail(email: String): Boolean {
        return Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    /**
     * Checks if the current user is a bypass user
     */
    fun isCurrentUserBypass(): Boolean {
        return securePreferences.getBoolean("is_bypass_user", false)
    }

    /**
     * Signs in anonymously with Firebase Auth
     * Returns an AuthResult with the anonymous user
     */
    suspend fun signInAnonymously(): AuthResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Attempting anonymous sign-in")
            
            // Check if we already have an anonymous user
            if (auth.currentUser != null && auth.currentUser!!.isAnonymous) {
                Log.d(TAG, "Already signed in anonymously: ${auth.currentUser!!.uid}")
                
                // Update user state
                updateUserState(auth.currentUser)
                
                // Ensure user document exists in Firestore
                ensureAnonymousUserDocumentExists(auth.currentUser!!)
                
                return@withContext AuthResult(
                    success = true,
                    user = _currentUser.value,
                    errorMessage = null
                )
            }
            
            // Sign in anonymously
            val authResult = auth.signInAnonymously().await()
            val firebaseUser = authResult.user
            
            if (firebaseUser != null) {
                Log.d(TAG, "Anonymous sign-in successful: ${firebaseUser.uid}")
                
                // Create a user object for the anonymous user
                val user = User(
                    id = firebaseUser.uid,
                    username = "Anonymous User",
                    email = "",
                    hashedPassword = "",
                    salt = "",
                    createdAt = System.currentTimeMillis(),
                    lastLogin = System.currentTimeMillis()
                )
                
                // Create user document in Firestore
                ensureAnonymousUserDocumentExists(firebaseUser)
                
                // Update user state
                _currentUser.value = user
                _isLoggedIn.value = true
                
                return@withContext AuthResult(
                    success = true,
                    user = user,
                    errorMessage = null
                )
            } else {
                Log.e(TAG, "Anonymous sign-in failed: No user returned")
                return@withContext AuthResult(
                    success = false,
                    user = null,
                    errorMessage = "Anonymous sign-in failed"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Anonymous sign-in failed", e)
            return@withContext AuthResult(
                success = false,
                user = null,
                errorMessage = "Anonymous sign-in failed: ${e.message}"
            )
        }
    }
    
    /**
     * Ensures that an anonymous user has a document in Firestore
     */
    private suspend fun ensureAnonymousUserDocumentExists(firebaseUser: FirebaseUser) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Ensuring user document exists for anonymous user: ${firebaseUser.uid}")
            
            // Check if user document already exists
            val userDocRef = firestore.collection("users").document(firebaseUser.uid)
            val userDoc = try {
                userDocRef.get().await()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking if user document exists", e)
                null
            }
            
            if (userDoc == null || !userDoc.exists()) {
                // Create a new user document for the anonymous user
                val userData = hashMapOf(
                    "username" to "Anonymous User",
                    "isAnonymous" to true,
                    "createdAt" to System.currentTimeMillis(),
                    "lastLogin" to System.currentTimeMillis()
                )
                
                try {
                    userDocRef.set(userData).await()
                    Log.d(TAG, "User document created successfully for anonymous user")
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating user document for anonymous user", e)
                }
            } else {
                Log.d(TAG, "User document already exists for this anonymous user")
                // Update last login time
                try {
                    userDocRef.update("lastLogin", System.currentTimeMillis()).await()
                } catch (e: Exception) {
                    Log.e(TAG, "Error updating last login time", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in ensureAnonymousUserDocumentExists", e)
        }
    }
} 