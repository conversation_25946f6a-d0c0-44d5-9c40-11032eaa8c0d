package com.ammar.medical.presentation.ui.screens.camera

import android.net.Uri
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.ammar.medical.camera.CameraManager
import com.ammar.medical.camera.CameraState
import com.ammar.medical.camera.FlashMode
import com.ammar.medical.presentation.ui.components.CommonTopAppBar
import com.ammar.medical.presentation.ui.theme.AmmarTheme
import dagger.hilt.android.AndroidEntryPoint

@Composable
fun CameraScreen(
    onNavigateBack: () -> Unit,
    onImageCaptured: (Uri) -> Unit,
    cameraManager: CameraManager
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    var previewView by remember { mutableStateOf<PreviewView?>(null) }
    var flashMode by remember { mutableStateOf(FlashMode.OFF) }
    val cameraState by cameraManager.cameraState.collectAsState()

    LaunchedEffect(previewView) {
        previewView?.let { preview ->
            cameraManager.startCamera(
                lifecycleOwner = lifecycleOwner,
                preview = preview,
                onImageCaptured = onImageCaptured,
                onError = { /* Handle error */ }
            )
        }
    }

    AmmarTheme {
        Scaffold(
            topBar = {
                CommonTopAppBar(
                    title = "Scan Document",
                    navigationIcon = Icons.Default.ArrowBack,
                    onNavigationClick = onNavigateBack
                )
            }
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // Camera Preview
                AndroidView(
                    factory = { context ->
                        PreviewView(context).apply {
                            previewView = this
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )

                // Camera Controls
                Column(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Flash Control
                    IconButton(
                        onClick = {
                            flashMode = when (flashMode) {
                                FlashMode.OFF -> FlashMode.ON
                                FlashMode.ON -> FlashMode.OFF
                                FlashMode.AUTO -> FlashMode.OFF
                            }
                            cameraManager.setFlashMode(flashMode)
                        }
                    ) {
                        Icon(
                            imageVector = when (flashMode) {
                                FlashMode.ON -> Icons.Default.FlashOn
                                FlashMode.OFF -> Icons.Default.FlashOff
                                FlashMode.AUTO -> Icons.Default.FlashAuto
                            },
                            contentDescription = "Flash",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }

                    // Capture Button
                    FloatingActionButton(
                        onClick = {
                            cameraManager.captureImage(
                                onImageCaptured = onImageCaptured,
                                onError = { /* Handle error */ }
                            )
                        },
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    ) {
                        Icon(
                            imageVector = Icons.Default.Lens,
                            contentDescription = "Capture"
                        )
                    }
                }

                // Camera State Overlay
                when (cameraState) {
                    is CameraState.Initializing -> {
                        CircularProgressIndicator(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .size(48.dp)
                        )
                    }
                    is CameraState.Error -> {
                        Text(
                            text = "Camera Error: ${(cameraState as CameraState.Error).exception.message}",
                            color = MaterialTheme.colorScheme.error,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(16.dp)
                        )
                    }
                    else -> {}
                }
            }
        }
    }
} 