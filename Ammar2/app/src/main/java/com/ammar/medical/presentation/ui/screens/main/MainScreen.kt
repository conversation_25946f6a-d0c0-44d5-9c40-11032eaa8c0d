package com.ammar.medical.presentation.ui.screens.main

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.ammar.medical.R
import com.ammar.medical.presentation.navigation.Screen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavController,
    content: @Composable (PaddingValues) -> Unit
) {
    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
    val scope = rememberCoroutineScope()

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                Spacer(modifier = Modifier.height(12.dp))
                NavigationDrawerHeader()
                NavigationDrawerBody(
                    navController = navController,
                    onNavigate = { route ->
                        scope.launch {
                            drawerState.close()
                            navController.navigate(route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    }
                )
            }
        }
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text(stringResource(R.string.app_name)) },
                    navigationIcon = {
                        IconButton(onClick = { scope.launch { drawerState.open() } }) {
                            Icon(Icons.Default.Menu, contentDescription = stringResource(R.string.open_menu))
                        }
                    }
                )
            }
        ) { paddingValues ->
            content(paddingValues)
        }
    }
}

@Composable
private fun NavigationDrawerHeader() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.MedicalServices,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = stringResource(R.string.app_name),
                style = MaterialTheme.typography.titleLarge
            )
        }
    }
}

@Composable
private fun NavigationDrawerBody(
    navController: NavController,
    onNavigate: (String) -> Unit
) {
    Column {
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.Home, contentDescription = null) },
            label = { Text(stringResource(R.string.home)) },
            selected = navController.currentDestination?.route == Screen.Home.route,
            onClick = { onNavigate(Screen.Home.route) }
        )
        
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.Search, contentDescription = null) },
            label = { Text(stringResource(R.string.search)) },
            selected = navController.currentDestination?.route == Screen.Search.route,
            onClick = { onNavigate(Screen.Search.route) }
        )
        
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.Category, contentDescription = null) },
            label = { Text(stringResource(R.string.categories)) },
            selected = navController.currentDestination?.route == Screen.CategoryManagement.route,
            onClick = { onNavigate(Screen.CategoryManagement.route) }
        )
        
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.Analytics, contentDescription = null) },
            label = { Text(stringResource(R.string.category_statistics)) },
            selected = navController.currentDestination?.route == Screen.CategoryStatistics.route,
            onClick = { onNavigate(Screen.CategoryStatistics.route) }
        )
        
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.FileDownload, contentDescription = null) },
            label = { Text(stringResource(R.string.export)) },
            selected = navController.currentDestination?.route == Screen.Export.route,
            onClick = { onNavigate(Screen.Export.route) }
        )
        
        NavigationDrawerItem(
            icon = { Icon(Icons.Default.Settings, contentDescription = null) },
            label = { Text(stringResource(R.string.settings)) },
            selected = navController.currentDestination?.route == Screen.Settings.route,
            onClick = { onNavigate(Screen.Settings.route) }
        )
    }
} 