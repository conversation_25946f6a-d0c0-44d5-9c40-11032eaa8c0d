# Firebase Cloud Function Deployment Guide

This guide will help you deploy the Cloud Function that sends notifications when a user receives a new message.

## Prerequisites

1. Node.js installed on your development machine
2. Firebase CLI installed (`npm install -g firebase-tools`)
3. A Firebase project with Firestore and Cloud Messaging enabled

## Deployment Steps

### 1. Install Firebase CLI (if not already installed)

```bash
npm install -g firebase-tools
```

### 2. Login to Firebase

```bash
firebase login
```

This will open a browser window where you need to sign in with the Google account associated with your Firebase project.

### 3. Initialize Firebase in your project (if not already done)

```bash
cd /Users/<USER>/Desktop/Ammar2
firebase init functions
```

When prompted:
- Select "Use an existing project" and choose your Firebase project
- Select JavaScript when asked about language
- Choose "No" when asked about ESLint
- Choose "No" when asked if you want to install dependencies with npm

**Note**: Since we've already created the necessary files, you can choose to overwrite them when prompted.

### 4. Install Dependencies

```bash
cd functions
npm install
```

### 5. Deploy the Cloud Function

```bash
firebase deploy --only functions
```

This will deploy the `sendMessageNotification` function to Firebase. You should see a success message with the URL of your deployed function.

## Verifying Deployment

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Navigate to Functions
3. You should see your `sendMessageNotification` function listed
4. Click on the function to view its details and logs

## Testing the Function

1. Send a message from one user to another in your app
2. Check the Firebase Functions logs to see if the function was triggered
3. The recipient should receive a notification if the app is in the background

## Troubleshooting

If you encounter any issues:

1. **Check Firebase Functions Logs**:
   - Go to the Firebase Console → Functions
   - Click on the `sendMessageNotification` function
   - Check the logs for any errors

2. **Verify Firestore Security Rules**:
   - Make sure your Firestore security rules allow the Cloud Function to read user documents
   - The rules should allow reading FCM tokens

3. **Check FCM Tokens**:
   - Verify that FCM tokens are being stored in user documents in Firestore
   - You can check this in the Firebase Console under Firestore Database

4. **Verify Message Structure**:
   - Make sure messages in Firestore have the required fields: `senderUid`, `recipientUid`, and `message`

5. **Check for Quota Limits**:
   - Firebase has quotas for Cloud Functions and FCM
   - Check if you've exceeded any quotas in the Firebase Console

## Next Steps

Once your Cloud Function is deployed and working:

1. Test sending messages between different users
2. Verify that notifications are received when the app is in the background
3. Check that clicking on a notification opens the app and shows the inbox

If you need to make changes to the function:
1. Edit the code in `functions/src/index.js`
2. Deploy again using `firebase deploy --only functions` 