package io.ammar.medical.repository

import android.util.Log
import io.ammar.medical.data.Contact
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await

/**
 * Repository for managing user contacts
 */
class ContactRepository {
    private val TAG = "ContactRepository"
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    /**
     * Get all contacts for the current user
     */
    suspend fun getContacts(): Result<List<Contact>> {
        val currentUser = auth.currentUser ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            val snapshot = db.collection("users")
                .document(currentUser.uid)
                .collection("contacts")
                .orderBy("timestamp", Query.Direction.DESCENDING)
                .get()
                .await()
            
            val contacts = snapshot.documents.mapNotNull { doc ->
                try {
                    Contact(
                        uid = doc.getString("uid") ?: return@mapNotNull null,
                        name = doc.getString("name") ?: "Unknown",
                        timestamp = doc.getLong("timestamp") ?: System.currentTimeMillis()
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing contact", e)
                    null
                }
            }
            
            Result.success(contacts)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting contacts", e)
            Result.failure(e)
        }
    }
    
    /**
     * Add a new contact
     */
    suspend fun addContact(uid: String, name: String): Result<Contact> {
        val currentUser = auth.currentUser ?: return Result.failure(Exception("No user logged in"))
        
        if (uid.isBlank()) {
            return Result.failure(Exception("Contact UID cannot be empty"))
        }
        
        val contactName = if (name.isBlank()) uid else name
        
        return try {
            // First verify that the user exists
            val userDoc = db.collection("users").document(uid).get().await()
            
            if (!userDoc.exists()) {
                return Result.failure(Exception("User with this ID does not exist"))
            }
            
            // User exists, add to contacts
            val contact = Contact(
                uid = uid,
                name = contactName,
                timestamp = System.currentTimeMillis()
            )
            
            db.collection("users").document(currentUser.uid)
                .collection("contacts")
                .document(uid) // Use the UID as the document ID to prevent duplicates
                .set(contact)
                .await()
            
            Result.success(contact)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding contact", e)
            Result.failure(e)
        }
    }
    
    /**
     * Delete a contact
     */
    suspend fun deleteContact(contact: Contact): Result<Unit> {
        val currentUser = auth.currentUser ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            db.collection("users").document(currentUser.uid)
                .collection("contacts")
                .document(contact.uid)
                .delete()
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting contact", e)
            Result.failure(e)
        }
    }
} 