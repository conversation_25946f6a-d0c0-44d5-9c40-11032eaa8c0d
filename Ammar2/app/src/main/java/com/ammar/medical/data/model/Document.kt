package com.ammar.medical.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "documents")
data class Document(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val uri: String,
    val documentType: String,
    val patientInfo: Map<String, String>,
    val doctorInfo: Map<String, String>,
    val date: String,
    val medicalDetails: List<String>,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val isSynced: Boolean = false
) 