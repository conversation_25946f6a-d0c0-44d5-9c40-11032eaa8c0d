package com.ammar.medical.data.repository

import com.ammar.medical.data.model.Document
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TagRepository @Inject constructor(
    private val documentRepository: DocumentRepository
) {
    suspend fun getTagSuggestions(): Set<String> {
        val allTags = mutableSetOf<String>()
        documentRepository.getAllDocuments().forEach { document ->
            allTags.addAll(document.tags)
        }
        return allTags + DEFAULT_TAGS
    }

    suspend fun getPopularTags(limit: Int = 10): Set<String> {
        val tagCounts = mutableMapOf<String, Int>()
        documentRepository.getAllDocuments().forEach { document ->
            document.tags.forEach { tag ->
                tagCounts[tag] = (tagCounts[tag] ?: 0) + 1
            }
        }
        return tagCounts.entries
            .sortedByDescending { it.value }
            .take(limit)
            .map { it.key }
            .toSet()
    }

    companion object {
        private val DEFAULT_TAGS = setOf(
            "Medical",
            "Lab Results",
            "X-Ray",
            "MRI",
            "CT Scan",
            "Prescription",
            "Report",
            "Blood Test",
            "Urine Test",
            "ECG",
            "Ultrasound",
            "Biopsy",
            "Vaccination",
            "Allergy",
            "Dental",
            "Vision",
            "Hearing",
            "Physical Therapy",
            "Mental Health",
            "Emergency"
        )
    }
} 