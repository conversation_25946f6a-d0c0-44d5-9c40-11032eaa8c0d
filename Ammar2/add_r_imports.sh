#!/bin/bash

# Directory with the new files
NEW_DIR="app/src/main/java/io/ammar/medical"

# Find all Kotlin files
for file in $(find "$NEW_DIR" -name "*.kt" -type f); do
    # Check if the file references R but doesn't import it
    if grep -q "R\." "$file" && ! grep -q "import io\.ammar\.medical\.R" "$file"; then
        # Add import statement after the package declaration
        sed -i '' '/^package/a\'$'\n''import io.ammar.medical.R' "$file"
        echo "Added R import to $file"
    fi
done

echo "R class imports added to all necessary Kotlin files" 