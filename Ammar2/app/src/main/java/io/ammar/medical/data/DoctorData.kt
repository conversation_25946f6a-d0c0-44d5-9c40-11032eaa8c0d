package io.ammar.medical.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Data class representing a doctor
 */
@Parcelize
data class DoctorData(
    val id: String = "",
    val name: String = "",
    val email: String = "",
    val phone: String = "",
    val specialty: String = "",
    val hospital: String = "",
    val profileImageUrl: String = "",
    val isFavorite: Boolean = false
) : Parcelable

/**
 * Data class representing a saved doctor for a patient
 */
@Parcelize
data class SavedDoctor(
    val doctorId: String = "",
    val patientId: String = "",
    val doctorName: String = "",
    val doctorEmail: String = "",
    val doctorSpecialty: String = "",
    val lastShared: Long = 0,
    val shareCount: Int = 0
) : Parcelable 