package com.ammar.medical.presentation.ui.screens.document

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.DocumentRepository
import com.ammar.medical.domain.service.SharingService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DocumentDetailsViewModel @Inject constructor(
    private val documentRepository: DocumentRepository,
    private val sharingService: SharingService
) : ViewModel() {

    private val _uiState = MutableStateFlow<DocumentDetailsUiState>(DocumentDetailsUiState.Loading)
    val uiState: StateFlow<DocumentDetailsUiState> = _uiState.asStateFlow()

    private val _showOptionsMenu = MutableStateFlow(false)
    val showOptionsMenu: StateFlow<Boolean> = _showOptionsMenu.asStateFlow()

    fun loadDocument(documentId: Long) {
        viewModelScope.launch {
            try {
                documentRepository.getDocumentById(documentId)?.let { document ->
                    _uiState.value = DocumentDetailsUiState.Success(document)
                } ?: run {
                    _uiState.value = DocumentDetailsUiState.Error("Document not found")
                }
            } catch (e: Exception) {
                _uiState.value = DocumentDetailsUiState.Error(e.message ?: "Failed to load document")
            }
        }
    }

    fun toggleOptionsMenu() {
        _showOptionsMenu.value = !_showOptionsMenu.value
    }

    fun shareDocument(document: Document) {
        viewModelScope.launch {
            try {
                sharingService.shareDocument(document)
            } catch (e: Exception) {
                _uiState.value = DocumentDetailsUiState.Error(e.message ?: "Failed to share document")
            }
        }
    }

    fun deleteDocument(documentId: Long) {
        viewModelScope.launch {
            try {
                documentRepository.deleteDocument(documentId)
                _uiState.value = DocumentDetailsUiState.Success(
                    (uiState.value as? DocumentDetailsUiState.Success)?.document?.copy(
                        isDeleted = true
                    ) ?: throw IllegalStateException("Document not found")
                )
            } catch (e: Exception) {
                _uiState.value = DocumentDetailsUiState.Error(e.message ?: "Failed to delete document")
            }
        }
    }
}

sealed class DocumentDetailsUiState {
    object Loading : DocumentDetailsUiState()
    data class Success(val document: Document) : DocumentDetailsUiState()
    data class Error(val message: String) : DocumentDetailsUiState()
} 