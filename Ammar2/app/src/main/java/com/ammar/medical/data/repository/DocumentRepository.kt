package com.ammar.medical.data.repository

import com.ammar.medical.data.local.DocumentDao
import com.ammar.medical.data.model.Document
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DocumentRepository @Inject constructor(
    private val documentDao: DocumentDao,
    private val firestore: FirebaseFirestore,
    private val storage: FirebaseStorage
) {
    fun getAllDocuments(): Flow<List<Document>> = documentDao.getAllDocuments()

    suspend fun getDocumentById(id: Long): Document? = documentDao.getDocumentById(id)

    suspend fun insertDocument(document: Document): Long {
        val id = documentDao.insertDocument(document)
        syncDocument(document.copy(id = id))
        return id
    }

    suspend fun updateDocument(document: Document) {
        documentDao.updateDocument(document)
        syncDocument(document)
    }

    suspend fun deleteDocument(document: Document) {
        documentDao.deleteDocument(document)
        deleteFromCloud(document)
    }

    fun searchDocuments(query: String): Flow<List<Document>> = documentDao.searchDocuments(query)

    private suspend fun syncDocument(document: Document) {
        try {
            // Upload document file to Firebase Storage
            val file = File(document.uri)
            val storageRef = storage.reference
                .child("documents/${document.id}/${file.name}")
            storageRef.putFile(file.toUri()).await()

            // Save document metadata to Firestore
            firestore.collection("documents")
                .document(document.id.toString())
                .set(document)
                .await()

            documentDao.markDocumentAsSynced(document.id)
        } catch (e: Exception) {
            // Handle sync error
        }
    }

    private suspend fun deleteFromCloud(document: Document) {
        try {
            // Delete from Firebase Storage
            val storageRef = storage.reference
                .child("documents/${document.id}")
            storageRef.delete().await()

            // Delete from Firestore
            firestore.collection("documents")
                .document(document.id.toString())
                .delete()
                .await()
        } catch (e: Exception) {
            // Handle deletion error
        }
    }

    suspend fun syncUnsyncedDocuments() {
        val unsyncedDocuments = documentDao.getUnsyncedDocuments()
        unsyncedDocuments.forEach { document ->
            syncDocument(document)
        }
    }
} 