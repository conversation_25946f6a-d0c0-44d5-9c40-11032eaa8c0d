package com.ammar.medical.presentation.ui.screens.settings

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.ammar.medical.R
import com.ammar.medical.presentation.ui.components.CommonTopAppBar
import com.ammar.medical.presentation.ui.theme.AmmarTheme

@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    AmmarTheme {
        Scaffold(
            topBar = {
                CommonTopAppBar(
                    title = stringResource(R.string.settings),
                    navigationIcon = Icons.Default.ArrowBack,
                    onNavigationClick = onNavigateBack
                )
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Theme Selection
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.theme),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            ThemeOption(
                                icon = Icons.Default.LightMode,
                                text = stringResource(R.string.light),
                                selected = uiState.theme == Theme.LIGHT,
                                onClick = { viewModel.setTheme(Theme.LIGHT) }
                            )
                            ThemeOption(
                                icon = Icons.Default.DarkMode,
                                text = stringResource(R.string.dark),
                                selected = uiState.theme == Theme.DARK,
                                onClick = { viewModel.setTheme(Theme.DARK) }
                            )
                            ThemeOption(
                                icon = Icons.Default.AutoMode,
                                text = stringResource(R.string.system),
                                selected = uiState.theme == Theme.SYSTEM,
                                onClick = { viewModel.setTheme(Theme.SYSTEM) }
                            )
                        }
                    }
                }

                // Language Selection
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.language),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            LanguageOption(
                                text = "English",
                                selected = uiState.language == Language.ENGLISH,
                                onClick = { viewModel.setLanguage(Language.ENGLISH) }
                            )
                            LanguageOption(
                                text = "العربية",
                                selected = uiState.language == Language.ARABIC,
                                onClick = { viewModel.setLanguage(Language.ARABIC) }
                            )
                        }
                    }
                }

                // Notifications
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = stringResource(R.string.notifications),
                                style = MaterialTheme.typography.titleMedium
                            )
                            Switch(
                                checked = uiState.notificationsEnabled,
                                onCheckedChange = { viewModel.setNotificationsEnabled(it) }
                            )
                        }
                    }
                }

                // About Section
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.about),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = stringResource(R.string.version, "1.0.0"),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = stringResource(R.string.developer, "Ammar Medical"),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ThemeOption(
    icon: ImageVector,
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
    ) {
        FilledIconToggleButton(
            checked = selected,
            onCheckedChange = { onClick() }
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text
            )
        }
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
fun LanguageOption(
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    FilledTonalButton(
        onClick = onClick,
        colors = ButtonDefaults.filledTonalButtonColors(
            containerColor = if (selected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Text(text = text)
    }
} 