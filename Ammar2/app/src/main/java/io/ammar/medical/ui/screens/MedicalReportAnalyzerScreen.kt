package io.ammar.medical.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Computer
import androidx.compose.material.icons.filled.Upload
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.ammar.medical.R
import io.ammar.medical.ui.components.MedicalReportAnalysisResult
import io.ammar.medical.viewmodel.AnalysisState
import io.ammar.medical.viewmodel.MedicalAnalysisViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MedicalReportAnalyzerScreen(
    onNavigateBack: () -> Unit,
    viewModel: MedicalAnalysisViewModel = viewModel()
) {
    val context = LocalContext.current
    val analysisState by viewModel.analysisState.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.medical_analysis)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack, 
                            contentDescription = stringResource(R.string.back),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (analysisState) {
                is AnalysisState.Initial -> {
                    // Show upload UI
                    UploadUI(viewModel)
                }
                else -> {
                    // Show analysis result
                    MedicalReportAnalysisResult(
                        viewModel = viewModel,
                        analysisState = analysisState,
                        onDismiss = {
                            viewModel.resetAnalysisState()
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun UploadUI(viewModel: MedicalAnalysisViewModel) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val currentAnalysis by viewModel.currentAnalysis.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Medical Report Analyzer",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        Text(
            text = "Upload medical reports, lab results, or radiology images to get an AI-powered analysis.",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        // Upload button
        Button(
            onClick = {
                // Implement file selection logic here
                // For now, we'll just show a placeholder analysis
                val sampleTexts = listOf(
                    "Patient: John Doe\nAge: 45\nBlood Pressure: 140/90 mmHg\nHeart Rate: 72 bpm\nGlucose: 110 mg/dL\nCholesterol: 220 mg/dL\nHDL: 45 mg/dL\nLDL: 150 mg/dL\nTriglycerides: 180 mg/dL"
                )
                viewModel.analyzeMedicalReports(sampleTexts, listOf("lab_report"))
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
        ) {
            Icon(
                Icons.Default.Upload,
                contentDescription = "Upload",
                modifier = Modifier.padding(end = 8.dp)
            )
            Text("Upload Medical Reports")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Share to PC button - Only show if there's an analysis
        if (currentAnalysis != null) {
            Button(
                onClick = { viewModel.shareAnalysisToPC(context) },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Icon(
                    Icons.Default.Computer,
                    contentDescription = "Share to PC",
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text("Share to PC")
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        Text(
            text = "Supported file types: PDF, JPG, PNG",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "How it works",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "1. Upload your medical reports or lab results\n" +
                           "2. Our AI will extract and analyze the text\n" +
                           "3. Get a comprehensive analysis with critical findings highlighted\n" +
                           "4. Share the analysis with your doctor or save it to your PC",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
} 