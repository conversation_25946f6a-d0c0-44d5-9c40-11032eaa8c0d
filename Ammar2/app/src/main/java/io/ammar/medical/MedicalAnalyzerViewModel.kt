package io.ammar.medical

import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.UUID

data class MedicalFile(
    val id: String = UUID.randomUUID().toString(),
    val bitmap: Bitmap,
    val extractedText: String = "",
    val isProcessing: Boolean = false
)

class MedicalAnalyzerViewModel : ViewModel() {
    // Model for image analysis
    private val imageModel = GenerativeModel(
        modelName = "gemini-2.0-flash",
        apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk" // Replace with your actual API key
    )

    // Model for text analysis
    private val textModel = GenerativeModel(
        modelName = "gemini-2.0-flash",
        apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk" // Replace with your actual API key
    )

    private val _medicalFiles = MutableStateFlow<List<MedicalFile>>(emptyList())
    val medicalFiles: StateFlow<List<MedicalFile>> = _medicalFiles.asStateFlow()

    private val _holisticAnalysis = MutableStateFlow<String>("")
    val holisticAnalysis: StateFlow<String> = _holisticAnalysis.asStateFlow()

    private val _isAnalyzing = MutableStateFlow(false)
    val isAnalyzing: StateFlow<Boolean> = _isAnalyzing.asStateFlow()

    fun addMedicalImage(bitmap: Bitmap) {
        val newFile = MedicalFile(bitmap = bitmap)
        _medicalFiles.value = _medicalFiles.value + newFile
        extractTextFromImage(newFile)
    }

    private fun extractTextFromImage(file: MedicalFile) {
        viewModelScope.launch {
            try {
                // Update file status to processing
                updateFileStatus(file.id, true)

                val prompt = """
                    Extract and structure all the text from this medical document.
                    Include all visible text, numbers, and data.
                    Format the output in a clear, structured way.
                    If there are any medical terms, values, or important findings, highlight them.
                """.trimIndent()

                val inputContent = content {
                    image(file.bitmap)
                    text(prompt)
                }

                val response = imageModel.generateContent(inputContent)
                val extractedText = response.text ?: "No text could be extracted"

                // Update the file with extracted text
                updateFileWithText(file.id, extractedText)

            } catch (e: Exception) {
                updateFileWithText(file.id, "Error: ${e.message}")
            } finally {
                updateFileStatus(file.id, false)
            }
        }
    }

    private fun updateFileStatus(fileId: String, isProcessing: Boolean) {
        _medicalFiles.value = _medicalFiles.value.map { file ->
            if (file.id == fileId) file.copy(isProcessing = isProcessing) else file
        }
    }

    private fun updateFileWithText(fileId: String, text: String) {
        _medicalFiles.value = _medicalFiles.value.map { file ->
            if (file.id == fileId) file.copy(extractedText = text) else file
        }
    }

    fun performHolisticAnalysis() {
        if (_medicalFiles.value.isEmpty()) {
            _holisticAnalysis.value = "Please add medical files before performing analysis."
            return
        }

        if (_medicalFiles.value.any { it.isProcessing }) {
            _holisticAnalysis.value = "Please wait for all files to finish processing before analyzing."
            return
        }

        viewModelScope.launch {
            try {
                _isAnalyzing.value = true
                
                // Prepare the context from all files
                val allTexts = _medicalFiles.value.mapIndexed { index, file ->
                    "Document ${index + 1}:\n${file.extractedText}"
                }.joinToString("\n\n---\n\n")
                
                val prompt = """
                    As a medical expert, analyze the following medical documents holistically.
                    
                    Documents to analyze:
                    $allTexts
                    
                    Please provide a comprehensive analysis including:
                    
                    1. KEY FINDINGS:
                    - List the most important findings from all documents
                    - Highlight any abnormal values or concerning results
                    
                    2. PATTERNS & CORRELATIONS:
                    - Identify any patterns across the documents
                    - Note any correlations between different findings
                    
                    3. DIAGNOSIS & ASSESSMENT:
                    - Potential diagnoses based on the findings
                    - Level of certainty for each diagnosis
                    
                    4. RECOMMENDATIONS:
                    - Suggested next steps
                    - Additional tests or examinations needed
                    - Treatment recommendations if applicable
                    
                    5. URGENT CONCERNS:
                    - Any findings requiring immediate attention
                    - Critical values or dangerous conditions
                    
                    Format the response in a clear, structured manner with headers and bullet points.
                """.trimIndent()

                val response = textModel.generateContent(prompt)
                _holisticAnalysis.value = response.text ?: "No analysis available"
            } catch (e: Exception) {
                _holisticAnalysis.value = "Error performing holistic analysis: ${e.message}"
            } finally {
                _isAnalyzing.value = false
            }
        }
    }

    fun removeFile(fileId: String) {
        _medicalFiles.value = _medicalFiles.value.filter { it.id != fileId }
        if (_medicalFiles.value.isEmpty()) {
            _holisticAnalysis.value = ""
        }
    }

    fun reprocessFiles() {
        val currentFiles = _medicalFiles.value
        currentFiles.forEach { file ->
            extractTextFromImage(file)
        }
    }

    fun clearAll() {
        _medicalFiles.value = emptyList()
        _holisticAnalysis.value = ""
        _isAnalyzing.value = false
    }
} 