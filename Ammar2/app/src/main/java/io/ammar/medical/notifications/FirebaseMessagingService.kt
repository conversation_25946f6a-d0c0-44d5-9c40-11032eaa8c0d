package io.ammar.medical.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import io.ammar.medical.MainActivity
import io.ammar.medical.R
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.firebase.firestore.FirebaseFirestore

class MyFirebaseMessagingService : FirebaseMessagingService() {
    
    companion object {
        private const val TAG = "FCMService"
    }
    
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "Refreshed FCM token: $token")
        // Store the token in Firestore for the current user
        storeToken(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d(TAG, "Message received from: ${remoteMessage.from}")

        // Handle notification when app is in foreground
        remoteMessage.notification?.let { notification ->
            Log.d(TAG, "Message Notification Title: ${notification.title}")
            Log.d(TAG, "Message Notification Body: ${notification.body}")
            showNotification(
                title = notification.title ?: "New Notification",
                message = notification.body ?: "You have a new notification"
            )
        }

        // Handle data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            handleDataPayload(remoteMessage.data)
        }
    }

    private fun showNotification(title: String, message: String) {
        val channelId = "default_channel"
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Default Channel",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Default Notification Channel"
                enableLights(true)
                enableVibration(true)
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Create intent for notification tap action
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // Build notification
        val notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .build()

        // Show notification
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
        Log.d(TAG, "Notification shown with title: $title and message: $message")
    }

    private fun storeToken(token: String) {
        val firestore = FirebaseFirestore.getInstance()
        val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser

        currentUser?.let { user ->
            firestore.collection("users").document(user.uid)
                .update("fcmToken", token)
                .addOnSuccessListener {
                    Log.d(TAG, "FCM token successfully stored for user: ${user.uid}")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to store FCM token", e)
                    // Try to create a new document if update fails
                    firestore.collection("users").document(user.uid)
                        .set(mapOf("fcmToken" to token))
                        .addOnSuccessListener {
                            Log.d(TAG, "FCM token successfully stored in new document for user: ${user.uid}")
                        }
                        .addOnFailureListener { innerE ->
                            Log.e(TAG, "Failed to store FCM token in new document", innerE)
                        }
                }
        } ?: Log.e(TAG, "No user logged in to store FCM token")
    }

    private fun handleDataPayload(data: Map<String, String>) {
        Log.d(TAG, "Handling data payload: $data")
        
        // Check if this is a message notification
        if (data["type"] == "new_message") {
            val messageId = data["messageId"]
            val senderUid = data["senderUid"]
            val senderName = data["senderName"] ?: "Someone"
            val messageContent = data["messageContent"] ?: "You have received a new message"
            
            // Show a notification for the new message
            val title = "New Message from $senderName"
            val message = messageContent
            
            // If we have sender info, enhance the notification
            if (senderUid != null) {
                // Create intent with extras to open inbox directly
                val intent = Intent(this, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    putExtra("openInbox", true)
                    putExtra("messageId", messageId)
                    putExtra("senderUid", senderUid)
                }
                
                val pendingIntent = PendingIntent.getActivity(
                    this, 0, intent,
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                )
                
                // Show enhanced notification
                showMessageNotification(title, message, pendingIntent)
            } else {
                // Show regular notification
                showNotification(title, message)
            }
        }
    }
    
    private fun showMessageNotification(title: String, message: String, pendingIntent: PendingIntent) {
        val channelId = "messages_channel"
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Messages Channel",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Channel for Message Notifications"
                enableLights(true)
                enableVibration(true)
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Build notification
        val notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_notification)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .build()

        // Show notification
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
        Log.d(TAG, "Message notification shown with title: $title and message: $message")
    }
} 