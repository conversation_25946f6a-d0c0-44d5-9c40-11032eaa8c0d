package io.ammar.medical.viewmodel

import android.app.Application
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Data class representing the initialization state of the app
 */
data class InitializationState(
    val isInitialized: Boolean = false,
    val statusMessage: String = "Initializing...",
    val errorMessage: String? = null,
    val retryCount: Int = 0,
    val isOfflineMode: Boolean = false
)

/**
 * ViewModel for the splash screen that handles app initialization
 */
class SplashViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "SplashViewModel"
    
    // Firebase instances
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    private val messaging = FirebaseMessaging.getInstance()
    
    // Initialization state
    var initializationState by mutableStateOf(InitializationState())
        private set
    
    // Network state
    private val _isNetworkAvailable = MutableStateFlow(false)
    val isNetworkAvailable: StateFlow<Boolean> = _isNetworkAvailable.asStateFlow()
    
    // Firebase state
    private val _isFirebaseAvailable = MutableStateFlow(false)
    val isFirebaseAvailable: StateFlow<Boolean> = _isFirebaseAvailable.asStateFlow()
    
    /**
     * Initialize the app by checking Firebase services and performing authentication
     */
    fun initializeApp() {
        viewModelScope.launch {
            try {
                // Check network connectivity first
                updateState(statusMessage = "Checking network connectivity...")
                val isNetworkAvailable = NetworkUtils.isNetworkAvailable(getApplication())
                _isNetworkAvailable.value = isNetworkAvailable
                
                if (!isNetworkAvailable) {
                    Log.w(TAG, "Network is not available, entering offline mode")
                    updateState(
                        statusMessage = "Network unavailable, entering offline mode...",
                        isOfflineMode = true
                    )
                    
                    // Set up offline mode
                    setupOfflineMode()
                    
                    // Still try to initialize Firebase with offline persistence
                    tryInitializeFirebaseOffline()
                    
                    // Try to use cached authentication if available
                    if (auth.currentUser != null) {
                        Log.d(TAG, "Using cached authentication: ${auth.currentUser?.uid}")
                    } else {
                        Log.w(TAG, "No cached authentication available")
                    }
                    
                    // Mark initialization as complete after a delay
                    delay(2000)
                    updateState(
                        isInitialized = true,
                        statusMessage = "Offline mode ready",
                        isOfflineMode = true
                    )
                    
                    return@launch
                }
                
                // Check Firebase initialization
                updateState(statusMessage = "Initializing Firebase...")
                val firebaseReady = checkFirebaseInitialization()
                _isFirebaseAvailable.value = firebaseReady
                
                if (!firebaseReady) {
                    // If Firebase initialization failed, enter offline mode
                    Log.w(TAG, "Firebase initialization failed, entering offline mode")
                    updateState(
                        statusMessage = "Firebase unavailable, entering offline mode...",
                        isOfflineMode = true
                    )
                    
                    // Set up offline mode
                    setupOfflineMode()
                    
                    // Try to use cached authentication if available
                    if (auth.currentUser != null) {
                        Log.d(TAG, "Using cached authentication: ${auth.currentUser?.uid}")
                    }
                    
                    // Mark initialization as complete after a delay
                    delay(2000)
                    updateState(
                        isInitialized = true,
                        statusMessage = "Offline mode ready",
                        isOfflineMode = true
                    )
                    
                    return@launch
                }
                
                // Perform authentication (anonymous if needed)
                updateState(statusMessage = "Authenticating...")
                val authSuccess = ensureAuthentication()
                
                if (!authSuccess) {
                    // If authentication failed, enter offline mode
                    Log.w(TAG, "Authentication failed, entering offline mode")
                    updateState(
                        statusMessage = "Authentication failed, entering offline mode...",
                        isOfflineMode = true
                    )
                    
                    // Set up offline mode
                    setupOfflineMode()
                    
                    // Mark initialization as complete after a delay
                    delay(2000)
                    updateState(
                        isInitialized = true,
                        statusMessage = "Offline mode ready",
                        isOfflineMode = true
                    )
                    
                    return@launch
                }
                
                // Register FCM token if possible
                if (isNetworkAvailable && firebaseReady && authSuccess) {
                    updateState(statusMessage = "Registering for notifications...")
                    registerFcmToken()
                }
                
                // Add a small delay to ensure everything is ready
                delay(1000)
                
                // Mark initialization as complete
                updateState(
                    isInitialized = true,
                    statusMessage = "Initialization complete!",
                    isOfflineMode = false
                )
                
                Log.d(TAG, "App initialization completed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error during app initialization", e)
                updateState(
                    statusMessage = "Error during initialization, entering offline mode...",
                    errorMessage = e.message ?: "Unknown error occurred",
                    isOfflineMode = true
                )
                
                // Set up offline mode
                setupOfflineMode()
                
                // Even if there's an error, we'll proceed after a delay
                delay(2000)
                updateState(
                    isInitialized = true,
                    statusMessage = "Offline mode ready",
                    isOfflineMode = true
                )
            }
        }
    }
    
    /**
     * Set up offline mode by configuring Firebase for offline use
     */
    private suspend fun setupOfflineMode() {
        try {
            // Enable Firestore offline persistence if not already enabled
            withContext(Dispatchers.IO) {
                try {
                    val settings = com.google.firebase.firestore.FirebaseFirestoreSettings.Builder()
                        .setPersistenceEnabled(true)
                        .setCacheSizeBytes(com.google.firebase.firestore.FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                        .build()
                    
                    firestore.firestoreSettings = settings
                    Log.d(TAG, "Firestore offline persistence enabled")
                } catch (e: Exception) {
                    Log.e(TAG, "Error enabling Firestore offline persistence", e)
                }
            }
            
            // Try to use cached auth state if available
            if (auth.currentUser != null) {
                Log.d(TAG, "Using cached authentication state for offline mode")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up offline mode", e)
        }
    }
    
    /**
     * Try to initialize Firebase with offline capabilities
     */
    private suspend fun tryInitializeFirebaseOffline() {
        try {
            // Enable Firestore offline persistence
            withContext(Dispatchers.IO) {
                try {
                    val settings = com.google.firebase.firestore.FirebaseFirestoreSettings.Builder()
                        .setPersistenceEnabled(true)
                        .setCacheSizeBytes(com.google.firebase.firestore.FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                        .build()
                    
                    firestore.firestoreSettings = settings
                    Log.d(TAG, "Firestore offline persistence enabled")
                } catch (e: Exception) {
                    Log.e(TAG, "Error enabling Firestore offline persistence", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase offline", e)
        }
    }
    
    /**
     * Check if Firebase services are properly initialized
     */
    private suspend fun checkFirebaseInitialization(): Boolean {
        return try {
            // First, do a quick check with a Firestore operation
            val isFirestoreWorking = testFirestoreConnection()
            
            if (isFirestoreWorking) {
                // If Firestore is working, we're online
                return true
            }
            
            // If the quick check failed, do a more thorough check
            val connectivityResult = FirebaseUtils.checkFirebaseConnectivity(getApplication())
            
            // Log the result
            Log.d(TAG, "Firebase connectivity check result: $connectivityResult")
            
            // Consider Firebase ready if either Firestore or Auth is connected
            connectivityResult.isFirestoreConnected || connectivityResult.isAuthConnected
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Firebase initialization", e)
            false
        }
    }
    
    /**
     * Test if Firestore is working with a simple operation
     */
    private suspend fun testFirestoreConnection(): Boolean {
        return try {
            withTimeoutOrNull(5000) { // 5 second timeout
                val db = FirebaseFirestore.getInstance()
                // Try to get a document that should always exist (or at least the query should work)
                val result = db.collection("users").limit(1).get().await()
                true // If we get here, Firestore is working
            } ?: false // Timeout occurred
        } catch (e: Exception) {
            Log.e(TAG, "Error testing Firestore connection", e)
            false
        }
    }
    
    /**
     * Ensure the user is authenticated, using anonymous auth if needed
     */
    private suspend fun ensureAuthentication(): Boolean {
        return try {
            // Check if user is already authenticated
            if (auth.currentUser != null) {
                Log.d(TAG, "User already authenticated: ${auth.currentUser?.uid}")
                return true
            }
            
            // Try to sign in anonymously with timeout
            Log.d(TAG, "No user authenticated, attempting anonymous sign-in")
            val authResult = withTimeoutOrNull(10000) { // 10 seconds timeout
                auth.signInAnonymously().await()
            }
            
            if (authResult != null) {
                Log.d(TAG, "Anonymous authentication successful: ${authResult.user?.uid}")
                
                // Create user document in Firestore
                authResult.user?.let { user ->
                    try {
                        // Check if user document exists
                        val userDoc = firestore.collection("users").document(user.uid).get().await()
                        
                        if (!userDoc.exists()) {
                            // Create new user document
                            val userData = hashMapOf(
                                "id" to user.uid,
                                "username" to "Anonymous User",
                                "isAnonymous" to true,
                                "createdAt" to System.currentTimeMillis(),
                                "lastLogin" to System.currentTimeMillis()
                            )
                            
                            firestore.collection("users").document(user.uid).set(userData).await()
                            Log.d(TAG, "Created new user document for anonymous user")
                        } else {
                            // Update last login
                            firestore.collection("users").document(user.uid)
                                .update("lastLogin", System.currentTimeMillis())
                                .await()
                            Log.d(TAG, "Updated last login for existing user")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error creating/updating user document", e)
                        // Continue anyway, this is not critical
                    }
                }
                
                return true
            } else {
                Log.w(TAG, "Anonymous authentication timed out")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during authentication", e)
            false
        }
    }
    
    /**
     * Register FCM token for push notifications
     */
    private suspend fun registerFcmToken() {
        try {
            val token = withTimeoutOrNull(5000) { // 5 seconds timeout
                messaging.token.await()
            }
            
            if (token != null) {
                Log.d(TAG, "FCM token retrieved: ${token.take(10)}...")
                
                // Store token in Firestore
                val currentUser = auth.currentUser
                if (currentUser != null) {
                    try {
                        firestore.collection("users").document(currentUser.uid)
                            .update("fcmToken", token)
                            .await()
                        Log.d(TAG, "FCM token stored in Firestore")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error storing FCM token", e)
                        // Not critical, continue
                    }
                }
            } else {
                Log.w(TAG, "Failed to retrieve FCM token (timeout)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error registering FCM token", e)
            // Not critical, continue
        }
    }
    
    /**
     * Update the initialization state
     */
    private fun updateState(
        isInitialized: Boolean = initializationState.isInitialized,
        statusMessage: String = initializationState.statusMessage,
        errorMessage: String? = initializationState.errorMessage,
        retryCount: Int = initializationState.retryCount,
        isOfflineMode: Boolean = initializationState.isOfflineMode
    ) {
        initializationState = initializationState.copy(
            isInitialized = isInitialized,
            statusMessage = statusMessage,
            errorMessage = errorMessage,
            retryCount = retryCount,
            isOfflineMode = isOfflineMode
        )
        
        Log.d(TAG, "Initialization state updated: $statusMessage, offline mode: $isOfflineMode")
    }
    
    /**
     * Retry initialization (can be called from UI)
     */
    fun retryInitialization() {
        if (initializationState.isInitialized) {
            // Only allow retry if we're in offline mode
            if (initializationState.isOfflineMode) {
                updateState(
                    isInitialized = false,
                    statusMessage = "Retrying initialization...",
                    errorMessage = null,
                    retryCount = initializationState.retryCount + 1,
                    isOfflineMode = false
                )
                
                initializeApp()
            }
        }
    }
} 