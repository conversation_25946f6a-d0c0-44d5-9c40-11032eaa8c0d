package com.ammar.medical.domain.service

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import androidx.core.content.FileProvider
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.model.DocumentType
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SharingService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val qrCodeService: QrCodeService
) {
    fun shareDocument(document: Document) {
        val intent = Intent(Intent.ACTION_SEND_MULTIPLE).apply {
            type = when (document.type) {
                DocumentType.IMAGE -> "image/*"
                DocumentType.PDF -> "application/pdf"
                else -> "*/*"
            }
            
            val uris = mutableListOf<Uri>()
            
            // Add document file
            val documentFile = File(document.uri)
            val documentUri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                documentFile
            )
            uris.add(documentUri)
            
            // Add QR code
            val qrCode = qrCodeService.generateDocumentQrCode(document.id)
            val qrCodeUri = saveQrCodeToCache(qrCode, document.id)
            uris.add(qrCodeUri)
            
            putParcelableArrayListExtra(Intent.EXTRA_STREAM, ArrayList(uris))
            
            // Add text content
            val subject = "Medical Document: ${document.title}"
            val text = buildDocumentText(document)
            putExtra(Intent.EXTRA_SUBJECT, subject)
            putExtra(Intent.EXTRA_TEXT, text)
            
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        val chooser = Intent.createChooser(intent, context.getString(R.string.share_document))
        context.startActivity(chooser)
    }
    
    fun shareQrCode(documentId: Long) {
        val qrCode = qrCodeService.generateDocumentQrCode(documentId)
        val qrCodeUri = saveQrCodeToCache(qrCode, documentId)
        
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "image/png"
            putExtra(Intent.EXTRA_STREAM, qrCodeUri)
            putExtra(Intent.EXTRA_SUBJECT, context.getString(R.string.share_qr_code))
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        val chooser = Intent.createChooser(intent, context.getString(R.string.share_qr_code))
        context.startActivity(chooser)
    }
    
    private fun buildDocumentText(document: Document): String {
        return buildString {
            appendLine("Medical Document Details:")
            appendLine("Title: ${document.title}")
            appendLine("Type: ${document.type}")
            appendLine("Date: ${document.date}")
            if (!document.description.isNullOrEmpty()) {
                appendLine("Description: ${document.description}")
            }
            if (document.tags.isNotEmpty()) {
                appendLine("Tags: ${document.tags.joinToString(", ")}")
            }
        }
    }
    
    private fun saveQrCodeToCache(qrCode: Bitmap, documentId: Long): Uri {
        val cacheDir = context.cacheDir
        val qrCodeFile = File(cacheDir, "qr_code_$documentId.png")
        qrCodeFile.outputStream().use { out ->
            qrCode.compress(Bitmap.CompressFormat.PNG, 100, out)
        }
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            qrCodeFile
        )
    }
} 