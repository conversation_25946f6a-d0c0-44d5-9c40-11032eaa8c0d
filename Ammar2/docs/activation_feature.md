# Dynamic Activation Code Feature

This document explains the dynamic activation code feature, which allows you to control access to premium features in the app without updating the app itself.

## Feature Overview

The activation code system allows you to:

1. Control access to premium features (like AI analysis) through activation codes
2. Update valid activation codes remotely without updating the app
3. Basic features like text extraction remain available to all users
4. Check the validity of stored activation codes when the app starts

## How It Works

1. **Firebase Firestore**: The app fetches valid activation codes from a Firestore database
2. **Settings Screen**: Users enter their activation code in the Settings screen
3. **Validation**: The app checks if the entered code is in the list of valid codes
4. **Feature Access**: Premium features are only accessible if the user has a valid activation code
5. **Persistence**: Valid activation codes are stored locally for offline use
6. **Verification**: The app re-validates stored codes when it starts to ensure they're still valid

## Implementation

### SettingsViewModel

The `SettingsViewModel` class handles:
- Fetching valid activation codes from Firestore
- Validating user-entered codes
- Storing valid codes locally
- Checking if premium features are available
- Refreshing the list of valid codes

### UI Components

The Settings screen includes:
- An activation code input field
- A status indicator showing whether premium features are activated
- A refresh button to update the list of valid codes
- Error messages for invalid activation codes

### Premium Features Control

The `MultiImageAnalyzerScreen` checks if the user has a valid activation code before allowing analysis:
- If activated, the user can use all features
- If not activated, a dialog prompts the user to enter an activation code in Settings

## Changing Activation Codes

To change the valid activation codes:

1. Go to the Firebase Console
2. Navigate to Firestore Database
3. Find the "configuration" collection and "activation_codes" document
4. Edit the "codes" array to add or remove activation codes
5. Users with the app will receive the updated codes the next time they refresh or restart the app

## Security Considerations

- The app can only read the activation codes, not modify them
- Users might be able to extract codes from the app, so don't use codes that need to be kept secret
- This system is suitable for feature gating, not for high-security applications
- For more security, consider implementing server-side validation 