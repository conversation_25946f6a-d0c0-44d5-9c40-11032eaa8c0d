package com.ammar.medical.presentation.ui.screens.camera

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.camera.CameraManager
import com.ammar.medical.domain.service.DocumentAnalysisService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CameraViewModel @Inject constructor(
    private val cameraManager: CameraManager,
    private val documentAnalysisService: DocumentAnalysisService
) : ViewModel() {

    private val _uiState = MutableStateFlow<CameraUiState>(CameraUiState.Initial)
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()

    fun onImageCaptured(uri: Uri) {
        viewModelScope.launch {
            _uiState.value = CameraUiState.Processing
            try {
                val result = documentAnalysisService.analyzeDocument(uri)
                when (result) {
                    is DocumentAnalysisResult.Success -> {
                        _uiState.value = CameraUiState.Success(
                            uri = uri,
                            documentType = result.documentType,
                            patientInfo = result.patientInfo,
                            doctorInfo = result.doctorInfo,
                            date = result.date,
                            medicalDetails = result.medicalDetails
                        )
                    }
                    is DocumentAnalysisResult.Error -> {
                        _uiState.value = CameraUiState.Error(result.message)
                    }
                }
            } catch (e: Exception) {
                _uiState.value = CameraUiState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    fun onError(error: Exception) {
        _uiState.value = CameraUiState.Error(error.message ?: "Unknown error occurred")
    }

    override fun onCleared() {
        super.onCleared()
        cameraManager.release()
    }
}

sealed class CameraUiState {
    object Initial : CameraUiState()
    object Processing : CameraUiState()
    data class Success(
        val uri: Uri,
        val documentType: String,
        val patientInfo: Map<String, String>,
        val doctorInfo: Map<String, String>,
        val date: String,
        val medicalDetails: List<String>
    ) : CameraUiState()
    data class Error(val message: String) : CameraUiState()
} 