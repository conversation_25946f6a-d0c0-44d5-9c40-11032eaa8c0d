#!/usr/bin/env python3
import firebase_admin
from firebase_admin import credentials, firestore, auth
import json
import os
from datetime import datetime
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import webbrowser
import qrcode
from qrcode.image.pil import PilImage
from PIL import Image, ImageTk
import uuid
import time
import io

class TextExtractionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Text Extraction Sync Tool")
        self.root.geometry("700x600")
        self.root.minsize(600, 500)
        
        # Set app icon if available
        try:
            self.root.iconbitmap("app_icon.ico")  # You can add an icon file
        except:
            pass
            
        # Configure style
        self.style = ttk.Style()
        self.style.configure("TFrame", background="#f0f0f0")
        self.style.configure("TButton", font=("Arial", 10))
        self.style.configure("TLabel", font=("Arial", 10), background="#f0f0f0")
        self.style.configure("Header.TLabel", font=("Arial", 14, "bold"), background="#f0f0f0")
        self.style.configure("Subheader.TLabel", font=("Arial", 12), background="#f0f0f0")
        
        # Main frame
        self.main_frame = ttk.Frame(root, padding="20")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        ttk.Label(self.main_frame, text="Text Extraction Sync Tool", style="Header.TLabel").pack(pady=(0, 20))
        
        # Session variables
        self.session_id = None
        self.session_listener = None
        self.user_id = None
        self.qr_code_image = None
        self.session_check_thread = None
        self.session_active = False
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # QR Code tab
        self.qr_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.qr_tab, text="Scan QR Code")
        
        # Manual Login tab
        self.manual_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.manual_tab, text="Manual Login")
        
        # Setup QR Code tab
        self.setup_qr_tab()
        
        # Setup Manual Login tab
        self.setup_manual_tab()
        
        # Status and log area (common to both tabs)
        ttk.Label(self.main_frame, text="Status Log:", style="Subheader.TLabel").pack(anchor=tk.W, pady=(20, 5))
        
        self.log_frame = ttk.Frame(self.main_frame, borderwidth=1, relief=tk.SUNKEN)
        self.log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(self.log_frame, wrap=tk.WORD, height=10, bg="#ffffff")
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.scrollbar = ttk.Scrollbar(self.log_frame, command=self.log_text.yview)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=self.scrollbar.set)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=10)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.main_frame, textvariable=self.status_var)
        self.status_label.pack(anchor=tk.W)
        
        # Footer with open folder button
        self.footer_frame = ttk.Frame(self.main_frame)
        self.footer_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.open_folder_btn = ttk.Button(self.footer_frame, text="Open Output Folder", command=self.open_output_folder)
        self.open_folder_btn.pack(side=tk.RIGHT)
        
        # Initialize Firebase
        self.firebase_initialized = False
        self.log("Welcome to the Text Extraction Sync Tool!")
        self.log("Please scan the QR code with your app or enter your User ID manually.")
        
        # Generate QR code on startup
        self.generate_qr_code()
    
    def setup_qr_tab(self):
        """Setup the QR code tab"""
        # QR code frame
        self.qr_frame = ttk.Frame(self.qr_tab, padding="20")
        self.qr_frame.pack(fill=tk.BOTH, expand=True)
        
        # Instructions
        ttk.Label(
            self.qr_frame, 
            text="Scan this QR code with the app to connect",
            style="Subheader.TLabel"
        ).pack(pady=(0, 20))
        
        # QR code display
        self.qr_display = ttk.Label(self.qr_frame)
        self.qr_display.pack(pady=20)
        
        # Connection status
        self.connection_status_var = tk.StringVar(value="Waiting for connection...")
        self.connection_status = ttk.Label(
            self.qr_frame, 
            textvariable=self.connection_status_var,
            foreground="#666666"
        )
        self.connection_status.pack(pady=10)
        
        # Refresh QR code button
        self.refresh_qr_btn = ttk.Button(
            self.qr_frame, 
            text="Generate New QR Code", 
            command=self.generate_qr_code
        )
        self.refresh_qr_btn.pack(pady=10)
        
        # Output directory frame
        self.qr_output_frame = ttk.Frame(self.qr_frame)
        self.qr_output_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(self.qr_output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.qr_output_dir_var = tk.StringVar(value="extracted_texts")
        self.qr_output_dir_entry = ttk.Entry(self.qr_output_frame, textvariable=self.qr_output_dir_var, width=40)
        self.qr_output_dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.qr_browse_btn = ttk.Button(self.qr_output_frame, text="Browse...", command=self.browse_directory_qr)
        self.qr_browse_btn.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Add Clear button to QR tab
        self.qr_action_frame = ttk.Frame(self.qr_frame)
        self.qr_action_frame.pack(fill=tk.X, pady=10)
        
        self.qr_clear_btn = ttk.Button(
            self.qr_action_frame, 
            text="Clear All Texts from Firebase", 
            command=self.clear_texts_qr,
            style="Warning.TButton"
        )
        self.qr_clear_btn.pack(side=tk.LEFT, padx=5)
    
    def setup_manual_tab(self):
        """Setup the manual login tab"""
        # User ID frame
        self.user_id_frame = ttk.Frame(self.manual_tab)
        self.user_id_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(self.user_id_frame, text="User ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.user_id_var = tk.StringVar()
        self.user_id_entry = ttk.Entry(self.user_id_frame, textvariable=self.user_id_var, width=40)
        self.user_id_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(self.user_id_frame, text="(Find this by clicking 'Share to PC' in the app)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Output directory frame
        self.output_frame = ttk.Frame(self.manual_tab)
        self.output_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(self.output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.output_dir_var = tk.StringVar(value="extracted_texts")
        self.output_dir_entry = ttk.Entry(self.output_frame, textvariable=self.output_dir_var, width=40)
        self.output_dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.browse_btn = ttk.Button(self.output_frame, text="Browse...", command=self.browse_directory)
        self.browse_btn.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Action buttons
        self.action_frame = ttk.Frame(self.manual_tab)
        self.action_frame.pack(fill=tk.X, pady=20)
        
        # Sync button
        self.sync_btn = ttk.Button(self.action_frame, text="Sync Texts to PC", command=self.sync_texts)
        self.sync_btn.pack(side=tk.LEFT, padx=5)
        
        # Clear button with warning color
        self.clear_btn = ttk.Button(
            self.action_frame, 
            text="Clear All Texts from Firebase", 
            command=self.clear_texts,
            style="Warning.TButton"
        )
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        # Configure warning style for clear button
        self.style.configure(
            "Warning.TButton",
            foreground="red",
            font=("Arial", 10, "bold")
        )
    
    def generate_qr_code(self):
        """Generate a new QR code for session authentication"""
        # Cancel any existing session check
        if self.session_check_thread and self.session_check_thread.is_alive():
            self.session_active = False
            self.session_check_thread.join(timeout=1.0)
        
        # Remove any existing session listener
        if self.session_listener:
            self.session_listener.unsubscribe()
            self.session_listener = None
        
        # Reset connection status
        self.connection_status_var.set("Waiting for connection...")
        self.connection_status.config(foreground="#666666")
        
        # Initialize Firebase if needed
        if not self.initialize_firebase():
            return
        
        # Generate a new session ID
        self.session_id = str(uuid.uuid4())
        self.session_active = True
        
        # Create session document in Firestore
        try:
            db = firestore.client()
            session_data = {
                'created': int(time.time() * 1000),  # Current time in milliseconds
                'expires': int(time.time() * 1000) + (5 * 60 * 1000),  # Expires in 5 minutes
                'status': 'pending',
                'userId': None,
                'deviceInfo': {
                    'platform': sys.platform,
                    'hostname': os.environ.get('COMPUTERNAME', os.environ.get('HOSTNAME', 'Unknown'))
                }
            }
            
            # Add session to Firestore
            db.collection('pc_sessions').document(self.session_id).set(session_data)
            
            # Create QR code with session ID
            qr_data = json.dumps({
                'type': 'pc_sync',
                'sessionId': self.session_id,
                'timestamp': int(time.time())
            })
            
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=6,  # Reduced from 10 to 6
                border=2,    # Reduced from 4 to 2
            )
            qr.add_data(qr_data)
            qr.make(fit=True)
            
            # Create PIL image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Resize the image to a smaller size (e.g., 200x200)
            img = img.resize((200, 200), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage for Tkinter
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # Keep reference to avoid garbage collection
            self.qr_code_image = ImageTk.PhotoImage(Image.open(img_buffer))
            
            # Display QR code
            self.qr_display.config(image=self.qr_code_image)
            
            # Start session check thread
            self.session_check_thread = threading.Thread(target=self._check_session_status, daemon=True)
            self.session_check_thread.start()
            
            # Set up listener for session changes
            self.session_listener = db.collection('pc_sessions').document(self.session_id).on_snapshot(
                self._on_session_update
            )
            
            self.log("QR code generated. Session ID: " + self.session_id)
            self.log("Scan this QR code with your app to connect.")
            
        except Exception as e:
            self.log("Error generating QR code: " + str(e))
            messagebox.showerror("QR Code Error", "Failed to generate QR code: " + str(e))
    
    def _on_session_update(self, doc_snapshot, changes, read_time):
        """Callback for session document updates"""
        try:
            for doc in doc_snapshot:
                if not doc.exists:
                    continue
                    
                data = doc.to_dict()
                status = data.get('status', 'pending')
                
                if status == 'connected' and data.get('userId'):
                    # Session is now connected
                    self.user_id = data.get('userId')
                    
                    # Update UI on main thread
                    self.root.after(0, self._handle_successful_connection, data)
                elif status == 'expired' or status == 'cancelled':
                    # Session expired or cancelled
                    self.root.after(0, self._handle_failed_connection, status)
        except Exception as e:
            self.log("Error in session update: " + str(e))
    
    def _handle_successful_connection(self, session_data):
        """Handle successful QR code connection"""
        user_id = session_data.get('userId')
        device_name = session_data.get('deviceInfo', {}).get('deviceName', 'Unknown device')
        
        self.connection_status_var.set("Connected to " + device_name)
        self.connection_status.config(foreground="#008800")
        
        self.log("Successfully connected with User ID: " + user_id)
        self.log("Connected to device: " + device_name)
        
        # Start syncing texts automatically
        output_dir = self.qr_output_dir_var.get().strip()
        if not output_dir:
            output_dir = "extracted_texts"
            self.qr_output_dir_var.set(output_dir)
        
        # Start sync in a separate thread
        threading.Thread(target=self._sync_texts_thread, args=(user_id, output_dir), daemon=True).start()
    
    def _handle_failed_connection(self, reason):
        """Handle failed QR code connection"""
        if reason == 'expired':
            self.connection_status_var.set("QR code expired. Generate a new one.")
            self.connection_status.config(foreground="#cc0000")
            self.log("QR code has expired. Please generate a new one.")
        else:
            self.connection_status_var.set("Connection cancelled. Try again.")
            self.connection_status.config(foreground="#cc0000")
            self.log("Connection was cancelled. Please try again.")
    
    def _check_session_status(self):
        """Check session status periodically and expire if needed"""
        try:
            db = firestore.client()
            session_ref = db.collection('pc_sessions').document(self.session_id)
            
            # Check every 5 seconds until session is no longer active
            while self.session_active:
                # Get current session data
                session_doc = session_ref.get()
                
                if not session_doc.exists:
                    self.log("Session no longer exists.")
                    break
                    
                session_data = session_doc.to_dict()
                status = session_data.get('status', 'pending')
                expires = session_data.get('expires', 0)
                
                # Check if session has expired
                current_time = int(time.time() * 1000)
                if status == 'pending' and current_time > expires:
                    # Update session as expired
                    session_ref.update({
                        'status': 'expired'
                    })
                    self.log("Session expired due to timeout.")
                    break
                
                # If session is connected or cancelled, stop checking
                if status in ['connected', 'cancelled', 'expired']:
                    break
                
                # Wait before next check
                time.sleep(5)
                
        except Exception as e:
            self.log("Error checking session status: " + str(e))
        finally:
            self.session_active = False
    
    def browse_directory_qr(self):
        """Open directory browser dialog for QR tab"""
        directory = filedialog.askdirectory(initialdir=os.getcwd())
        if directory:
            self.qr_output_dir_var.set(directory)
    
    def browse_directory(self):
        """Open directory browser dialog for manual tab"""
        directory = filedialog.askdirectory(initialdir=os.getcwd())
        if directory:
            self.output_dir_var.set(directory)
    
    def open_output_folder(self):
        """Open the output folder in file explorer"""
        # Use the active tab's output directory
        if self.notebook.index(self.notebook.select()) == 0:  # QR tab
            output_dir = self.qr_output_dir_var.get()
        else:  # Manual tab
            output_dir = self.output_dir_var.get()
            
        if os.path.exists(output_dir):
            if sys.platform == 'win32':
                os.startfile(output_dir)
            elif sys.platform == 'darwin':  # macOS
                webbrowser.open('file:///' + os.path.abspath(output_dir))
            else:  # Linux
                try:
                    os.system('xdg-open "' + os.path.abspath(output_dir) + '"')
                except:
                    self.log("Could not open folder. Path: " + output_dir)
        else:
            messagebox.showinfo("Folder Not Found", "The folder '" + output_dir + "' does not exist yet.")
    
    def log(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, "[" + timestamp + "] " + message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        if self.firebase_initialized:
            return True
            
        self.log("Initializing Firebase...")
        self.update_status("Initializing Firebase...")
        
        try:
            if os.path.exists('serviceAccountKey.json'):
                cred = credentials.Certificate('serviceAccountKey.json')
                firebase_admin.initialize_app(cred)
                self.firebase_initialized = True
                self.log("Firebase initialized successfully.")
                return True
            else:
                error_msg = (
                    "serviceAccountKey.json not found. Please download it from Firebase Console:\n"
                    "1. Go to Firebase Console\n"
                    "2. Project Settings > Service Accounts\n"
                    "3. Generate New Private Key\n"
                    "4. Save as 'serviceAccountKey.json' in the same directory as this script"
                )
                self.log(error_msg)
                messagebox.showerror("Firebase Error", error_msg)
                return False
        except Exception as e:
            self.log("Firebase initialization error: " + str(e))
            messagebox.showerror("Firebase Error", "Failed to initialize Firebase: " + str(e))
            return False
    
    def sync_texts(self):
        """Sync texts to PC (manual method)"""
        user_id = self.user_id_var.get().strip()
        output_dir = self.output_dir_var.get().strip()
        
        if not user_id:
            messagebox.showwarning("Input Required", "Please enter your User ID.")
            self.user_id_entry.focus()
            return
            
        if not output_dir:
            self.output_dir_var.set("extracted_texts")
            output_dir = "extracted_texts"
        
        # Disable buttons during operation
        self.sync_btn.config(state=tk.DISABLED)
        self.clear_btn.config(state=tk.DISABLED)
        
        # Start sync in a separate thread
        threading.Thread(target=self._sync_texts_thread, args=(user_id, output_dir), daemon=True).start()
    
    def _sync_texts_thread(self, user_id, output_dir):
        """Thread function for syncing texts"""
        try:
            if not self.initialize_firebase():
                if hasattr(self, 'sync_btn'):
                    self.sync_btn.config(state=tk.NORMAL)
                    self.clear_btn.config(state=tk.NORMAL)
                return
                
            self.log("Fetching all text extractions and medical analyses for user " + user_id + "...")
            self.update_status("Fetching texts and analyses...")
            self.progress_var.set(10)
            
            texts = get_extracted_texts(user_id)
            
            if not texts:
                self.log("No extracted texts or medical analyses found for this user.")
                self.log("Make sure you:")
                self.log("1. Have processed some texts in the Android app")
                self.log("2. Have uploaded images or shared content to the app")
                self.log("3. Have used the Medical Reports Analyzer feature")
                self.log("4. Are using the correct User ID")
                self.log("Note: Some extracted texts may expire after 1 hour.")
                messagebox.showinfo("No Content Found", "No extracted texts or medical analyses found for this user ID.")
                self.update_status("Ready")
                self.progress_var.set(0)
                if hasattr(self, 'sync_btn'):
                    self.sync_btn.config(state=tk.NORMAL)
                    self.clear_btn.config(state=tk.NORMAL)
                return
            
            # Count medical reports specifically
            medical_reports_count = sum(1 for text in texts if text.get('sourceType') == 'medical_report')
            other_texts_count = len(texts) - medical_reports_count
            
            self.log(f"Found {len(texts)} items: {other_texts_count} extracted texts and {medical_reports_count} medical analyses. Saving to {output_dir}...")
            self.update_status(f"Saving {len(texts)} items...")
            self.progress_var.set(50)
            
            # Display expiration information
            self.log("NOTE: Regular extracted texts will expire 1 hour after creation. Medical analyses are permanent.")
            self.log("Content overview:")
            
            # Group by source type for display
            source_types = {}
            for text in texts:
                source = text.get('sourceType', 'unknown')
                if source not in source_types:
                    source_types[source] = 0
                source_types[source] += 1
            
            for source, count in source_types.items():
                self.log(f"  • {source}: {count} items")
            
            # Show sample of texts
            self.log("Sample of content being synced:")
            shown_types = set()
            shown_count = 0
            
            # First show medical reports if any
            for text in texts:
                if text.get('sourceType') == 'medical_report' and 'medical_report' not in shown_types and shown_count < 5:
                    filename = text.get('fileName', 'unnamed')
                    self.log(f"  {shown_count+1}. [Medical Report] {filename}")
                    shown_types.add('medical_report')
                    shown_count += 1
            
            # Then show other types
            for text in texts:
                source = text.get('sourceType', 'unknown')
                if source != 'medical_report' and source not in shown_types and shown_count < 5:
                    filename = text.get('fileName', 'unnamed')
                    expires_in = text.get('expiresIn', 'unknown')
                    self.log(f"  {shown_count+1}. [{source}] {filename}: Expires in {expires_in}")
                    shown_types.add(source)
                    shown_count += 1
            
            # Show more examples if we haven't reached 5 yet
            if shown_count < 5:
                for text in texts:
                    if shown_count >= 5:
                        break
                    source = text.get('sourceType', 'unknown')
                    if source in shown_types:  # We've shown this type already but need more examples
                        filename = text.get('fileName', 'unnamed')
                        expires_in = text.get('expiresIn', 'unknown')
                        self.log(f"  {shown_count+1}. [{source}] {filename}: Expires in {expires_in}")
                        shown_count += 1
            
            if len(texts) > 5:
                self.log(f"  ... and {len(texts) - shown_count} more items")
            
            save_texts_to_files(texts, output_dir)
            self.progress_var.set(90)
            
            self.log("Sync complete! Files are organized by source type in the output directory.")
            self.log("Each text file has an accompanying metadata JSON file with additional information.")
            
            # Show directory structure
            self.log("Directory structure created:")
            for root, dirs, files in os.walk(output_dir):
                level = root.replace(output_dir, '').count(os.sep)
                indent = ' ' * 4 * level
                self.log(indent + os.path.basename(root) + "/")
                subindent = ' ' * 4 * (level + 1)
                file_count = len(files)
                if file_count <= 10:
                    for f in files:
                        self.log(subindent + f)
                else:
                    for f in files[:5]:
                        self.log(subindent + f)
                    self.log(subindent + "... and " + str(file_count - 5) + " more files")
            
            self.progress_var.set(100)
            self.update_status("Sync completed successfully")
            messagebox.showinfo("Sync Complete", f"Successfully synced {len(texts)} items to {output_dir}")
            
        except Exception as e:
            self.log("Error: " + str(e))
            self.update_status("Error occurred")
            messagebox.showerror("Error", "An error occurred: " + str(e))
        finally:
            if hasattr(self, 'sync_btn'):
                self.sync_btn.config(state=tk.NORMAL)
                self.clear_btn.config(state=tk.NORMAL)
    
    def clear_texts(self):
        """Clear all texts from Firebase"""
        user_id = self.user_id_var.get().strip()
        
        if not user_id:
            messagebox.showwarning("Input Required", "Please enter your User ID.")
            self.user_id_entry.focus()
            return
        
        # Confirm deletion
        confirm = messagebox.askokcancel(
            "Confirm Deletion",
            "WARNING: This will delete ALL your extracted texts from Firebase.\n"
            "This includes both shared and uploaded content.\n"
            "Medical report analyses will NOT be deleted as they are permanent records.\n"
            "This action cannot be undone.\n\n"
            "Are you sure you want to continue?",
            icon=messagebox.WARNING
        )
        
        if not confirm:
            self.log("Deletion cancelled.")
            return
        
        # Disable buttons during operation
        self.sync_btn.config(state=tk.DISABLED)
        self.clear_btn.config(state=tk.DISABLED)
        
        # Start deletion in a separate thread
        threading.Thread(target=self._clear_texts_thread, args=(user_id,), daemon=True).start()
    
    def _clear_texts_thread(self, user_id):
        """Thread function for clearing texts"""
        try:
            if not self.initialize_firebase():
                self.sync_btn.config(state=tk.NORMAL)
                self.clear_btn.config(state=tk.NORMAL)
                return
                
            self.log("Clearing all text extractions for user " + user_id + "...")
            self.update_status("Deleting texts...")
            self.progress_var.set(30)
            
            count = delete_all_texts(user_id)
            
            # Check if there are medical analyses that weren't deleted
            db = firestore.client()
            try:
                medical_docs = db.collection('medical_analyses')\
                    .where('patientId', '==', user_id)\
                    .stream()
                
                medical_count = sum(1 for _ in medical_docs)
                if medical_count > 0:
                    self.log(f"Note: {medical_count} medical analyses were not deleted as they are permanent records.")
            except Exception as e:
                print(f"Error counting medical analyses: {e}")
            
            self.progress_var.set(100)
            self.log("Successfully deleted " + str(count) + " text extractions for user " + user_id + ".")
            self.update_status("Deletion completed")
            messagebox.showinfo("Deletion Complete", "Successfully deleted " + str(count) + " text extractions for user " + user_id + ".")
            
        except Exception as e:
            self.log("Error: " + str(e))
            self.update_status("Error occurred")
            messagebox.showerror("Error", "An error occurred: " + str(e))
        finally:
            self.sync_btn.config(state=tk.NORMAL)
            self.clear_btn.config(state=tk.NORMAL)

    def clear_texts_qr(self):
        """Clear all texts from Firebase (QR tab version)"""
        # If we have a user ID from QR connection, use it
        if self.user_id:
            user_id = self.user_id
        else:
            messagebox.showwarning(
                "No Connection", 
                "Please scan the QR code with your app first or use the Manual Login tab."
            )
            return
        
        # Confirm deletion
        confirm = messagebox.askokcancel(
            "Confirm Deletion",
            "WARNING: This will delete ALL your extracted texts from Firebase.\n"
            "This includes both shared and uploaded content.\n"
            "Medical report analyses will NOT be deleted as they are permanent records.\n"
            "This action cannot be undone.\n\n"
            "Are you sure you want to continue?",
            icon=messagebox.WARNING
        )
        
        if not confirm:
            self.log("Deletion cancelled.")
            return
        
        # Disable buttons during operation
        self.qr_clear_btn.config(state=tk.DISABLED)
        self.refresh_qr_btn.config(state=tk.DISABLED)
        
        # Start deletion in a separate thread
        threading.Thread(target=self._clear_texts_thread, args=(user_id,), daemon=True).start()

# Keep the original functions for Firebase operations
def setup_firebase():
    """Initialize Firebase Admin SDK"""
    # The service account key should be in the same directory as this script
    if os.path.exists('serviceAccountKey.json'):
        cred = credentials.Certificate('serviceAccountKey.json')
        firebase_admin.initialize_app(cred)
    else:
        raise FileNotFoundError(
            "serviceAccountKey.json not found. Please download it from Firebase Console:\n"
            "1. Go to Firebase Console\n"
            "2. Project Settings > Service Accounts\n"
            "3. Generate New Private Key\n"
            "4. Save as 'serviceAccountKey.json' in the same directory as this script"
        )

def get_extracted_texts(user_id):
    """Get all extracted texts for a specific user"""
    db = firestore.client()
    current_time = int(datetime.now().timestamp() * 1000)  # Current time in milliseconds
    
    # Query using existing index (userId + timestamp + __name__)
    docs = db.collection('extracted_texts')\
        .where('userId', '==', user_id)\
        .order_by('timestamp', direction=firestore.Query.DESCENDING)\
        .stream()
    
    # Filter expired documents in memory
    texts = []
    for doc in docs:
        data = doc.to_dict()
        # Only include documents that haven't expired
        if data.get('expirationTime', 0) > current_time:
            # Add remaining time information
            remaining_seconds = (data['expirationTime'] - current_time) // 1000
            remaining_minutes = remaining_seconds // 60
            data['expiresIn'] = str(remaining_minutes) + " minutes" if remaining_minutes > 0 else "less than a minute"
            # Add document ID for deletion
            data['_id'] = doc.id
            # Ensure sourceType is set (default to 'uploaded' if not specified)
            if 'sourceType' not in data:
                data['sourceType'] = 'uploaded'
            texts.append(data)
    
    return texts

def delete_all_texts(user_id):
    """Delete all extracted texts for a specific user"""
    db = firestore.client()
    total_count = 0
    
    # Delete from 'extracted_texts' collection
    count = _delete_from_collection(db, 'extracted_texts', user_id)
    total_count += count
    
    # Also try to delete from 'text_extractions' collection (for uploaded images)
    try:
        alt_count = _delete_from_collection(db, 'text_extractions', user_id)
        total_count += alt_count
    except Exception as e:
        print(f"Error deleting from text_extractions collection: {e}")
    
    # Note: We don't delete from medical_analyses as those are important medical records
    # Just inform the user about this
    try:
        # Just count medical analyses to inform the user
        medical_docs = db.collection('medical_analyses')\
            .where('patientId', '==', user_id)\
            .stream()
        
        medical_count = sum(1 for _ in medical_docs)
        if medical_count > 0:
            print(f"Note: {medical_count} medical analyses were not deleted as they are permanent records.")
    except Exception as e:
        print(f"Error counting medical analyses: {e}")
    
    return total_count

def _delete_from_collection(db, collection_name, user_id):
    """Helper function to delete documents from a specific collection"""
    # Query all documents for this user
    docs = db.collection(collection_name)\
        .where('userId', '==', user_id)\
        .stream()
    
    # Count documents and delete them
    count = 0
    batch = db.batch()
    batch_size = 0
    max_batch_size = 500  # Firestore batch limit
    
    for doc in docs:
        batch.delete(doc.reference)
        count += 1
        batch_size += 1
        
        # If batch is full, commit it and start a new one
        if batch_size >= max_batch_size:
            batch.commit()
            batch = db.batch()
            batch_size = 0
    
    # Commit any remaining deletes
    if batch_size > 0:
        batch.commit()
    
    return count

def save_texts_to_files(texts, output_dir):
    """Save extracted texts to files in the specified directory"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Group texts by source type
    texts_by_type = {}
    for text in texts:
        source_type = text.get('sourceType', 'unknown')
        if source_type not in texts_by_type:
            texts_by_type[source_type] = []
        texts_by_type[source_type].append(text)
    
    # Save each type to its own directory
    for source_type, type_texts in texts_by_type.items():
        type_dir = os.path.join(output_dir, source_type)
        if not os.path.exists(type_dir):
            os.makedirs(type_dir)
        
        # Save individual text files
        for text in type_texts:
            # Handle timestamp which might be in different formats
            if isinstance(text.get('timestamp'), int):
                timestamp = datetime.fromtimestamp(text['timestamp'] / 1000)
            elif isinstance(text.get('timestamp'), datetime):
                timestamp = text['timestamp']
            else:
                timestamp = datetime.now()
                
            filename = text.get('fileName', None)
            if filename:
                # Remove extension and add our own
                filename = os.path.splitext(filename)[0]
                save_name = filename + "_" + timestamp.strftime('%Y%m%d_%H%M%S') + ".txt"
            else:
                save_name = "extracted_" + timestamp.strftime('%Y%m%d_%H%M%S') + ".txt"
            
            filepath = os.path.join(type_dir, save_name)
            
            # Save the text content
            with open(filepath, 'w', encoding='utf-8') as f:
                # For medical reports, add a formatted header
                if source_type == 'medical_report':
                    f.write("=== MEDICAL REPORT ANALYSIS ===\n\n")
                    
                    # Add critical findings if available
                    if 'criticalFindings' in text and text['criticalFindings']:
                        f.write("CRITICAL FINDINGS:\n")
                        for i, finding in enumerate(text['criticalFindings'], 1):
                            f.write(f"{i}. 🚨 {finding}\n")
                        f.write("\n")
                    
                    # Add recommendations if available
                    if 'recommendations' in text and text['recommendations']:
                        f.write("RECOMMENDATIONS:\n")
                        for i, rec in enumerate(text['recommendations'], 1):
                            f.write(f"{i}. {rec}\n")
                        f.write("\n")
                    
                    f.write("FULL ANALYSIS:\n")
                    f.write("====================\n\n")
                
                # Write the main text content
                f.write(text['text'])
            
            # Save metadata separately
            metadata = {
                'documentId': text.get('documentId', text.get('_id', '')),
                'timestamp': text.get('timestamp', ''),
                'sourceType': source_type,
                'originalFileName': text.get('fileName', None),
                'extractedAt': timestamp.isoformat()
            }
            
            # Add expiration information if available
            if 'expirationTime' in text:
                expiration_time = datetime.fromtimestamp(text['expirationTime'] / 1000)
                metadata['expiresAt'] = expiration_time.isoformat()
                metadata['expiresIn'] = text.get('expiresIn', 'unknown')
            elif 'expiresIn' in text:
                metadata['expiresIn'] = text['expiresIn']
            
            # Add medical report specific metadata
            if source_type == 'medical_report':
                if 'criticalFindings' in text:
                    metadata['criticalFindings'] = text['criticalFindings']
                if 'recommendations' in text:
                    metadata['recommendations'] = text['recommendations']
                if 'fileCount' in text:
                    metadata['fileCount'] = text['fileCount']
                if 'fileTypes' in text:
                    metadata['fileTypes'] = text['fileTypes']
            
            metadata_path = os.path.splitext(filepath)[0] + '_metadata.json'
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)

def main():
    """Main function to start the GUI application"""
    root = tk.Tk()
    app = TextExtractionApp(root)
    root.mainloop()

if __name__ == '__main__':
    main() 