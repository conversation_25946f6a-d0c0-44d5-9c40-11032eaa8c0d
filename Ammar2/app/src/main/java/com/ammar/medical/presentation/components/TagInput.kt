package com.ammar.medical.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.ammar.medical.R

@Composable
fun TagInput(
    tags: Set<String>,
    suggestions: Set<String>,
    onTagsChange: (Set<String>) -> Unit,
    modifier: Modifier = Modifier
) {
    var showSuggestions by remember { mutableStateOf(false) }
    var inputText by remember { mutableStateOf("") }
    var filteredSuggestions by remember { mutableStateOf(suggestions) }

    Column(modifier = modifier) {
        // Existing tags
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            tags.forEach { tag ->
                AssistChip(
                    onClick = { onTagsChange(tags - tag) },
                    label = { Text(tag) },
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = stringResource(R.string.remove_tag)
                        )
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Tag input field
        OutlinedTextField(
            value = inputText,
            onValueChange = { text ->
                inputText = text
                filteredSuggestions = suggestions.filter { it.contains(text, ignoreCase = true) }
                showSuggestions = text.isNotEmpty()
            },
            label = { Text(stringResource(R.string.add_tag)) },
            trailingIcon = {
                if (inputText.isNotEmpty()) {
                    IconButton(onClick = { inputText = "" }) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = stringResource(R.string.clear)
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        )

        // Suggestions dropdown
        if (showSuggestions && filteredSuggestions.isNotEmpty()) {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.surface,
                tonalElevation = 2.dp
            ) {
                Column {
                    filteredSuggestions.forEach { suggestion ->
                        ListItem(
                            headlineContent = { Text(suggestion) },
                            leadingContent = {
                                Icon(
                                    imageVector = Icons.Default.LocalOffer,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            },
                            modifier = Modifier.clickable {
                                if (!tags.contains(suggestion)) {
                                    onTagsChange(tags + suggestion)
                                }
                                inputText = ""
                                showSuggestions = false
                            }
                        )
                    }
                }
            }
        }
    }
} 