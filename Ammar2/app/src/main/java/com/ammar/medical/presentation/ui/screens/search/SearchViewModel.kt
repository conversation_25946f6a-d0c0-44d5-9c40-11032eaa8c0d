package com.ammar.medical.presentation.ui.screens.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Category
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.CategoryRepository
import com.ammar.medical.data.repository.DocumentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

sealed class SearchUiState {
    data object Loading : SearchUiState()
    data class Success(
        val documents: List<Document>,
        val query: String = "",
        val selectedTypes: Set<Document.DocumentType> = emptySet(),
        val selectedTags: Set<String> = emptySet(),
        val selectedCategories: Set<Long> = emptySet(),
        val dateRange: ClosedFloatingPointRange<Float> = 0f..1f,
        val sortOption: SortOption = SortOption.DATE_DESC,
        val availableTags: List<String> = emptyList(),
        val availableCategories: List<Category> = emptyList(),
        val showFilters: Boolean = false,
        val error: String? = null
    ) : SearchUiState()
    data class Error(val message: String) : SearchUiState()
}

enum class SortOption {
    DATE_DESC, DATE_ASC, TITLE_ASC, TITLE_DESC, TYPE_ASC, TYPE_DESC
}

@HiltViewModel
class SearchViewModel @Inject constructor(
    private val documentRepository: DocumentRepository,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<SearchUiState>(SearchUiState.Loading)
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    init {
        loadData()
    }

    private fun loadData() {
        viewModelScope.launch {
            try {
                combine(
                    documentRepository.getAllDocuments(),
                    categoryRepository.categories,
                    documentRepository.getAllTags()
                ) { documents, categories, tags ->
                    SearchUiState.Success(
                        documents = documents,
                        availableCategories = categories,
                        availableTags = tags
                    )
                }.collect { state ->
                    _uiState.value = state
                }
            } catch (e: Exception) {
                _uiState.value = SearchUiState.Error(e.message ?: "Failed to load data")
            }
        }
    }

    fun updateQuery(query: String) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(query = query)
        search()
    }

    fun toggleDocumentType(type: Document.DocumentType) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedTypes = if (type in currentState.selectedTypes) {
                currentState.selectedTypes - type
            } else {
                currentState.selectedTypes + type
            }
        )
        search()
    }

    fun toggleTag(tag: String) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedTags = if (tag in currentState.selectedTags) {
                currentState.selectedTags - tag
            } else {
                currentState.selectedTags + tag
            }
        )
        search()
    }

    fun toggleCategory(categoryId: Long) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedCategories = if (categoryId in currentState.selectedCategories) {
                currentState.selectedCategories - categoryId
            } else {
                currentState.selectedCategories + categoryId
            }
        )
        search()
    }

    fun updateDateRange(range: ClosedFloatingPointRange<Float>) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(dateRange = range)
        search()
    }

    fun updateSortOption(option: SortOption) {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(sortOption = option)
        search()
    }

    fun toggleFilters() {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(showFilters = !currentState.showFilters)
    }

    private fun search() {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        viewModelScope.launch {
            try {
                val filteredDocuments = documentRepository.searchDocuments(
                    query = currentState.query,
                    types = currentState.selectedTypes,
                    tags = currentState.selectedTags,
                    categories = currentState.selectedCategories,
                    dateRange = currentState.dateRange,
                    sortOption = currentState.sortOption
                )
                _uiState.value = currentState.copy(
                    documents = filteredDocuments,
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = currentState.copy(
                    error = e.message ?: "Failed to search documents"
                )
            }
        }
    }

    fun clearFilters() {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedTypes = emptySet(),
            selectedTags = emptySet(),
            selectedCategories = emptySet(),
            dateRange = 0f..1f,
            sortOption = SortOption.DATE_DESC
        )
        search()
    }

    fun clearError() {
        val currentState = _uiState.value as? SearchUiState.Success ?: return
        _uiState.value = currentState.copy(error = null)
    }
} 