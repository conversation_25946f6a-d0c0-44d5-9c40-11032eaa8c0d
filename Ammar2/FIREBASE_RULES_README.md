# Firebase Security Rules Fix

## Problem
The app is experiencing permission errors when trying to send or check messages. The error message "Failed to verify recipient" with "permission denied" indicates that the Firebase security rules are not properly configured to allow the messaging functionality.

## Solution
You need to update your Firebase security rules in the Firebase Console to allow users to:
1. Read other users' documents (to verify recipients)
2. Create and read messages
3. Update messages (to mark them as read)

## Steps to Fix

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to "Firestore Database" in the left sidebar
4. Click on the "Rules" tab
5. Replace the current rules with the following:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own documents
    match /users/{userId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && 
                    (request.auth.uid == userId || 
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'lastLogin']));
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow users to send and receive messages
    match /messages/{messageId} {
      allow create: if request.auth != null && 
                     request.resource.data.senderUid == request.auth.uid;
      
      allow read: if request.auth != null && 
                   (resource.data.senderUid == request.auth.uid || 
                    resource.data.recipientUid == request.auth.uid);
      
      allow update: if request.auth != null && 
                     resource.data.recipientUid == request.auth.uid &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
      
      allow delete: if request.auth != null && 
                     (resource.data.senderUid == request.auth.uid || 
                      resource.data.recipientUid == request.auth.uid);
    }
    
    // Allow users to manage their contacts
    match /users/{userId}/contacts/{contactId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow create, update: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

6. Click "Publish" to apply the new rules

## Explanation of Rules

- **Users Collection**:
  - Any authenticated user can read any user document (needed to verify recipients)
  - Users can only write to their own documents

- **Messages Collection**:
  - Users can create messages only if they set themselves as the sender
  - Users can read messages only if they are either the sender or recipient
  - Recipients can update messages, but only to change the "read" field
  - Both sender and recipient can delete messages

- **Contacts Collection**:
  - Only authenticated users can read their own contacts
  - Only authenticated users can create and update their own contacts
  - Only authenticated users can delete their own contacts

## Testing

After updating the rules, you should be able to:
1. Send messages to other users
2. View messages in your inbox
3. Messages should be automatically marked as read when viewed

If you continue to experience issues, check the Firebase console logs for more detailed error messages. 