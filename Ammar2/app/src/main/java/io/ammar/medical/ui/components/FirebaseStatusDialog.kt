package io.ammar.medical.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils
import kotlinx.coroutines.launch

/**
 * A dialog that displays Firebase connectivity status
 */
@Composable
fun FirebaseStatusDialog(
    onDismiss: () -> Unit,
    onRetry: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    var isLoading by remember { mutableStateOf(true) }
    var connectivityResult by remember { mutableStateOf<FirebaseUtils.FirebaseConnectivityResult?>(null) }
    var isNetworkAvailable by remember { mutableStateOf(NetworkUtils.isNetworkAvailable(context)) }
    
    // Check Firebase connectivity when dialog is shown
    LaunchedEffect(Unit) {
        isLoading = true
        isNetworkAvailable = NetworkUtils.isNetworkAvailable(context)
        
        if (isNetworkAvailable) {
            try {
                connectivityResult = FirebaseUtils.checkFirebaseConnectivity(context)
            } catch (e: Exception) {
                // Handle error
            }
        }
        
        isLoading = false
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Firebase Connectivity Status") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                if (!isNetworkAvailable) {
                    Text(
                        "No network connection available. Please check your internet connection.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "Checking Firebase connectivity...",
                        style = MaterialTheme.typography.bodyMedium
                    )
                } else if (connectivityResult != null) {
                    val result = connectivityResult!!
                    
                    StatusItem(
                        title = "Network Available",
                        status = isNetworkAvailable
                    )
                    
                    StatusItem(
                        title = "Firebase Initialized",
                        status = result.isInitialized
                    )
                    
                    StatusItem(
                        title = "Firebase Hosts Reachable",
                        status = result.isFirebaseHostReachable
                    )
                    
                    StatusItem(
                        title = "Firestore Connected",
                        status = result.isFirestoreConnected,
                        errorMessage = result.firestoreError
                    )
                    
                    StatusItem(
                        title = "Authentication Connected",
                        status = result.isAuthConnected,
                        errorMessage = result.authError
                    )
                    
                    StatusItem(
                        title = "FCM Connected",
                        status = result.isFcmConnected,
                        errorMessage = result.fcmError
                    )
                    
                    Divider(modifier = Modifier.padding(vertical = 8.dp))
                    
                    Text(
                        text = "Overall Status: ${if (result.isFullyConnected) "Connected" else "Issues Detected"}",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold,
                        color = if (result.isFullyConnected) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.error
                    )
                    
                    if (!result.isFullyConnected) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Troubleshooting Tips:",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "• Check your internet connection\n" +
                                   "• Ensure you have the latest app version\n" +
                                   "• Try restarting the app\n" +
                                   "• If problems persist, contact support",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                } else {
                    Text(
                        "Failed to check Firebase connectivity.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    scope.launch {
                        isLoading = true
                        isNetworkAvailable = NetworkUtils.isNetworkAvailable(context)
                        
                        if (isNetworkAvailable) {
                            try {
                                connectivityResult = FirebaseUtils.checkFirebaseConnectivity(context)
                                onRetry()
                            } catch (e: Exception) {
                                // Handle error
                            }
                        }
                        
                        isLoading = false
                    }
                }
            ) {
                Text("Retry")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

@Composable
private fun StatusItem(
    title: String,
    status: Boolean,
    errorMessage: String? = null
) {
    Column(modifier = Modifier.padding(vertical = 4.dp)) {
        Text(
            text = "$title: ${if (status) "✓" else "✗"}",
            style = MaterialTheme.typography.bodyMedium,
            color = if (status) 
                MaterialTheme.colorScheme.primary 
            else 
                MaterialTheme.colorScheme.error
        )
        
        if (!status && !errorMessage.isNullOrEmpty()) {
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
} 