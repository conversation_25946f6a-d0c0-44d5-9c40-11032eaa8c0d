package io.ammar.medical.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import io.ammar.medical.data.SavedDoctor
import io.ammar.medical.viewmodel.DoctorOperationState
import io.ammar.medical.viewmodel.MedicalAnalysisViewModel
import kotlinx.coroutines.launch

@Composable
fun SavedDoctorsList(
    viewModel: MedicalAnalysisViewModel,
    onDoctorSelected: (SavedDoctor) -> Unit = {},
    selectionMode: Boolean = false,
    onSelectionDone: () -> Unit = {}
) {
    val savedDoctors by viewModel.savedDoctors.collectAsState()
    val selectedDoctors by viewModel.selectedDoctors.collectAsState()
    val operationState by viewModel.doctorOperationState.collectAsState()
    
    var showAddDoctorDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf<SavedDoctor?>(null) }
    
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = if (selectionMode) "Select Doctors" else "Your Doctors",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            if (selectionMode) {
                Button(
                    onClick = onSelectionDone,
                    enabled = selectedDoctors.isNotEmpty()
                ) {
                    Text("Done (${selectedDoctors.size})")
                }
            } else {
                IconButton(onClick = { showAddDoctorDialog = true }) {
                    Icon(Icons.Default.Add, contentDescription = "Add Doctor")
                }
            }
        }
        
        // Doctors list
        if (savedDoctors.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No saved doctors yet. Add your first doctor!",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(savedDoctors) { doctor ->
                    val isSelected = selectedDoctors.any { it.doctorId == doctor.doctorId }
                    
                    DoctorItem(
                        doctor = doctor,
                        isSelected = isSelected,
                        selectionMode = selectionMode,
                        onSelect = {
                            if (selectionMode) {
                                viewModel.toggleDoctorSelection(doctor)
                            } else {
                                onDoctorSelected(doctor)
                            }
                        },
                        onDelete = {
                            showDeleteConfirmation = doctor
                        }
                    )
                }
            }
        }
        
        // Loading indicator
        if (operationState is DoctorOperationState.Loading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
        
        // Error message
        if (operationState is DoctorOperationState.Error) {
            val errorMessage = (operationState as DoctorOperationState.Error).message
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
    
    // Add doctor dialog
    if (showAddDoctorDialog) {
        AddDoctorDialog(
            onDismiss = { showAddDoctorDialog = false },
            onSave = { name, email, specialty ->
                scope.launch {
                    viewModel.saveDoctor(name, email, specialty)
                    showAddDoctorDialog = false
                }
            }
        )
    }
    
    // Delete confirmation dialog
    showDeleteConfirmation?.let { doctor ->
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = null },
            title = { Text("Remove Doctor") },
            text = { Text("Are you sure you want to remove Dr. ${doctor.doctorName} from your saved doctors?") },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.removeDoctor(doctor.doctorId)
                        showDeleteConfirmation = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Remove")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmation = null }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
fun DoctorItem(
    doctor: SavedDoctor,
    isSelected: Boolean = false,
    selectionMode: Boolean = false,
    onSelect: () -> Unit = {},
    onDelete: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onSelect),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Doctor avatar or selection indicator
            if (selectionMode) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(
                            if (isSelected) 
                                MaterialTheme.colorScheme.primary 
                            else 
                                MaterialTheme.colorScheme.surfaceVariant
                        )
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline,
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (isSelected) {
                        Icon(
                            Icons.Default.Check,
                            contentDescription = "Selected",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            } else {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primaryContainer),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = "Doctor",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
            
            // Doctor info
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp)
            ) {
                Text(
                    text = "Dr. ${doctor.doctorName}",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Text(
                    text = doctor.doctorEmail,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (doctor.doctorSpecialty.isNotEmpty()) {
                    Text(
                        text = doctor.doctorSpecialty,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.tertiary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // Delete button (only in non-selection mode)
            if (!selectionMode) {
                IconButton(onClick = onDelete) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "Delete",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

@Composable
fun AddDoctorDialog(
    onDismiss: () -> Unit,
    onSave: (name: String, email: String, specialty: String) -> Unit
) {
    var doctorName by remember { mutableStateOf("") }
    var doctorEmail by remember { mutableStateOf("") }
    var doctorSpecialty by remember { mutableStateOf("") }
    
    var nameError by remember { mutableStateOf(false) }
    var emailError by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .fillMaxWidth()
            ) {
                Text(
                    text = "Add New Doctor",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Doctor name
                OutlinedTextField(
                    value = doctorName,
                    onValueChange = { 
                        doctorName = it
                        nameError = it.isBlank()
                    },
                    label = { Text("Doctor Name") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = nameError,
                    supportingText = {
                        if (nameError) {
                            Text("Name is required")
                        }
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Doctor email
                OutlinedTextField(
                    value = doctorEmail,
                    onValueChange = { 
                        doctorEmail = it
                        emailError = !isValidEmail(it)
                    },
                    label = { Text("Email") },
                    modifier = Modifier.fillMaxWidth(),
                    isError = emailError,
                    supportingText = {
                        if (emailError) {
                            Text("Valid email is required")
                        }
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Doctor specialty (optional)
                OutlinedTextField(
                    value = doctorSpecialty,
                    onValueChange = { doctorSpecialty = it },
                    label = { Text("Specialty (optional)") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            nameError = doctorName.isBlank()
                            emailError = !isValidEmail(doctorEmail)
                            
                            if (!nameError && !emailError) {
                                onSave(doctorName, doctorEmail, doctorSpecialty)
                            }
                        }
                    ) {
                        Text("Save")
                    }
                }
            }
        }
    }
}

private fun isValidEmail(email: String): Boolean {
    return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() && email.isNotEmpty()
} 