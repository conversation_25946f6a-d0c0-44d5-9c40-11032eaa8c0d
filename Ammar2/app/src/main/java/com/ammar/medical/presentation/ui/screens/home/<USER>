package com.ammar.medical.presentation.ui.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.DocumentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val documentRepository: DocumentRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<HomeUiState>(HomeUiState.Loading)
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private var currentSearchQuery = ""

    init {
        loadDocuments()
    }

    private fun loadDocuments() {
        viewModelScope.launch {
            try {
                documentRepository.getAllDocuments()
                    .catch { e ->
                        _uiState.value = HomeUiState.Error(e.message ?: "Unknown error occurred")
                    }
                    .collect { documents ->
                        _uiState.value = HomeUiState.Success(documents)
                    }
            } catch (e: Exception) {
                _uiState.value = HomeUiState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    fun searchDocuments(query: String) {
        currentSearchQuery = query
        viewModelScope.launch {
            try {
                documentRepository.searchDocuments(query)
                    .catch { e ->
                        _uiState.value = HomeUiState.Error(e.message ?: "Unknown error occurred")
                    }
                    .collect { documents ->
                        _uiState.value = HomeUiState.Success(documents)
                    }
            } catch (e: Exception) {
                _uiState.value = HomeUiState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    fun clearSearch() {
        currentSearchQuery = ""
        loadDocuments()
    }

    fun syncUnsyncedDocuments() {
        viewModelScope.launch {
            try {
                documentRepository.syncUnsyncedDocuments()
            } catch (e: Exception) {
                // Handle sync error
            }
        }
    }
}

sealed class HomeUiState {
    object Loading : HomeUiState()
    data class Success(val documents: List<Document>) : HomeUiState()
    data class Error(val message: String) : HomeUiState()
} 