package io.ammar.medical.utils

import android.content.Context
import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Manager class for handling offline mode throughout the app
 */
class OfflineModeManager(
    private val context: Context,
    private val coroutineScope: CoroutineScope
) {
    private val TAG = "OfflineModeManager"
    
    // Firebase instances
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    
    // Offline mode state
    private val _isOfflineMode = MutableStateFlow(false)
    val isOfflineMode: StateFlow<Boolean> = _isOfflineMode.asStateFlow()
    
    // Banner visibility state
    private val _showOfflineBanner = MutableStateFlow(false)
    val showOfflineBanner: StateFlow<Boolean> = _showOfflineBanner.asStateFlow()
    
    // Network state
    private val _isNetworkAvailable = MutableStateFlow(false)
    val isNetworkAvailable: StateFlow<Boolean> = _isNetworkAvailable.asStateFlow()
    
    init {
        // Initialize Firestore for offline persistence
        setupFirestoreOfflinePersistence()
        
        // Start monitoring network state
        monitorNetworkState()
        
        // Ensure user is authenticated
        coroutineScope.launch {
            ensureAuthentication()
        }
    }
    
    /**
     * Set up Firestore for offline persistence
     */
    private fun setupFirestoreOfflinePersistence() {
        try {
            val settings = com.google.firebase.firestore.FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(true)
                .setCacheSizeBytes(com.google.firebase.firestore.FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                .build()
            
            firestore.firestoreSettings = settings
            Log.d(TAG, "Firestore offline persistence enabled")
        } catch (e: Exception) {
            Log.e(TAG, "Error enabling Firestore offline persistence", e)
        }
    }
    
    /**
     * Monitor network state and update offline mode accordingly
     */
    private fun monitorNetworkState() {
        coroutineScope.launch {
            NetworkUtils.observeNetworkStatus(context).collectLatest { isConnected ->
                _isNetworkAvailable.value = isConnected
                
                if (isConnected) {
                    // Network is available, check if we can connect to Firebase
                    checkFirebaseConnection()
                } else {
                    // Network is unavailable, enter offline mode
                    enterOfflineMode("No network connection")
                }
            }
        }
    }
    
    /**
     * Ensure the user is authenticated, using anonymous auth if needed
     */
    private suspend fun ensureAuthentication() {
        try {
            // Check if user is already authenticated
            if (auth.currentUser != null) {
                Log.d(TAG, "User already authenticated: ${auth.currentUser?.uid}")
                return
            }
            
            // Try to sign in anonymously with timeout
            Log.d(TAG, "No user authenticated, attempting anonymous sign-in")
            val authResult = withTimeoutOrNull(10000) { // 10 seconds timeout
                auth.signInAnonymously().await()
            }
            
            if (authResult != null) {
                Log.d(TAG, "Anonymous authentication successful: ${authResult.user?.uid}")
                
                // Create user document in Firestore
                authResult.user?.let { user ->
                    try {
                        // Check if user document exists
                        val userDoc = firestore.collection("users").document(user.uid).get().await()
                        
                        if (!userDoc.exists()) {
                            // Create new user document
                            val userData = hashMapOf(
                                "id" to user.uid,
                                "username" to "Anonymous User",
                                "isAnonymous" to true,
                                "createdAt" to System.currentTimeMillis(),
                                "lastLogin" to System.currentTimeMillis()
                            )
                            
                            firestore.collection("users").document(user.uid).set(userData).await()
                            Log.d(TAG, "Created new user document for anonymous user")
                        } else {
                            // Update last login
                            firestore.collection("users").document(user.uid)
                                .update("lastLogin", System.currentTimeMillis())
                                .await()
                            Log.d(TAG, "Updated last login for existing user")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error creating/updating user document", e)
                        // Continue anyway, this is not critical
                    }
                }
            } else {
                Log.w(TAG, "Anonymous authentication timed out")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during authentication", e)
        }
    }
    
    /**
     * Check if we can connect to Firebase
     */
    private suspend fun checkFirebaseConnection() {
        try {
            // First, do a quick check with a Firestore operation
            val isFirestoreWorking = testFirestoreConnection()
            
            if (isFirestoreWorking) {
                // If Firestore is working, we're online
                exitOfflineMode()
                
                // Ensure user is authenticated when we're online
                ensureAuthentication()
                return
            }
            
            // If the quick check failed, do a more thorough check
            val connectivityResult = FirebaseUtils.checkFirebaseConnectivity(context)
            Log.d(TAG, "Firebase connectivity check result: $connectivityResult")
            
            // Consider the app online if either Firestore or Auth is connected
            if (connectivityResult.isFirestoreConnected || connectivityResult.isAuthConnected) {
                exitOfflineMode()
                
                // Ensure user is authenticated when we're online
                ensureAuthentication()
            } else {
                enterOfflineMode("Firebase services unavailable")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Firebase connection", e)
            enterOfflineMode("Error checking Firebase connection")
        }
    }
    
    /**
     * Test if Firestore is working with a simple operation
     */
    private suspend fun testFirestoreConnection(): Boolean {
        return try {
            withTimeoutOrNull(5000) { // 5 second timeout
                val db = FirebaseFirestore.getInstance()
                // Try to get a document that should always exist (or at least the query should work)
                val result = db.collection("users").limit(1).get().await()
                true // If we get here, Firestore is working
            } ?: false // Timeout occurred
        } catch (e: Exception) {
            Log.e(TAG, "Error testing Firestore connection", e)
            false
        }
    }
    
    /**
     * Enter offline mode
     */
    private fun enterOfflineMode(reason: String) {
        Log.d(TAG, "Entering offline mode: $reason")
        _isOfflineMode.value = true
        _showOfflineBanner.value = true
    }
    
    /**
     * Exit offline mode
     */
    private fun exitOfflineMode() {
        Log.d(TAG, "Exiting offline mode")
        _isOfflineMode.value = false
        // Also hide the banner when exiting offline mode
        _showOfflineBanner.value = false
    }
    
    /**
     * Dismiss the offline banner
     */
    fun dismissOfflineBanner() {
        _showOfflineBanner.value = false
    }
    
    /**
     * Retry connection to Firebase
     */
    fun retryConnection() {
        coroutineScope.launch {
            val isNetworkAvailable = NetworkUtils.isNetworkAvailable(context)
            _isNetworkAvailable.value = isNetworkAvailable
            
            if (isNetworkAvailable) {
                // Network is available, check if we can connect to Firebase
                checkFirebaseConnection()
            } else {
                // Network is unavailable, stay in offline mode
                enterOfflineMode("No network connection")
            }
        }
    }
    
    /**
     * Check if a Firestore operation can be performed
     * Returns true if online or if the operation can be performed offline
     */
    suspend fun canPerformFirestoreOperation(collection: String): Boolean {
        // If we're online, we can perform any operation
        if (!_isOfflineMode.value) {
            return true
        }
        
        // If we're offline, check if we have cached data for this collection
        return withContext(Dispatchers.IO) {
            try {
                val db = FirebaseFirestore.getInstance()
                val query = db.collection(collection).limit(1)
                
                // Try to get from cache
                val result = query.get(com.google.firebase.firestore.Source.CACHE).await()
                
                // If we got a result (even empty), we can perform operations on this collection
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error checking cached data for $collection", e)
                false
            }
        }
    }
} 