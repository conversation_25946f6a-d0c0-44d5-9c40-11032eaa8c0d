package com.ammar.medical.domain.service

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.GenerateContentResponse
import com.google.ai.client.generativeai.type.content
import com.google.ai.client.generativeai.type.generationConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DocumentAnalysisService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val generativeModel: GenerativeModel
) {
    suspend fun analyzeDocument(uri: Uri): DocumentAnalysisResult = withContext(Dispatchers.IO) {
        try {
            // Load image from URI
            val bitmap = loadImageFromUri(uri)
            
            // Prepare content for analysis
            val content = content {
                image(bitmap)
                text("Analyze this medical document and extract the following information: " +
                     "1. Document type (e.g., prescription, medical report, etc.) " +
                     "2. Patient information " +
                     "3. Doctor information " +
                     "4. Date " +
                     "5. Key medical details " +
                     "Please format the response in a structured way.")
            }

            // Generate analysis
            val response = generativeModel.generateContent(
                generationConfig = generationConfig {
                    temperature = 0.4f
                    topK = 32
                    topP = 1f
                },
                content = content
            )

            // Process response
            processResponse(response)
        } catch (e: Exception) {
            DocumentAnalysisResult.Error(e.message ?: "Unknown error occurred")
        }
    }

    private suspend fun loadImageFromUri(uri: Uri): Bitmap = withContext(Dispatchers.IO) {
        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            BitmapFactory.decodeStream(inputStream)
        } ?: throw IllegalStateException("Failed to load image from URI")
    }

    private fun processResponse(response: GenerateContentResponse): DocumentAnalysisResult {
        return try {
            val text = response.text ?: throw IllegalStateException("Empty response from Gemini AI")
            
            // Parse the structured response
            val documentType = extractDocumentType(text)
            val patientInfo = extractPatientInfo(text)
            val doctorInfo = extractDoctorInfo(text)
            val date = extractDate(text)
            val medicalDetails = extractMedicalDetails(text)

            DocumentAnalysisResult.Success(
                documentType = documentType,
                patientInfo = patientInfo,
                doctorInfo = doctorInfo,
                date = date,
                medicalDetails = medicalDetails
            )
        } catch (e: Exception) {
            DocumentAnalysisResult.Error(e.message ?: "Failed to process analysis result")
        }
    }

    private fun extractDocumentType(text: String): String {
        // TODO: Implement document type extraction
        return "Unknown"
    }

    private fun extractPatientInfo(text: String): Map<String, String> {
        // TODO: Implement patient info extraction
        return emptyMap()
    }

    private fun extractDoctorInfo(text: String): Map<String, String> {
        // TODO: Implement doctor info extraction
        return emptyMap()
    }

    private fun extractDate(text: String): String {
        // TODO: Implement date extraction
        return ""
    }

    private fun extractMedicalDetails(text: String): List<String> {
        // TODO: Implement medical details extraction
        return emptyList()
    }
}

sealed class DocumentAnalysisResult {
    data class Success(
        val documentType: String,
        val patientInfo: Map<String, String>,
        val doctorInfo: Map<String, String>,
        val date: String,
        val medicalDetails: List<String>
    ) : DocumentAnalysisResult()

    data class Error(val message: String) : DocumentAnalysisResult()
} 