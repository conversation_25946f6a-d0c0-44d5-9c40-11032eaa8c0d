#!/bin/bash

# Directory with the new files
NEW_DIR="app/src/main/java/io/ammar/medical"

# Update Firebase service references
for file in $(find "$NEW_DIR" -name "*.kt" -type f); do
    # Replace any hardcoded references to the old package in Firebase-related code
    sed -i '' 's/com\.example\.ammar2\.notifications\.FirebaseMessagingService/io.ammar.medical.notifications.FirebaseMessagingService/g' "$file"
    sed -i '' 's/com\.example\.ammar2\.auth\./io.ammar.medical.auth./g' "$file"
    sed -i '' 's/com\.example\.ammar2\.data\./io.ammar.medical.data./g' "$file"
    sed -i '' 's/"com\.example\.ammar2"/"io.ammar.medical"/g' "$file"
done

echo "Firebase references updated in all Kotlin files" 