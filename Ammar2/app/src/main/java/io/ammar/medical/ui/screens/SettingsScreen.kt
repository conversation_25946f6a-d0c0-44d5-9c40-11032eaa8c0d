package io.ammar.medical.ui.screens

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.background
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.ammar.medical.R
import io.ammar.medical.viewmodel.SettingsViewModel
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import android.widget.Toast

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    settingsViewModel: SettingsViewModel = viewModel()
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val scrollState = rememberScrollState()
    val scope = rememberCoroutineScope()
    
    // Load settings on first composition
    LaunchedEffect(Unit) {
        settingsViewModel.loadSettings(context)
    }
    
    // Collect state from ViewModel
    val apiKey by settingsViewModel.apiKey.collectAsState()
    val isUsingDefaultKey by settingsViewModel.isUsingDefaultKey.collectAsState()
    val isValidatingApiKey by settingsViewModel.isValidatingApiKey.collectAsState()
    val apiKeyValidationError by settingsViewModel.apiKeyValidationError.collectAsState()
    val apiKeyValidationSuccess by settingsViewModel.apiKeyValidationSuccess.collectAsState()
    
    // Get activation status
    val activationCode by settingsViewModel.activationCode.collectAsState()
    val isActivated by settingsViewModel.isActivated.collectAsState()
    val activationError by settingsViewModel.activationError.collectAsState()
    val isLoadingActivationCodes by settingsViewModel.isLoadingActivationCodes.collectAsState()
    
    // Local state for form
    var newApiKey by remember { mutableStateOf("") }
    var showApiKey by remember { mutableStateOf(false) }
    var newActivationCode by remember { mutableStateOf("") }
    
    // Reset the form when apiKey changes
    LaunchedEffect(apiKey) {
        newApiKey = apiKey ?: ""
    }
    
    // Reset activation form when activationCode changes
    LaunchedEffect(activationCode) {
        newActivationCode = activationCode ?: ""
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack, 
                            contentDescription = stringResource(R.string.back),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // App version
            Text(
                text = stringResource(R.string.app_version, "1.2"),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.align(Alignment.End)
            )
            
            // Premium Features Section
            Text(
                text = stringResource(R.string.premium_features),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            // Activation Card
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.premium_features_description),
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        
                        if (isLoadingActivationCodes) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            IconButton(onClick = { 
                                settingsViewModel.refreshActivationCodes(context)
                            }) {
                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = stringResource(R.string.refresh_activation),
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Activation status
                    if (isActivated) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = stringResource(R.string.activation_success),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                        Text(
                            text = "Code: $activationCode",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    } else {
                        // Activation input
                        OutlinedTextField(
                            value = newActivationCode,
                            onValueChange = { newActivationCode = it },
                            label = { Text(stringResource(R.string.activation_code)) },
                            placeholder = { Text(stringResource(R.string.enter_activation_code)) },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            isError = activationError != null,
                            enabled = !isLoadingActivationCodes
                        )
                        
                        if (activationError != null) {
                            Text(
                                text = activationError!!,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            // Use a simpler approach for debug button - always show in this case
                            TextButton(onClick = {
                                // Show diagnostic info as a toast or dialog
                                val activationStatus = settingsViewModel.getActivationDiagnostics(context)
                                Toast.makeText(context, activationStatus, Toast.LENGTH_LONG).show()
                            }) {
                                Text("Debug", color = MaterialTheme.colorScheme.tertiary)
                            }
                            
                            Button(
                                onClick = {
                                    settingsViewModel.validateActivationCode(context, newActivationCode.trim())
                                },
                                enabled = !isLoadingActivationCodes && newActivationCode.isNotBlank(),
                                modifier = Modifier.align(Alignment.CenterVertically)
                            ) {
                                Text(stringResource(R.string.activate))
                            }
                        }
                    }
                }
            }
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // Section title for AI settings
            Text(
                text = stringResource(R.string.ai_settings),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            // Gemini API Key Card
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // API key validation result
                    if (apiKeyValidationSuccess) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = MaterialTheme.colorScheme.primaryContainer,
                                    shape = MaterialTheme.shapes.small
                                )
                                .padding(8.dp)
                        ) {
                            Icon(
                                Icons.Default.Check,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = if (isUsingDefaultKey) 
                                    stringResource(R.string.using_default_api_key)
                                else 
                                    stringResource(R.string.api_key_saved),
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                    
                    // API key validation error
                    if (apiKeyValidationError != null) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = MaterialTheme.colorScheme.errorContainer,
                                    shape = MaterialTheme.shapes.small
                                )
                                .padding(8.dp)
                        ) {
                            Icon(
                                Icons.Default.Error,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                                            Text(
                                text = apiKeyValidationError!!,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                    
                    // Set Custom API Key UI
                    Column {
                        Text(
                            text = stringResource(R.string.set_custom_api_key),
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        
                        OutlinedTextField(
                            value = newApiKey,
                            onValueChange = { newApiKey = it },
                            label = { Text(stringResource(R.string.gemini_api_key)) },
                            placeholder = { Text(stringResource(R.string.enter_gemini_api_key)) },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Password,
                                imeAction = ImeAction.Done
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    focusManager.clearFocus()
                                    if (newApiKey.isNotBlank()) {
                                        settingsViewModel.saveApiKey(context, newApiKey)
                                    }
                                }
                            ),
                            isError = apiKeyValidationError != null,
                            trailingIcon = {
                                if (isValidatingApiKey) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(24.dp),
                                        strokeWidth = 2.dp
                                    )
                                } else if (apiKeyValidationSuccess) {
                                    Icon(
                                        Icons.Default.Check,
                                        contentDescription = null,
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // Button to save API key
                            Button(
                                onClick = {
                                    focusManager.clearFocus()
                                    settingsViewModel.saveApiKey(context, newApiKey)
                                },
                                modifier = Modifier.weight(1f),
                                enabled = newApiKey.isNotBlank() && !isValidatingApiKey
                            ) {
                                Icon(
                                    Icons.Default.Save,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(stringResource(R.string.save_key))
                            }
                            
                            // Button to reset to default
                            OutlinedButton(
                                onClick = {
                                    focusManager.clearFocus()
                                    settingsViewModel.resetToDefaultApiKey(context)
                                    newApiKey = ""
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(
                                    Icons.Default.Refresh,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(stringResource(R.string.use_default))
                            }
                        }
                        
                        // Button to get API key from Google
                        OutlinedButton(
                            onClick = {
                                settingsViewModel.openGoogleAIStudio(context)
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = MaterialTheme.colorScheme.primary
                            )
                        ) {
                            Icon(
                                Icons.Default.OpenInNew,
                                contentDescription = null,
                                modifier = Modifier.size(18.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(stringResource(R.string.get_api_key))
                        }
                    }
                }
            }
            
            // App Settings section
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = stringResource(R.string.app_settings),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            // Language Settings Card
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Language selection
                    Text(
                        text = stringResource(R.string.language_settings),
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    // Get current language
                    val currentLanguage by settingsViewModel.appLanguage.collectAsState()
                    
                    // Language selection radio buttons
                    Column(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // System Default option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = currentLanguage == "system",
                                    onClick = {
                                        settingsViewModel.setAppLanguage(context, "system")
                                    }
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentLanguage == "system",
                                onClick = {
                                    settingsViewModel.setAppLanguage(context, "system")
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(stringResource(R.string.system_default))
                        }
                        
                        // English option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = currentLanguage == "en",
                                    onClick = {
                                        settingsViewModel.setAppLanguage(context, "en")
                                    }
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentLanguage == "en",
                                onClick = {
                                    settingsViewModel.setAppLanguage(context, "en")
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(stringResource(R.string.english))
                        }
                        
                        // Arabic option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = currentLanguage == "ar",
                                    onClick = {
                                        settingsViewModel.setAppLanguage(context, "ar")
                                    }
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentLanguage == "ar",
                                onClick = {
                                    settingsViewModel.setAppLanguage(context, "ar")
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(stringResource(R.string.arabic))
                        }
                    }
                    
                    // Note about language change
                    Text(
                        text = "Note: Some UI changes may require restarting the app to fully apply the selected language.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // App version info
            Text(
                text = "App version: 1.0.0",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 24.dp)
            )
        }
    }
} 