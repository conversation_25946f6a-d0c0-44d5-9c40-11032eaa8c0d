package com.ammar.medical.presentation.ui.screens.document

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.ammar.medical.R
import com.ammar.medical.presentation.components.CommonTopAppBar
import com.ammar.medical.presentation.components.LoadingIndicator
import com.ammar.medical.presentation.components.TagInput

@Composable
fun DocumentEditScreen(
    documentId: Long,
    onNavigateBack: () -> Unit,
    onSave: () -> Unit,
    viewModel: DocumentEditViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(documentId) {
        viewModel.loadDocument(documentId)
    }

    Scaffold(
        topBar = {
            CommonTopAppBar(
                title = stringResource(R.string.edit_document),
                navigationIcon = Icons.Default.ArrowBack,
                onNavigationClick = onNavigateBack,
                actions = {
                    IconButton(
                        onClick = onSave,
                        enabled = uiState is DocumentEditUiState.Success
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(R.string.save)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (uiState) {
                is DocumentEditUiState.Loading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is DocumentEditUiState.Success -> {
                    val document = (uiState as DocumentEditUiState.Success).document
                    val tagSuggestions = (uiState as DocumentEditUiState.Success).tagSuggestions
                    
                    DocumentEditContent(
                        document = document,
                        tagSuggestions = tagSuggestions,
                        onTitleChange = viewModel::updateTitle,
                        onDescriptionChange = viewModel::updateDescription,
                        onTagsChange = viewModel::updateTags
                    )
                }
                is DocumentEditUiState.Error -> {
                    Text(
                        text = (uiState as DocumentEditUiState.Error).message,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun DocumentEditContent(
    document: Document,
    tagSuggestions: Set<String>,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onTagsChange: (Set<String>) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        OutlinedTextField(
            value = document.title,
            onValueChange = onTitleChange,
            label = { Text(stringResource(R.string.title)) },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        OutlinedTextField(
            value = document.description ?: "",
            onValueChange = onDescriptionChange,
            label = { Text(stringResource(R.string.description)) },
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            minLines = 3
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(R.string.tags),
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        TagInput(
            tags = document.tags,
            suggestions = tagSuggestions,
            onTagsChange = onTagsChange,
            modifier = Modifier.fillMaxWidth()
        )
    }
} 