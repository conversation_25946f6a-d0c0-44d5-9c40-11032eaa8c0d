package io.ammar.medical.auth

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

@Composable
fun TestIcon() {
    Icon(
        imageVector = Icons.Filled.Visibility,
        contentDescription = "Test Icon"
    )
    
    Icon(
        imageVector = Icons.Filled.VisibilityOff,
        contentDescription = "Test Icon Off"
    )
} 