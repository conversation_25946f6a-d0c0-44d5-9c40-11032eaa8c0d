// Firestore rules v2
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own documents
    match /users/{userId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && 
                    (request.auth.uid == userId || 
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'lastLogin']));
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow users to send and receive messages
    match /messages/{messageId} {
      allow create: if request.auth != null && 
                     request.resource.data.senderUid == request.auth.uid;
      
      allow read: if request.auth != null && 
                   (resource.data.senderUid == request.auth.uid || 
                    resource.data.recipientUid == request.auth.uid);
      
      allow update: if request.auth != null && 
                     resource.data.recipientUid == request.auth.uid &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
      
      allow delete: if request.auth != null && 
                     (resource.data.senderUid == request.auth.uid || 
                      resource.data.recipientUid == request.auth.uid);
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
} 