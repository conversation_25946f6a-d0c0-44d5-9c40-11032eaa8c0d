# Updated Firebase Security Rules

## Problem
The app is showing "missing or denied permission" when anonymous users try to access their contacts. This is because the current Firestore security rules don't properly allow anonymous users to access the contacts subcollection.

## Solution
You need to update your Firebase security rules in the Firebase Console to allow anonymous users to:
1. Read and write to their own contacts subcollection
2. Read other users' documents (to verify recipients)
3. Create and read messages

## Steps to Fix

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to "Firestore Database" in the left sidebar
4. Click on the "Rules" tab
5. Replace the current rules with the following:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own documents
    match /users/{userId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && 
                    (request.auth.uid == userId || 
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'lastLogin']));
      allow delete: if request.auth != null && request.auth.uid == userId;
      
      // Allow users to manage their contacts (including anonymous users)
      match /contacts/{contactId} {
        allow read: if request.auth != null && request.auth.uid == userId;
        allow create, update: if request.auth != null && request.auth.uid == userId;
        allow delete: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Allow users to send and receive messages
    match /messages/{messageId} {
      allow create: if request.auth != null && 
                     request.resource.data.senderUid == request.auth.uid;
      
      allow read: if request.auth != null && 
                   (resource.data.senderUid == request.auth.uid || 
                    resource.data.recipientUid == request.auth.uid);
      
      allow update: if request.auth != null && 
                     resource.data.recipientUid == request.auth.uid &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
      
      allow delete: if request.auth != null && 
                     (resource.data.senderUid == request.auth.uid || 
                      resource.data.recipientUid == request.auth.uid);
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

6. Click "Publish" to apply the new rules

## Explanation of Changes

The key change is that we've moved the contacts subcollection rules inside the users document rule. This ensures that:

1. The rules are properly applied to the subcollection path: `/users/{userId}/contacts/{contactId}`
2. Anonymous users can access their own contacts since we only check for authentication (`request.auth != null`) and matching user ID
3. The rules maintain security by ensuring users can only access their own contacts

## Testing

After updating the rules, you should be able to:
1. Sign in anonymously and access the contacts feature
2. Add and delete contacts as an anonymous user
3. Send messages to contacts

## Troubleshooting

If you continue to experience permission issues:

1. Check the Firebase console logs for detailed error messages
2. Verify that the path to your contacts collection is exactly `/users/{userId}/contacts/{contactId}`
3. Make sure your app is using the correct Firebase project
4. Try clearing the app cache or reinstalling the app to ensure it's using the latest rules 