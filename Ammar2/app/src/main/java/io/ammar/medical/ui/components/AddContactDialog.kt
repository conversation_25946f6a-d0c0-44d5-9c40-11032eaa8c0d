package io.ammar.medical.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import io.ammar.medical.data.Contact

/**
 * Dialog for adding a new contact
 */
@Composable
fun AddContactDialog(
    onDismiss: () -> Unit,
    onContactAdded: () -> Unit
) {
    var uid by remember { mutableStateOf("") }
    var name by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    var error by remember { mutableStateOf<String?>(null) }
    var success by remember { mutableStateOf<String?>(null) }
    
    val scope = rememberCoroutineScope()
    
    AlertDialog(
        onDismissRequest = { 
            if (!isLoading) {
                onDismiss()
            }
        },
        title = { Text("Add New Contact") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedTextField(
                    value = uid,
                    onValueChange = { uid = it },
                    label = { Text("Contact User ID") },
                    placeholder = { Text("Enter contact's User ID") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )
                
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Contact Name (Optional)") },
                    placeholder = { Text("Enter a name for this contact") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                )
                
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }
                
                error?.let {
                    Text(
                        text = it,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                
                success?.let {
                    Text(
                        text = it,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (uid.isBlank()) {
                        error = "Contact UID cannot be empty"
                        return@Button
                    }
                    
                    isLoading = true
                    error = null
                    success = null
                    
                    scope.launch {
                        try {
                            val result = addContact(uid, name)
                            if (result.isSuccess) {
                                success = "Contact added successfully"
                                // Clear fields
                                uid = ""
                                name = ""
                                // Notify parent
                                onContactAdded()
                                // Close dialog after a delay
                                kotlinx.coroutines.delay(1500)
                                onDismiss()
                            } else {
                                error = result.exceptionOrNull()?.message ?: "Failed to add contact"
                            }
                        } catch (e: Exception) {
                            error = e.message ?: "An error occurred"
                        } finally {
                            isLoading = false
                        }
                    }
                },
                enabled = !isLoading && uid.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Add a contact to Firestore
 */
private suspend fun addContact(uid: String, name: String): Result<Contact> {
    val auth = FirebaseAuth.getInstance()
    val currentUser = auth.currentUser ?: return Result.failure(Exception("You must be signed in to add contacts"))
    
    if (uid.isBlank()) {
        return Result.failure(Exception("Contact UID cannot be empty"))
    }
    
    val contactName = if (name.isBlank()) uid else name
    
    return try {
        // First verify that the user exists
        val db = FirebaseFirestore.getInstance()
        val userDoc = db.collection("users").document(uid).get().await()
        
        if (!userDoc.exists()) {
            return Result.failure(Exception("User with this ID does not exist"))
        }
        
        // User exists, add to contacts
        val contact = Contact(
            uid = uid,
            name = contactName,
            timestamp = System.currentTimeMillis()
        )
        
        db.collection("users").document(currentUser.uid)
            .collection("contacts")
            .document(uid) // Use the UID as the document ID to prevent duplicates
            .set(contact)
            .await()
        
        Result.success(contact)
    } catch (e: Exception) {
        val errorMsg = if (e.message?.contains("permission denied", ignoreCase = true) == true) {
            Exception("Permission denied. Make sure Firestore rules allow writing contacts.")
        } else {
            e
        }
        Result.failure(errorMsg)
    }
} 