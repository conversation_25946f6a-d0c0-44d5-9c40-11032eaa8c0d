package io.ammar.medical
import io.ammar.medical.R
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.QrCode2
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.ammar.medical.data.Contact
import io.ammar.medical.data.UserMessage
import io.ammar.medical.ui.components.ContactsButton
import io.ammar.medical.ui.components.OfflineBanner
import io.ammar.medical.ui.screens.SplashScreen
import io.ammar.medical.ui.screens.TextRephraserScreen
import io.ammar.medical.ui.screens.SettingsScreen
import io.ammar.medical.ui.theme.Ammar2Theme
import io.ammar.medical.utils.OfflineModeManager
import io.ammar.medical.viewmodel.SplashViewModel
import io.ammar.medical.viewmodel.SettingsViewModel
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.Locale
import android.app.Activity

class MainActivity : ComponentActivity() {
    private val TAG = "MainActivity"
    private val auth = FirebaseAuth.getInstance()
    private val db = FirebaseFirestore.getInstance()
    
    // Offline mode manager
    private lateinit var offlineModeManager: OfflineModeManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get the offline mode manager
        offlineModeManager = AmmarApplication.getOfflineModeManager(this)
        
        // Force a connection check when the app starts
        offlineModeManager.retryConnection()
        
        // Apply saved language settings
        val sharedPreferences = getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        val savedLanguage = sharedPreferences.getString("app_language", "system") ?: "system"
        
        val locale = when (savedLanguage) {
            "en" -> Locale("en")
            "ar" -> Locale("ar")
            else -> Locale.getDefault() // Use system default
        }
        
        Locale.setDefault(locale)
        val configuration = Configuration(resources.configuration)
        configuration.setLocale(locale)
        resources.updateConfiguration(configuration, resources.displayMetrics)
        
        setContent {
            Ammar2Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // Track whether to show splash screen
                    var showSplash by remember { mutableStateOf(true) }
                    
                    // Create the splash view model
                    val splashViewModel: SplashViewModel = viewModel()
                    
                    if (showSplash) {
                        // Show splash screen
                        SplashScreen(
                            splashViewModel = splashViewModel,
                            onInitializationComplete = {
                                showSplash = false
                                // Force a connection check after splash screen completes
                                offlineModeManager.retryConnection()
                            }
                        )
                    } else {
                        // Show main screen after initialization
                        Box(modifier = Modifier.fillMaxSize()) {
                            // Main content
                            MainScreen()
                            
                            // Offline banner at the top
                            OfflineBanner(
                                modifier = Modifier.align(Alignment.TopCenter),
                                isOfflineMode = offlineModeManager.showOfflineBanner,
                                onRetry = { 
                                    // Force a connection check when retry is clicked
                                    offlineModeManager.retryConnection() 
                                },
                                onDismiss = { offlineModeManager.dismissOfflineBanner() }
                            )
                        }
                    }
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Force a connection check when the app resumes
        if (::offlineModeManager.isInitialized) {
            offlineModeManager.retryConnection()
        }
    }
    
    /**
     * Check for new messages
     */
    fun checkForNewMessages() {
        // Implementation omitted for simplicity
        Log.d(TAG, "Checking for new messages")
    }
    
    /**
     * Get shared file URI from intent
     */
    fun getSharedFileUri(): Uri? {
        return if (intent?.action == Intent.ACTION_SEND) {
            intent.getParcelableExtra(Intent.EXTRA_STREAM)
        } else {
            null
        }
    }
    
    /**
     * Check if the URI is a ZIP file
     */
    fun isZipFile(uri: Uri): Boolean {
        val mimeType = intent?.type
        return mimeType == "application/zip" || mimeType == "application/x-zip-compressed" ||
               contentResolver.getType(uri)?.let { 
                   it == "application/zip" || it == "application/x-zip-compressed" 
               } ?: false
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // Get current Firebase user with state observation
    val auth = FirebaseAuth.getInstance()
    var currentUser by remember { mutableStateOf(auth.currentUser) }
    var userId by remember { mutableStateOf(currentUser?.uid ?: "Not signed in") }
    var isAnonymous by remember { mutableStateOf(currentUser?.isAnonymous ?: false) }
    
    // Settings view model
    val settingsViewModel: SettingsViewModel = viewModel()
    
    // Screen navigation state - Using the sealed class from Screen.kt
    var currentScreen by remember { mutableStateOf<Screen>(Screen.Main) }
    
    // State for shared ZIP content
    var sharedZipUri by remember { mutableStateOf<Uri?>(null) }
    
    // Load settings on first composition
    LaunchedEffect(Unit) {
        settingsViewModel.loadSettings(context)
        
        // Check for shared ZIP files
        val activity = context as? MainActivity
        activity?.let {
            val intentHasRedirect = it.intent?.getBooleanExtra("REDIRECT_TO_ANALYZER", false) ?: false
            val navigateToSettings = it.intent?.getBooleanExtra("NAVIGATE_TO_SETTINGS", false) ?: false
            val sharedUri = it.getSharedFileUri()
            
            if (navigateToSettings) {
                // Navigate to settings screen
                currentScreen = Screen.Settings
            } else if (sharedUri != null && (it.isZipFile(sharedUri) || intentHasRedirect)) {
                // Navigate to MultiImageAnalyzer with the shared ZIP
                sharedZipUri = sharedUri
                currentScreen = Screen.MultiImageAnalyzer
            }
        }
    }
    
    // Observe auth state changes
    DisposableEffect(Unit) {
        val listener = FirebaseAuth.AuthStateListener { firebaseAuth ->
            currentUser = firebaseAuth.currentUser
            userId = currentUser?.uid ?: "Not signed in"
            isAnonymous = currentUser?.isAnonymous ?: false
        }
        auth.addAuthStateListener(listener)
        onDispose {
            auth.removeAuthStateListener(listener)
        }
    }
    
    // Message dialog state
    var showMessageDialog by remember { mutableStateOf(false) }
    var recipientUid by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }
    var messageSending by remember { mutableStateOf(false) }
    var messageStatus by remember { mutableStateOf<String?>(null) }
    
    // Inbox dialog state
    var showInboxDialog by remember { mutableStateOf(false) }
    var userMessages by remember { mutableStateOf<List<UserMessage>>(emptyList()) }
    var isLoadingMessages by remember { mutableStateOf(false) }
    var inboxStatus by remember { mutableStateOf<String?>(null) }
    
    // Function to load user messages
    val loadUserMessages = { uid: String ->
        isLoadingMessages = true
        userMessages = emptyList()
        inboxStatus = null
        
        Log.d("Messaging", "Loading messages for user $uid")
        
        val db = FirebaseFirestore.getInstance()
        db.collection("messages")
            .whereEqualTo("recipientUid", uid)
            .orderBy("timestamp", com.google.firebase.firestore.Query.Direction.DESCENDING)
            .get()
            .addOnSuccessListener { documents ->
                isLoadingMessages = false
                
                if (documents.isEmpty) {
                    Log.d("Messaging", "No messages found for user $uid")
                    return@addOnSuccessListener
                }
                
                Log.d("Messaging", "Found ${documents.size()} messages for user $uid")
                
                val messages = documents.mapNotNull { doc ->
                    try {
                        UserMessage(
                            id = doc.id,
                            senderUid = doc.getString("senderUid") ?: return@mapNotNull null,
                            recipientUid = doc.getString("recipientUid") ?: return@mapNotNull null,
                            message = doc.getString("message") ?: return@mapNotNull null,
                            timestamp = doc.getLong("timestamp") ?: System.currentTimeMillis(),
                            read = doc.getBoolean("read") ?: false
                        )
                    } catch (e: Exception) {
                        Log.e("Messaging", "Error parsing message", e)
                        null
                    }
                }
                
                userMessages = messages
                
                // Mark messages as read
                if (messages.isNotEmpty()) {
                    Log.d("Messaging", "Marking ${messages.count { !it.read }} unread messages as read")
                    
                    val batch = db.batch()
                    var hasUnreadMessages = false
                    
                    messages.forEach { message ->
                        if (!message.read) {
                            hasUnreadMessages = true
                            val docRef = db.collection("messages").document(message.id)
                            batch.update(docRef, "read", true)
                        }
                    }
                    
                    if (hasUnreadMessages) {
                        batch.commit()
                            .addOnSuccessListener {
                                Log.d("Messaging", "Messages marked as read")
                            }
                            .addOnFailureListener { e ->
                                Log.e("Messaging", "Error marking messages as read: ${e.message}", e)
                            }
                    } else {
                        Log.d("Messaging", "No unread messages to mark")
                    }
                }
            }
            .addOnFailureListener { e ->
                isLoadingMessages = false
                Log.e("Messaging", "Error loading messages: ${e.message}", e)
                
                inboxStatus = if (e.message?.contains("permission denied", ignoreCase = true) == true) {
                    "Error: Permission denied. Make sure Firestore rules allow reading messages collection."
                } else {
                    "Error loading messages: ${e.message}"
                }
            }
    }

    // Handle back button press
    BackHandler {
        if (currentScreen != Screen.Main) {
            currentScreen = Screen.Main
        } else {
            // Let the system handle back press if on main screen
            (context as? Activity)?.finish()
        }
    }

    Scaffold(
        topBar = {
            if (currentScreen != Screen.Settings) {
                TopAppBar(
                    title = {
                        Text(
                            text = when (currentScreen) {
                                Screen.Main -> stringResource(R.string.app_name)
                                Screen.MultiImageAnalyzer -> stringResource(R.string.medical_reports_analyzer)
                                Screen.TextRephraser -> stringResource(R.string.text_rephraser)
                                Screen.Settings -> stringResource(R.string.settings)
                            }
                        )
                    },
                    navigationIcon = {
                        if (currentScreen != Screen.Main) {
                            IconButton(onClick = { currentScreen = Screen.Main }) {
                                Icon(
                                    imageVector = Icons.Default.ArrowBack,
                                    contentDescription = stringResource(R.string.back),
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    },
                    actions = {
                        // Settings button
                        IconButton(onClick = { currentScreen = Screen.Settings }) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = stringResource(R.string.settings)
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.surface,
                        titleContentColor = MaterialTheme.colorScheme.onSurface
                    )
                )
            }
        }
    ) { paddingValues ->
        when (currentScreen) {
            Screen.Main -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Header
                    Text(
                        text = "SmartMed",
                        style = MaterialTheme.typography.headlineMedium
                    )
                    
                    // User ID Card
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Person,
                                    contentDescription = "User",
                                    modifier = Modifier.size(24.dp),
                                    tint = MaterialTheme.colorScheme.primary
                                )
                                Spacer(modifier = Modifier.width(16.dp))
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = "User ID:",
                                        style = MaterialTheme.typography.labelMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = userId,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    if (isAnonymous) {
                                        Text(
                                            text = "(Anonymous User)",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                }
                                
                                // Add contacts button
                                ContactsButton(
                                    onSelectContact = { contact ->
                                        // Open message dialog with this contact
                                        recipientUid = contact.uid
                                        showMessageDialog = true
                                    }
                                )
                                
                                // Add message button
                                IconButton(
                                    onClick = { showMessageDialog = true }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Send,
                                        contentDescription = "Send Message",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                                
                                // Add inbox button
                                IconButton(
                                    onClick = {
                                        val user = currentUser
                                        if (user != null) {
                                            showInboxDialog = true
                                            loadUserMessages(user.uid)
                                        } else {
                                            Toast.makeText(context, "You must be signed in to view messages", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Email,
                                        contentDescription = "View Messages",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }

                    // Action buttons row - change to column
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = { currentScreen = Screen.MultiImageAnalyzer },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Image Analysis")
                        }
                        
                        Button(
                            onClick = { currentScreen = Screen.TextRephraser },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary
                            )
                        ) {
                            Text("Text Rephraser")
                        }
                        
                        Button(
                            onClick = { 
                                // Launch ShareReceiveActivity
                                val intent = Intent(context, ShareReceiveActivity::class.java)
                                context.startActivity(intent)
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.tertiary
                            )
                        ) {
                            Text("Image Extractor")
                        }
                    }
                    
                    // Instructions Card replaced with Inbox Preview
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "Recent Messages",
                                    style = MaterialTheme.typography.titleLarge
                                )
                                
                                TextButton(
                                    onClick = { 
                                        val user = currentUser
                                        if (user != null) {
                                            showInboxDialog = true
                                            loadUserMessages(user.uid)
                                        }
                                    }
                                ) {
                                    Text("View All")
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // Show loading, error, or messages
                            if (isLoadingMessages) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(100.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            } else if (inboxStatus != null) {
                                Text(
                                    text = inboxStatus!!,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.error
                                )
                            } else if (userMessages.isEmpty()) {
                                Text(
                                    text = "No new messages",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            } else {
                                // Show last 3 unread messages
                                Column(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    userMessages.filter { !it.read }.take(3).forEach { message ->
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 4.dp),
                                            horizontalArrangement = Arrangement.SpaceBetween,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Column(modifier = Modifier.weight(1f)) {
                                                Text(
                                                    text = "From: ${message.senderUid}",
                                                    style = MaterialTheme.typography.labelMedium,
                                                    fontWeight = FontWeight.Bold
                                                )
                                                Text(
                                                    text = message.message,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                                Text(
                                                    text = formatTimestamp(message.timestamp),
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                                )
                                            }
                                            
                                            // New message indicator
                                            Box(
                                                modifier = Modifier
                                                    .size(8.dp)
                                                    .background(
                                                        color = MaterialTheme.colorScheme.primary,
                                                        shape = CircleShape
                                                    )
                                            )
                                        }
                                        
                                        if (message != userMessages.filter { !it.read }.take(3).last()) {
                                            Divider()
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Load messages when component is first displayed
                    LaunchedEffect(currentUser) {
                        currentUser?.let { user ->
                            loadUserMessages(user.uid)
                        }
                    }

                    // Show inbox dialog when requested
                    if (showInboxDialog) {
                        AlertDialog(
                            onDismissRequest = { 
                                showInboxDialog = false 
                                userMessages = emptyList()
                                inboxStatus = null
                            },
                            title = { Text("Your Messages") },
                            text = {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .heightIn(max = 400.dp)
                                ) {
                                    if (isLoadingMessages) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            CircularProgressIndicator()
                                        }
                                    } else if (userMessages.isEmpty() && inboxStatus == null) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = "No messages found",
                                                style = MaterialTheme.typography.bodyLarge,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                    } else if (inboxStatus != null) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = inboxStatus!!,
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = MaterialTheme.colorScheme.error,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                    } else {
                                        LazyColumn(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(300.dp)
                                        ) {
                                            items(userMessages) { message ->
                                                MessageItem(message)
                                                Divider()
                                            }
                                        }
                                    }
                                }
                            },
                            confirmButton = {
                                Button(
                                    onClick = { 
                                        showInboxDialog = false 
                                        userMessages = emptyList()
                                        inboxStatus = null
                                    }
                                ) {
                                    Text("Close")
                                }
                            }
                        )
                    }

                    // Show message dialog when requested
                    if (showMessageDialog) {
                        AlertDialog(
                            onDismissRequest = { 
                                if (!messageSending) {
                                    showMessageDialog = false
                                    recipientUid = ""
                                    messageText = ""
                                    messageStatus = null
                                }
                            },
                            title = { Text("Send Message") },
                            text = {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(8.dp),
                                    verticalArrangement = Arrangement.spacedBy(16.dp)
                                ) {
                                    OutlinedTextField(
                                        value = recipientUid,
                                        onValueChange = { recipientUid = it },
                                        label = { Text("Recipient User ID") },
                                        placeholder = { Text("Enter recipient's User ID") },
                                        modifier = Modifier.fillMaxWidth(),
                                        enabled = !messageSending
                                    )
                                    
                                    OutlinedTextField(
                                        value = messageText,
                                        onValueChange = { messageText = it },
                                        label = { Text("Message") },
                                        placeholder = { Text("Enter your message") },
                                        modifier = Modifier.fillMaxWidth(),
                                        minLines = 3,
                                        enabled = !messageSending
                                    )
                                    
                                    if (messageSending) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.align(Alignment.CenterHorizontally)
                                        )
                                    }
                                    
                                    messageStatus?.let {
                                        Text(
                                            text = it,
                                            color = if (it.startsWith("Error")) 
                                                MaterialTheme.colorScheme.error 
                                            else 
                                                MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            },
                            confirmButton = {
                                Button(
                                    onClick = {
                                        if (recipientUid.isBlank()) {
                                            messageStatus = "Error: Recipient ID cannot be empty"
                                            return@Button
                                        }
                                        
                                        if (messageText.isBlank()) {
                                            messageStatus = "Error: Message cannot be empty"
                                            return@Button
                                        }
                                        
                                        messageSending = true
                                        messageStatus = "Sending message..."
                                        
                                        // Send the message using Firestore
                                        scope.launch {
                                            try {
                                                val db = FirebaseFirestore.getInstance()
                                                
                                                // First verify that the recipient exists
                                                val recipientDoc = db.collection("users").document(recipientUid)
                                                    .get()
                                                    .await()
                                                    
                                                if (!recipientDoc.exists()) {
                                                    messageSending = false
                                                    messageStatus = "Error: Recipient user does not exist"
                                                    return@launch
                                                }
                                                
                                                // Create message document
                                                val messageData = hashMapOf(
                                                    "senderUid" to userId,
                                                    "recipientUid" to recipientUid,
                                                    "message" to messageText,
                                                    "timestamp" to System.currentTimeMillis(),
                                                    "read" to false
                                                )
                                                
                                                // Add message to Firestore
                                                db.collection("messages")
                                                    .add(messageData)
                                                    .await()
                                                
                                                // Success
                                                messageSending = false
                                                messageStatus = "Message sent successfully!"
                                                
                                                // Clear fields after successful send
                                                messageText = ""
                                                
                                                // Close dialog after a delay
                                                kotlinx.coroutines.delay(1500)
                                                showMessageDialog = false
                                                recipientUid = ""
                                                messageStatus = null
                                                
                                            } catch (e: Exception) {
                                                messageSending = false
                                                messageStatus = "Error: ${e.message}"
                                            }
                                        }
                                    },
                                    enabled = !messageSending && recipientUid.isNotBlank() && messageText.isNotBlank()
                                ) {
                                    Text("Send")
                                }
                            },
                            dismissButton = {
                                TextButton(
                                    onClick = {
                                        showMessageDialog = false
                                        recipientUid = ""
                                        messageText = ""
                                        messageStatus = null
                                    },
                                    enabled = !messageSending
                                ) {
                                    Text("Cancel")
                                }
                            }
                        )
                    }
                }
            }
            Screen.MultiImageAnalyzer -> {
                io.ammar.medical.MultiImageAnalyzerScreen(
                    onNavigateBack = { currentScreen = Screen.Main },
                    sharedZipUri = sharedZipUri
                )
            }
            Screen.TextRephraser -> {
                TextRephraserScreen(
                    onNavigateBack = { currentScreen = Screen.Main }
                )
            }
            Screen.Settings -> {
                SettingsScreen(
                    onNavigateBack = { currentScreen = Screen.Main }
                )
            }
        }
    }
}

// Function to format timestamp
fun formatTimestamp(timestamp: Long): String {
    val date = java.util.Date(timestamp)
    val format = java.text.SimpleDateFormat("MM/dd/yyyy HH:mm", java.util.Locale.getDefault())
    return format.format(date)
}

// Message item composable
@Composable
fun MessageItem(message: UserMessage) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "From: ${message.senderUid}",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = formatTimestamp(message.timestamp),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = message.message,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}