#!/bin/bash

# Directory with the new files
NEW_DIR="app/src/main/java/io/ammar/medical"

# Search patterns that indicate R class usage
R_PATTERNS=(
    "R\\."
    "stringResource"
    "painterResource"
    "vectorResource"
    "imageResource"
    "colorResource"
    "dimensionResource"
    "pluralStringResource"
    "rememberDrawableResource"
)

# Find all Kotlin files
for file in $(find "$NEW_DIR" -name "*.kt" -type f); do
    needs_import=false
    
    # Check all patterns
    for pattern in "${R_PATTERNS[@]}"; do
        if grep -q "$pattern" "$file"; then
            needs_import=true
            break
        fi
    done
    
    # Add import if needed and not already present
    if [ "$needs_import" = true ] && ! grep -q "import io\.ammar\.medical\.R" "$file"; then
        # Add import statement after the package declaration
        sed -i '' '/^package/a\'$'\n''import io.ammar.medical.R' "$file"
        echo "Added R import to $file"
    fi
done

echo "R class imports added to all necessary Kotlin files" 