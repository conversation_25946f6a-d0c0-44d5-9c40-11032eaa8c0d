package io.ammar.medical.utils

import android.content.Context
import android.util.Log
import com.google.android.gms.tasks.Tasks
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreException
import com.google.firebase.firestore.Source
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.net.InetAddress
import java.net.UnknownHostException
import java.util.concurrent.TimeUnit

/**
 * Utility class for Firebase-related operations and diagnostics
 */
object FirebaseUtils {
    private const val TAG = "FirebaseUtils"
    
    /**
     * Checks if Firebase services are reachable
     */
    suspend fun checkFirebaseConnectivity(context: Context? = null): FirebaseConnectivityResult = withContext(Dispatchers.IO) {
        val result = FirebaseConnectivityResult()
        
        // Check if Firebase is initialized
        result.isInitialized = if (context != null) {
            FirebaseApp.getApps(context).isNotEmpty()
        } else {
            try {
                FirebaseApp.getInstance() != null
            } catch (e: Exception) {
                Log.e(TAG, "Error checking Firebase initialization", e)
                false
            }
        }
        
        // Check if network is available first
        val isNetworkAvailable = context?.let { NetworkUtils.isNetworkAvailable(it) } ?: true
        result.isNetworkAvailable = isNetworkAvailable
        
        if (!isNetworkAvailable) {
            Log.w(TAG, "Network is not available, skipping host reachability checks")
            result.isFirebaseHostReachable = false
            result.isFirebaseAuthReachable = false
        } else {
            // Check if Firebase hosts are reachable
            val (firestoreReachable, authReachable) = NetworkUtils.checkFirebaseHostsReachable()
            result.isFirebaseHostReachable = firestoreReachable
            result.isFirebaseAuthReachable = authReachable
        }
        
        // Check Firestore connectivity - with better error handling and retry
        try {
            val db = FirebaseFirestore.getInstance()
            
            // First check if we can access cache
            val cacheResult = try {
                val testDoc = withTimeoutOrNull(2000) {
                    db.collection("connectivity_test").document("test")
                        .get(Source.CACHE).await()
                }
                testDoc != null
            } catch (e: Exception) {
                Log.w(TAG, "Firestore cache access failed", e)
                false
            }
            
            result.isFirestoreCacheAccessible = cacheResult
            
            // Only try server if network is available
            if (isNetworkAvailable) {
                // Try up to 2 times with a short delay between attempts
                var serverResult = false
                var lastError: Exception? = null
                
                for (attempt in 1..2) {
                    try {
                        Log.d(TAG, "Attempting Firestore connectivity check (attempt $attempt)")
                        val testDoc = withTimeoutOrNull(5000) {
                            db.collection("connectivity_test").document("test")
                                .get(Source.SERVER).await()
                        }
                        
                        if (testDoc != null) {
                            serverResult = true
                            Log.d(TAG, "Firestore connectivity check succeeded on attempt $attempt")
                            break
                        } else {
                            Log.w(TAG, "Firestore connectivity check returned null document on attempt $attempt")
                        }
                    } catch (e: Exception) {
                        lastError = e
                        Log.e(TAG, "Firestore server connectivity check failed on attempt $attempt", e)
                        
                        // Add a small delay before retrying
                        if (attempt < 2) {
                            delay(1000)
                        }
                    }
                }
                
                result.isFirestoreConnected = serverResult
                if (!serverResult && lastError != null) {
                    result.firestoreError = lastError.message
                }
            } else {
                result.isFirestoreConnected = false
                result.firestoreError = "Network unavailable"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Firestore connectivity check failed", e)
            result.isFirestoreConnected = false
            result.isFirestoreCacheAccessible = false
            result.firestoreError = e.message
        }
        
        // Check Auth connectivity
        try {
            val auth = FirebaseAuth.getInstance()
            result.isAuthConnected = auth.app != null
        } catch (e: Exception) {
            Log.e(TAG, "Auth connectivity check failed", e)
            result.isAuthConnected = false
            result.authError = e.message
        }
        
        // Check FCM connectivity with retry
        try {
            val fcm = FirebaseMessaging.getInstance()
            var fcmConnected = false
            var lastError: Exception? = null
            
            for (attempt in 1..2) {
                try {
                    Log.d(TAG, "Attempting FCM connectivity check (attempt $attempt)")
                    val tokenTask = fcm.token
                    val token = withTimeoutOrNull(5000) {
                        Tasks.await(tokenTask, 5, TimeUnit.SECONDS)
                    }
                    
                    if (token != null) {
                        fcmConnected = true
                        Log.d(TAG, "FCM connectivity check succeeded on attempt $attempt")
                        break
                    } else {
                        Log.w(TAG, "FCM connectivity check returned null token on attempt $attempt")
                    }
                } catch (e: Exception) {
                    lastError = e
                    Log.e(TAG, "FCM connectivity check failed on attempt $attempt", e)
                    
                    // Add a small delay before retrying
                    if (attempt < 2) {
                        delay(1000)
                    }
                }
            }
            
            result.isFcmConnected = fcmConnected
            if (!fcmConnected && lastError != null) {
                result.fcmError = lastError.message
            }
        } catch (e: Exception) {
            Log.e(TAG, "FCM connectivity check failed", e)
            result.isFcmConnected = false
            result.fcmError = e.message
        }
        
        return@withContext result
    }
    
    /**
     * Safely gets a document from Firestore, handling offline scenarios
     * Returns the document and a boolean indicating if it's from cache
     */
    suspend fun safeGetDocument(
        docRef: DocumentReference,
        preferCache: Boolean = false
    ): Pair<DocumentSnapshot?, Boolean> = withContext(Dispatchers.IO) {
        var isFromCache = false
        var document: DocumentSnapshot? = null
        
        try {
            // First try the preferred source
            val preferredSource = if (preferCache) Source.CACHE else Source.SERVER
            try {
                document = docRef.get(preferredSource).await()
                isFromCache = document.metadata.isFromCache
                Log.d(TAG, "Document retrieved from ${if (isFromCache) "cache" else "server"}")
            } catch (e: FirebaseFirestoreException) {
                Log.w(TAG, "Failed to get document from $preferredSource", e)
                
                // If preferred source failed, try the alternative
                val alternativeSource = if (preferCache) Source.SERVER else Source.CACHE
                try {
                    document = docRef.get(alternativeSource).await()
                    isFromCache = document.metadata.isFromCache
                    Log.d(TAG, "Document retrieved from fallback source: ${if (isFromCache) "cache" else "server"}")
                } catch (e2: Exception) {
                    Log.e(TAG, "Failed to get document from both sources", e2)
                    // Both sources failed, return null
                    return@withContext Pair(null, false)
                }
            }
        } catch (e: Exception) {
            // Handle any other exceptions
            Log.e(TAG, "Unexpected error getting document", e)
            return@withContext Pair(null, false)
        }
        
        return@withContext Pair(document, isFromCache)
    }
    
    /**
     * Checks if a host is reachable
     */
    private suspend fun isHostReachable(host: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val address = InetAddress.getByName(host)
            return@withContext address.isReachable(3000)
        } catch (e: UnknownHostException) {
            Log.e(TAG, "Host not found: $host", e)
            return@withContext false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking host reachability: $host", e)
            return@withContext false
        }
    }
    
    /**
     * Data class to hold Firebase connectivity check results
     */
    data class FirebaseConnectivityResult(
        var isInitialized: Boolean = false,
        var isNetworkAvailable: Boolean = false,
        var isFirebaseHostReachable: Boolean = false,
        var isFirebaseAuthReachable: Boolean = false,
        var isFirestoreConnected: Boolean = false,
        var isFirestoreCacheAccessible: Boolean = false,
        var isAuthConnected: Boolean = false,
        var isFcmConnected: Boolean = false,
        var firestoreError: String? = null,
        var authError: String? = null,
        var fcmError: String? = null
    ) {
        val isFullyConnected: Boolean
            get() = isInitialized && isFirebaseHostReachable && 
                    isFirestoreConnected && isAuthConnected && isFcmConnected
        
        val canOperateOffline: Boolean
            get() = isInitialized && 
                    (isFirestoreCacheAccessible || isAuthConnected)
                    
        fun getDetailedReport(): String {
            return """
                Firebase Connectivity Report:
                - Network Available: $isNetworkAvailable
                - Firebase Initialized: $isInitialized
                - Firebase Host Reachable: $isFirebaseHostReachable
                - Firebase Auth Host Reachable: $isFirebaseAuthReachable
                - Firestore Connected: $isFirestoreConnected ${firestoreError?.let { "($it)" } ?: ""}
                - Firestore Cache Accessible: $isFirestoreCacheAccessible
                - Auth Connected: $isAuthConnected ${authError?.let { "($it)" } ?: ""}
                - FCM Connected: $isFcmConnected ${fcmError?.let { "($it)" } ?: ""}
                - Fully Connected: $isFullyConnected
                - Can Operate Offline: $canOperateOffline
            """.trimIndent()
        }
    }
} 