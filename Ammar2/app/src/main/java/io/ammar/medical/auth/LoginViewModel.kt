package io.ammar.medical.auth

import android.app.Application
import android.util.Log
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout

class LoginViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "LoginViewModel"
    private val authService = AuthService(application.applicationContext)
    private val googleSignInHelper = GoogleSignInHelper(application.applicationContext)

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser

    private val _uiState = MutableStateFlow<LoginUiState>(LoginUiState.Initial)
    val uiState: StateFlow<LoginUiState> = _uiState

    private val _registrationState = MutableStateFlow<RegistrationState>(RegistrationState.Initial)
    val registrationState: StateFlow<RegistrationState> = _registrationState

    private val _isBypassUser = MutableStateFlow(false)
    val isBypassUser: StateFlow<Boolean> = _isBypassUser

    init {
        viewModelScope.launch {
            Log.d(TAG, "Initializing LoginViewModel")
            try {
                withTimeout(5000L) {
                    _isLoading.value = true
                    authService.refreshUserState()
                    syncStates()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during initialization", e)
                _uiState.value = LoginUiState.Error("Failed to initialize: ${e.message}")
                resetStates()
            } finally {
                _isLoading.value = false
            }
        }
    }

    private fun resetStates() {
        Log.d(TAG, "Resetting all states")
        _isLoggedIn.value = false
        _currentUser.value = null
        _isBypassUser.value = false
        _uiState.value = LoginUiState.Initial
        _registrationState.value = RegistrationState.Initial
        _isLoading.value = false
    }

    private suspend fun syncStates() {
        Log.d(TAG, "Syncing states with AuthService")
        val isLoggedInValue = authService.isLoggedIn.value
        val currentUserValue = authService.currentUser.value
        val isBypassUserValue = authService.isCurrentUserBypass()
        
        _isLoggedIn.value = isLoggedInValue
        _currentUser.value = currentUserValue
        _isBypassUser.value = isBypassUserValue
        
        if (isBypassUserValue) {
            Log.d(TAG, "States synced - Bypass user detected")
        }
        
        if (isLoggedInValue && currentUserValue != null) {
            _uiState.value = LoginUiState.Success(currentUserValue)
            Log.d(TAG, "States synced - User logged in: ${currentUserValue.username}")
        } else {
            Log.d(TAG, "States synced - No user logged in")
        }
    }

    private suspend fun updateStates(user: User) {
        Log.d(TAG, "Updating states with user: ${user.username}")
        try {
            // Reset states first to force recomposition
            _isLoggedIn.value = false
            _currentUser.value = null
            _uiState.value = LoginUiState.Loading
            
            // Small delay to ensure reset is processed
            delay(100)
            
            // Update with new values
            _currentUser.value = user
            _isLoggedIn.value = true
            _uiState.value = LoginUiState.Success(user)
            
            // Force Firebase refresh
            authService.refreshUserState()
            
            // Multiple refresh attempts with increasing delays
            repeat(3) { attempt ->
                delay((attempt + 1) * 300L) // Shorter delays: 300ms, 600ms, 900ms
                try {
                    authService.refreshUserState()
                    val isLoggedInValue = authService.isLoggedIn.value
                    val currentUserValue = authService.currentUser.value
                    
                    // Update states again with fresh values
                    _isLoggedIn.value = isLoggedInValue
                    _currentUser.value = currentUserValue
                    if (currentUserValue != null) {
                        _uiState.value = LoginUiState.Success(currentUserValue)
                    }
                    Log.d(TAG, "State refresh attempt ${attempt + 1} successful")
                } catch (e: Exception) {
                    Log.e(TAG, "State refresh attempt ${attempt + 1} failed", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating states", e)
            resetStates()
            throw e
        }
    }

    fun refreshUserState() {
        Log.d(TAG, "Manual refresh of user state requested")
        viewModelScope.launch {
            try {
                _isLoading.value = true
                authService.refreshUserState()
                syncStates()
                Log.d(TAG, "Manual user state refresh completed")
            } catch (e: Exception) {
                Log.e(TAG, "Error during manual user state refresh", e)
                _uiState.value = LoginUiState.Error("Failed to refresh state: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun register(username: String, email: String, password: String, confirmPassword: String) {
        viewModelScope.launch {
            Log.d(TAG, "Starting registration for email: $email")
            try {
                // Reset states first
                _isLoading.value = true
                _registrationState.value = RegistrationState.Loading
                _uiState.value = LoginUiState.Loading
                _isLoggedIn.value = false
                _currentUser.value = null

                val credentials = RegisterCredentials(username, email, password, confirmPassword)
                val result = withTimeout(10000L) {
                    authService.register(credentials)
                }

                if (result.success && result.user != null) {
                    Log.d(TAG, "Registration successful for user: ${result.user.username}")
                    
                    // Update registration state first
                    _registrationState.value = RegistrationState.Success(result.user)
                    
                    // Force immediate UI update with small delay
                    delay(100)
                    _currentUser.value = result.user
                    _isLoggedIn.value = true
                    _uiState.value = LoginUiState.Success(result.user)
                    
                    // Then do the full state update
                    updateStates(result.user)
                } else {
                    throw Exception(result.errorMessage ?: "Unknown error occurred")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Registration failed", e)
                _registrationState.value = RegistrationState.Error(e.message ?: "Registration failed")
                _uiState.value = LoginUiState.Error(e.message ?: "Registration failed")
                resetStates()
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun login(username: String, password: String) {
        viewModelScope.launch {
            Log.d(TAG, "Attempting login for user: $username")
            try {
                // Reset states first
                _isLoading.value = true
                _uiState.value = LoginUiState.Loading
                _isLoggedIn.value = false
                _currentUser.value = null

                val result = withTimeout(10000L) {
                    authService.login(LoginCredentials(username, password))
                }

                if (result.success && result.user != null) {
                    Log.d(TAG, "Login successful for user: ${result.user.username}")
                    
                    // Force immediate UI update with small delay
                    delay(100)
                    _currentUser.value = result.user
                    _isLoggedIn.value = true
                    _uiState.value = LoginUiState.Success(result.user)
                    
                    // Then do the full state update
                    updateStates(result.user)
                } else {
                    throw Exception(result.errorMessage ?: "Unknown error occurred")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Login failed", e)
                _uiState.value = LoginUiState.Error(e.message ?: "Login failed")
                resetStates()
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun logout() {
        viewModelScope.launch {
            Log.d(TAG, "Logging out")
            _isLoading.value = true
            try {
                authService.logout()
                resetStates()
            } catch (e: Exception) {
                Log.e(TAG, "Logout failed", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun resetPassword(email: String) {
        // Implement password reset logic
        // This would typically involve sending a reset link to the user's email
    }

    fun setupBiometric(activity: FragmentActivity, onSuccess: () -> Unit) {
        authService.setupBiometric(activity, onSuccess)
    }

    fun bypassLogin() {
        Log.d(TAG, "Using bypass login")
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Reset any previous state
                _uiState.value = LoginUiState.Initial
                
                val tempUser = User(
                    id = "temp_user_id",
                    username = "TestUser",
                    email = "<EMAIL>",
                    hashedPassword = "dummy_hashed_password",
                    salt = "dummy_salt",
                    createdAt = System.currentTimeMillis(),
                    lastLogin = System.currentTimeMillis()
                )
                
                // Bypass auth in the AuthService - this will now store the user in SharedPreferences
                authService.bypassAuth(tempUser)
                
                // Update all states immediately and directly
                _currentUser.value = tempUser
                _isLoggedIn.value = true
                _isBypassUser.value = true
                _uiState.value = LoginUiState.Success(tempUser)
                
                Log.d(TAG, "Bypass login complete - User is now logged in")
                Log.d(TAG, "Bypass user state: isBypassUser=${_isBypassUser.value}, isLoggedIn=${_isLoggedIn.value}, username=${_currentUser.value?.username}")
                
                // Add a delay before allowing any other state changes
                delay(1000)
                
                // Double-check the state after the delay
                if (!_isLoggedIn.value || _currentUser.value == null) {
                    Log.w(TAG, "Bypass login state was lost after delay, restoring it")
                    _currentUser.value = tempUser
                    _isLoggedIn.value = true
                    _isBypassUser.value = true
                    _uiState.value = LoginUiState.Success(tempUser)
                }
                
                // Add multiple checks to ensure the state persists
                repeat(3) { attempt ->
                    delay(500)
                    if (!_isLoggedIn.value || _currentUser.value == null || !_isBypassUser.value) {
                        Log.w(TAG, "Bypass login state was lost after check ${attempt + 1}, restoring it")
                        _currentUser.value = tempUser
                        _isLoggedIn.value = true
                        _isBypassUser.value = true
                        _uiState.value = LoginUiState.Success(tempUser)
                    } else {
                        Log.d(TAG, "Bypass login state check ${attempt + 1} passed")
                    }
                }
                
                // Disable loading state
                _isLoading.value = false
            } catch (e: Exception) {
                Log.e(TAG, "Error during bypass login", e)
                
                // Even if there's an error, still set the bypass user
                val tempUser = User(
                    id = "temp_user_id",
                    username = "TestUser",
                    email = "<EMAIL>",
                    hashedPassword = "dummy_hashed_password",
                    salt = "dummy_salt",
                    createdAt = System.currentTimeMillis(),
                    lastLogin = System.currentTimeMillis()
                )
                
                _currentUser.value = tempUser
                _isLoggedIn.value = true
                _isBypassUser.value = true
                _uiState.value = LoginUiState.Success(tempUser)
                
                _isLoading.value = false
            }
        }
    }

    // Add a debug login function for testing
    fun debugLogin() {
        Log.d(TAG, "Using debug login")
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Reset any previous state
                _uiState.value = LoginUiState.Initial
                
                // Create a test user
                val testUser = User(
                    id = "test-user-id",
                    username = "TestUser",
                    email = "<EMAIL>",
                    hashedPassword = "",
                    salt = "",
                    createdAt = System.currentTimeMillis(),
                    lastLogin = System.currentTimeMillis()
                )
                
                // Use the bypassAuth method to ensure persistence
                authService.bypassAuth(testUser)
                
                // Update states
                _currentUser.value = testUser
                _isLoggedIn.value = true
                _isBypassUser.value = true
                _uiState.value = LoginUiState.Success(testUser)
                
                Log.d(TAG, "Debug login successful - User is now logged in")
                
                // Add a delay before allowing any other state changes
                delay(500)
            } catch (e: Exception) {
                Log.e(TAG, "Debug login failed", e)
                _uiState.value = LoginUiState.Error("Debug login failed: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _uiState.value = LoginUiState.Initial
        _registrationState.value = RegistrationState.Initial
        _isLoading.value = false
    }

    fun getGoogleSignInIntent() = googleSignInHelper.getSignInIntent()

    fun handleGoogleSignIn(idToken: String) {
        viewModelScope.launch {
            Log.d(TAG, "Handling Google sign in")
            try {
                // Reset states first
                _isLoading.value = true
                _uiState.value = LoginUiState.Loading
                _isLoggedIn.value = false
                _currentUser.value = null

                val result = googleSignInHelper.handleSignInResult(idToken)
                
                if (result.success && result.user != null) {
                    Log.d(TAG, "Google sign in successful for user: ${result.user.username}")
                    
                    // Update registration state first (in case this is a new user)
                    _registrationState.value = RegistrationState.Success(result.user)
                    
                    // Force immediate UI update
                    _currentUser.value = result.user
                    _isLoggedIn.value = true
                    _uiState.value = LoginUiState.Success(result.user)
                    
                    // Ensure Firebase state is synced
                    authService.refreshUserState()
                    
                    // Then do the full state update with retries
                    repeat(3) { attempt ->
                        delay((attempt + 1) * 300L) // 300ms, 600ms, 900ms
                        try {
                            authService.refreshUserState()
                            val isLoggedInValue = authService.isLoggedIn.value
                            val currentUserValue = authService.currentUser.value
                            
                            _isLoggedIn.value = isLoggedInValue
                            _currentUser.value = currentUserValue
                            if (currentUserValue != null) {
                                _uiState.value = LoginUiState.Success(currentUserValue)
                            }
                            Log.d(TAG, "State refresh attempt ${attempt + 1} successful")
                        } catch (e: Exception) {
                            Log.e(TAG, "State refresh attempt ${attempt + 1} failed", e)
                        }
                    }
                } else {
                    throw Exception(result.errorMessage ?: "Google sign in failed")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Google sign in failed", e)
                _uiState.value = LoginUiState.Error(e.message ?: "Google sign in failed")
                resetStates()
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Checks if the current user is a bypass user
     */
    fun isCurrentUserBypass(): Boolean {
        return authService.isCurrentUserBypass()
    }

    /**
     * Signs in anonymously with Firebase Auth
     */
    fun signInAnonymously() {
        Log.d(TAG, "Initiating anonymous sign-in")
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _uiState.value = LoginUiState.Loading
                
                val result = authService.signInAnonymously()
                
                if (result.success && result.user != null) {
                    Log.d(TAG, "Anonymous sign-in successful: ${result.user.id}")
                    _currentUser.value = result.user
                    _isLoggedIn.value = true
                    _uiState.value = LoginUiState.Success(result.user)
                } else {
                    Log.e(TAG, "Anonymous sign-in failed: ${result.errorMessage}")
                    _uiState.value = LoginUiState.Error(result.errorMessage ?: "Anonymous sign-in failed")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during anonymous sign-in", e)
                _uiState.value = LoginUiState.Error("Error during anonymous sign-in: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            try {
                googleSignInHelper.signOut()
            } catch (e: Exception) {
                Log.e(TAG, "Error signing out from Google", e)
            }
        }
    }
}

sealed class LoginUiState {
    object Initial : LoginUiState()
    object Loading : LoginUiState()
    data class Success(val user: User) : LoginUiState()
    data class Error(val message: String) : LoginUiState()
}

sealed class RegistrationState {
    object Initial : RegistrationState()
    object Loading : RegistrationState()
    data class Success(val user: User) : RegistrationState()
    data class Error(val message: String) : RegistrationState()
} 