# Firebase Cloud Function for Message Notifications

This guide will help you set up a Firebase Cloud Function that sends push notifications when a user receives a new message.

## Prerequisites

1. Node.js installed on your development machine
2. Firebase CLI installed (`npm install -g firebase-tools`)
3. A Firebase project with Firestore and Cloud Messaging enabled

## Setup Steps

### 1. Initialize Firebase Cloud Functions

```bash
# Login to Firebase
firebase login

# Initialize Firebase in your project directory
cd /path/to/your/project
firebase init functions
```

Choose JavaScript when prompted for the language.

### 2. Create the Cloud Function

Replace the contents of `functions/index.js` with the following code:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.sendMessageNotification = functions.firestore
    .document('messages/{messageId}')
    .onCreate(async (snapshot, context) => {
        const messageData = snapshot.data();
        const recipientUid = messageData.recipientUid;
        const senderUid = messageData.senderUid;
        const messageContent = messageData.message;
        
        // Don't send notifications for messages to yourself
        if (recipientUid === senderUid) {
            console.log('Skipping notification for self-message');
            return null;
        }
        
        try {
            // Get recipient's FCM token
            const recipientDoc = await admin.firestore()
                .collection('users')
                .doc(recipientUid)
                .get();
                
            if (!recipientDoc.exists) {
                console.log('Recipient does not exist');
                return null;
            }
            
            const recipientData = recipientDoc.data();
            const fcmToken = recipientData.fcmToken;
            
            if (!fcmToken) {
                console.log('Recipient has no FCM token');
                return null;
            }
            
            // Get sender's username
            const senderDoc = await admin.firestore()
                .collection('users')
                .doc(senderUid)
                .get();
                
            let senderName = "Someone";
            if (senderDoc.exists) {
                senderName = senderDoc.data().username || "Someone";
            }
            
            // Truncate message if too long
            const shortMessage = messageContent.length > 100 
                ? messageContent.substring(0, 97) + '...' 
                : messageContent;
            
            // Create notification
            const payload = {
                notification: {
                    title: `New Message from ${senderName}`,
                    body: shortMessage,
                    clickAction: 'OPEN_INBOX',
                    sound: 'default'
                },
                data: {
                    messageId: context.params.messageId,
                    senderUid: senderUid,
                    senderName: senderName,
                    messageContent: shortMessage,
                    type: 'new_message'
                }
            };
            
            // Send notification
            const response = await admin.messaging().sendToDevice(fcmToken, payload);
            
            console.log('Notification sent successfully:', response);
            return { success: true };
        } catch (error) {
            console.error('Error sending notification:', error);
            return { success: false, error: error.message };
        }
    });
```

### 3. Deploy the Cloud Function

```bash
firebase deploy --only functions
```

## Testing

1. Make sure your app is storing FCM tokens in the user documents in Firestore
2. Send a message from one user to another
3. The recipient should receive a notification if the app is in the background
4. Clicking the notification should open the app and show the inbox

## Troubleshooting

If notifications aren't working:

1. Check the Firebase Functions logs in the Firebase Console
2. Verify that FCM tokens are being stored correctly in Firestore
3. Make sure the recipient has granted notification permissions
4. Test with a physical device, as emulators may not show notifications properly

## Additional Configuration

### Notification Channels

The app already has notification channels configured for Android 8.0+. If you need to customize them:

1. Open `FirebaseMessagingService.kt`
2. Modify the channel settings in the `showMessageNotification` method

### Notification Appearance

To customize notification appearance:

1. Update the `showMessageNotification` method in `FirebaseMessagingService.kt`
2. You can change the icon, sound, priority, etc.

### Background vs. Foreground Handling

The current implementation handles notifications differently based on whether the app is in the foreground or background:

- Foreground: The notification is processed by `onMessageReceived` and displayed by the app
- Background: The notification is automatically displayed by FCM

## Security Rules

Make sure your Firestore security rules allow reading and writing FCM tokens:

```
match /users/{userId} {
  allow read: if request.auth != null;
  allow write: if request.auth != null && request.auth.uid == userId;
  allow update: if request.auth != null && 
                request.auth.uid == userId && 
                request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'lastLogin']);
}
``` 