package com.ammar.medical.domain.service

import android.content.Context
import android.net.Uri
import androidx.core.content.FileProvider
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.CategoryRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DocumentExporter @Inject constructor(
    @ApplicationContext private val context: Context,
    private val categoryRepository: CategoryRepository
) {
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())

    suspend fun exportDocuments(documents: List<Document>, format: ExportFormat): Uri {
        val exportFile = when (format) {
            ExportFormat.JSON -> exportToJson(documents)
            ExportFormat.ZIP -> exportToZip(documents)
        }

        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            exportFile
        )
    }

    private suspend fun exportToJson(documents: List<Document>): File {
        val jsonArray = JSONArray()
        documents.forEach { document ->
            val category = categoryRepository.getCategoryById(document.categoryId)
            val jsonObject = JSONObject().apply {
                put("id", document.id)
                put("title", document.title)
                put("description", document.description)
                put("type", document.type.name)
                put("date", document.date)
                put("tags", JSONArray(document.tags))
                put("category", JSONObject().apply {
                    put("id", category.id)
                    put("name", category.name)
                })
            }
            jsonArray.put(jsonObject)
        }

        val exportFile = createExportFile("documents", "json")
        FileOutputStream(exportFile).use { stream ->
            stream.write(jsonArray.toString(2).toByteArray())
        }
        return exportFile
    }

    private suspend fun exportToZip(documents: List<Document>): File {
        val exportFile = createExportFile("documents", "zip")
        
        ZipOutputStream(FileOutputStream(exportFile)).use { zipStream ->
            // Add metadata file
            val metadataEntry = ZipEntry("metadata.json")
            zipStream.putNextEntry(metadataEntry)
            val jsonArray = JSONArray()
            documents.forEach { document ->
                val category = categoryRepository.getCategoryById(document.categoryId)
                val jsonObject = JSONObject().apply {
                    put("id", document.id)
                    put("title", document.title)
                    put("description", document.description)
                    put("type", document.type.name)
                    put("date", document.date)
                    put("tags", JSONArray(document.tags))
                    put("category", JSONObject().apply {
                        put("id", category.id)
                        put("name", category.name)
                    })
                    put("filename", "${document.id}_${document.title.replace(" ", "_")}")
                }
                jsonArray.put(jsonObject)
            }
            zipStream.write(jsonArray.toString(2).toByteArray())
            zipStream.closeEntry()

            // Add document files
            documents.forEach { document ->
                val documentFile = File(document.uri)
                if (documentFile.exists()) {
                    val extension = documentFile.extension
                    val entryName = "${document.id}_${document.title.replace(" ", "_")}.$extension"
                    val entry = ZipEntry(entryName)
                    zipStream.putNextEntry(entry)
                    documentFile.inputStream().use { input ->
                        input.copyTo(zipStream)
                    }
                    zipStream.closeEntry()
                }
            }
        }
        
        return exportFile
    }

    private fun createExportFile(prefix: String, extension: String): File {
        val timestamp = dateFormat.format(Date())
        val fileName = "${prefix}_${timestamp}.$extension"
        return File(context.cacheDir, fileName)
    }

    enum class ExportFormat {
        JSON,
        ZIP
    }
} 