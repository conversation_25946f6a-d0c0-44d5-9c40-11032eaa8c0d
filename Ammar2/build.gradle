buildscript {
    ext {
        compose_version = '1.5.7'
        kotlin_version = '1.9.22'
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

plugins {
    id 'com.android.application' version '8.2.0' apply false
    id 'com.android.library' version '8.2.0' apply false
    id 'org.jetbrains.kotlin.android' version "$kotlin_version" apply false
    id 'com.google.devtools.ksp' version '1.9.22-1.0.16' apply false
    id 'com.google.gms.google-services' version '4.3.15' apply false
}

// Task to print Java version
task printJavaVersion {
    doLast {
        println "Java version: ${System.getProperty('java.version')}"
        println "Java home: ${System.getProperty('java.home')}"
        println "JVM version: ${System.getProperty('java.vm.version')}"
    }
} 