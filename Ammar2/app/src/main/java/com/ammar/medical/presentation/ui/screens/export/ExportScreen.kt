package com.ammar.medical.presentation.ui.screens.export

import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import androidx.hilt.navigation.compose.hiltViewModel
import com.ammar.medical.R
import com.ammar.medical.data.model.Document
import com.ammar.medical.domain.service.DocumentExporter.ExportFormat
import com.ammar.medical.presentation.components.CommonTopAppBar
import com.ammar.medical.presentation.components.LoadingIndicator
import java.io.File

@Composable
fun ExportScreen(
    onNavigateBack: () -> Unit,
    viewModel: ExportViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(uiState) {
        val state = uiState as? ExportUiState.Success ?: return@LaunchedEffect
        state.exportUri?.let { uri ->
            val file = File(uri)
            val fileUri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = when (state.selectedFormat) {
                    ExportFormat.JSON -> "application/json"
                    ExportFormat.ZIP -> "application/zip"
                }
                putExtra(Intent.EXTRA_STREAM, fileUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            context.startActivity(Intent.createChooser(intent, "Share exported documents"))
            viewModel.clearExportUri()
        }
    }

    Scaffold(
        topBar = {
            CommonTopAppBar(
                title = stringResource(R.string.export_documents),
                navigationIcon = Icons.Default.ArrowBack,
                onNavigationClick = onNavigateBack,
                actions = {
                    val state = uiState as? ExportUiState.Success
                    if (state != null && state.selectedDocuments.isNotEmpty()) {
                        IconButton(onClick = viewModel::clearSelection) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = stringResource(R.string.clear_selection)
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (uiState) {
                is ExportUiState.Loading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is ExportUiState.Success -> {
                    val state = uiState as ExportUiState.Success
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        ExportFormatSelector(
                            selectedFormat = state.selectedFormat,
                            onFormatSelected = viewModel::updateExportFormat,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        )

                        DocumentList(
                            documents = state.documents,
                            selectedDocuments = state.selectedDocuments,
                            onDocumentSelected = viewModel::toggleDocumentSelection,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                        )

                        ExportButton(
                            selectedCount = state.selectedDocuments.size,
                            isExporting = state.isExporting,
                            onExportClick = viewModel::exportDocuments,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        )
                    }

                    state.error?.let { error ->
                        ErrorSnackbar(
                            message = error,
                            onDismiss = viewModel::clearError
                        )
                    }
                }
                is ExportUiState.Error -> {
                    Text(
                        text = (uiState as ExportUiState.Error).message,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun ExportFormatSelector(
    selectedFormat: ExportFormat,
    onFormatSelected: (ExportFormat) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(R.string.export_format),
            style = MaterialTheme.typography.titleMedium
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            ExportFormat.values().forEach { format ->
                FilterChip(
                    selected = format == selectedFormat,
                    onClick = { onFormatSelected(format) },
                    label = { Text(format.name) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun DocumentList(
    documents: List<Document>,
    selectedDocuments: Set<Long>,
    onDocumentSelected: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(documents) { document ->
            DocumentItem(
                document = document,
                isSelected = document.id in selectedDocuments,
                onSelected = { onDocumentSelected(document.id) }
            )
        }
    }
}

@Composable
private fun DocumentItem(
    document: Document,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onSelected
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isSelected,
                onCheckedChange = { onSelected() }
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = document.title,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = document.description,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 2
                )
            }
        }
    }
}

@Composable
private fun ExportButton(
    selectedCount: Int,
    isExporting: Boolean,
    onExportClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onExportClick,
        enabled = selectedCount > 0 && !isExporting,
        modifier = modifier
    ) {
        if (isExporting) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(
            text = if (selectedCount > 0) {
                stringResource(
                    R.string.export_selected_documents,
                    selectedCount
                )
            } else {
                stringResource(R.string.select_documents_to_export)
            }
        )
    }
}

@Composable
private fun ErrorSnackbar(
    message: String,
    onDismiss: () -> Unit
) {
    Snackbar(
        modifier = Modifier.padding(16.dp),
        action = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.dismiss))
            }
        }
    ) {
        Text(message)
    }
} 