package io.ammar.medical.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.ammar.medical.utils.NetworkUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow

/**
 * A composable that displays the network status at the top of the screen
 */
@Composable
fun NetworkStatusBar(
    modifier: Modifier = Modifier,
    networkStatusFlow: Flow<Boolean>? = null
) {
    val context = LocalContext.current
    val isConnected = networkStatusFlow?.collectAsState(initial = NetworkUtils.isNetworkAvailable(context))
        ?: remember { mutableStateOf(NetworkUtils.isNetworkAvailable(context)) }
    
    var showBanner by remember { mutableStateOf(false) }
    
    // Only show the banner when disconnected
    LaunchedEffect(isConnected.value) {
        showBanner = !isConnected.value
        
        // If we're connected, hide the banner after a delay
        if (isConnected.value && showBanner) {
            delay(3000)
            showBanner = false
        }
    }
    
    AnimatedVisibility(
        visible = showBanner,
        enter = expandVertically(),
        exit = shrinkVertically()
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .background(Color.Red.copy(alpha = 0.8f))
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No internet connection",
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
        }
    }
} 