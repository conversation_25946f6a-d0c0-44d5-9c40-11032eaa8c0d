package com.ammar.medical.presentation.ui.screens.export

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Document
import com.ammar.medical.domain.service.DocumentExporter
import com.ammar.medical.domain.service.DocumentExporter.ExportFormat
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class ExportUiState {
    data object Loading : ExportUiState()
    data class Success(
        val documents: List<Document>,
        val selectedDocuments: Set<Long>,
        val selectedFormat: ExportFormat,
        val isExporting: Boolean,
        val exportUri: String? = null,
        val error: String? = null
    ) : ExportUiState()
    data class Error(val message: String) : ExportUiState()
}

@HiltViewModel
class ExportViewModel @Inject constructor(
    private val documentRepository: DocumentRepository,
    private val documentExporter: DocumentExporter
) : ViewModel() {

    private val _uiState = MutableStateFlow<ExportUiState>(ExportUiState.Loading)
    val uiState: StateFlow<ExportUiState> = _uiState

    init {
        loadDocuments()
    }

    private fun loadDocuments() {
        viewModelScope.launch {
            try {
                val documents = documentRepository.getAllDocuments()
                _uiState.value = ExportUiState.Success(
                    documents = documents,
                    selectedDocuments = emptySet(),
                    selectedFormat = ExportFormat.JSON,
                    isExporting = false
                )
            } catch (e: Exception) {
                _uiState.value = ExportUiState.Error(e.message ?: "Failed to load documents")
            }
        }
    }

    fun toggleDocumentSelection(documentId: Long) {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedDocuments = if (documentId in currentState.selectedDocuments) {
                currentState.selectedDocuments - documentId
            } else {
                currentState.selectedDocuments + documentId
            }
        )
    }

    fun selectAllDocuments() {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(
            selectedDocuments = currentState.documents.map { it.id }.toSet()
        )
    }

    fun clearSelection() {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(selectedDocuments = emptySet())
    }

    fun updateExportFormat(format: ExportFormat) {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(selectedFormat = format)
    }

    fun exportDocuments() {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        if (currentState.selectedDocuments.isEmpty()) {
            _uiState.value = currentState.copy(error = "Please select at least one document")
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = currentState.copy(isExporting = true, error = null)
                val selectedDocs = currentState.documents.filter { it.id in currentState.selectedDocuments }
                val uri = documentExporter.exportDocuments(selectedDocs, currentState.selectedFormat)
                _uiState.value = currentState.copy(
                    isExporting = false,
                    exportUri = uri.toString(),
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = currentState.copy(
                    isExporting = false,
                    error = e.message ?: "Failed to export documents"
                )
            }
        }
    }

    fun clearError() {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(error = null)
    }

    fun clearExportUri() {
        val currentState = _uiState.value as? ExportUiState.Success ?: return
        _uiState.value = currentState.copy(exportUri = null)
    }
} 