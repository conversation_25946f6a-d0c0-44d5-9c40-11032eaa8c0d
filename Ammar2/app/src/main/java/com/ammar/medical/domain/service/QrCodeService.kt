package com.ammar.medical.domain.service

import android.graphics.Bitmap
import android.graphics.Color
import com.google.zxing.BarcodeFormat
import com.google.zxing.MultiFormatWriter
import com.google.zxing.common.BitMatrix
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class QrCodeService @Inject constructor() {

    fun generateQrCode(content: String, size: Int = 512): Bitmap {
        val bitMatrix = MultiFormatWriter().encode(
            content,
            BarcodeFormat.QR_CODE,
            size,
            size
        )
        return createBitmap(bitMatrix)
    }

    private fun createBitmap(bitMatrix: BitMatrix): Bitmap {
        val width = bitMatrix.width
        val height = bitMatrix.height
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)

        for (x in 0 until width) {
            for (y in 0 until height) {
                bitmap.setPixel(x, y, if (bitMatrix[x, y]) Color.BLACK else Color.WHITE)
            }
        }

        return bitmap
    }

    fun generateDocumentQrCode(documentId: Long): Bitmap {
        val content = "ammar_medical://document/$documentId"
        return generateQrCode(content)
    }

    fun parseDocumentQrCode(content: String): Long? {
        return if (content.startsWith("ammar_medical://document/")) {
            content.substringAfterLast("/").toLongOrNull()
        } else {
            null
        }
    }
} 