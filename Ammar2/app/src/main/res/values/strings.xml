<resources>
    <string name="app_name">SmartMed</string>
    
    <!-- Common UI elements -->
    <string name="back">Back</string>
    <string name="share">Share</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="done">Done</string>
    <string name="settings">Settings</string>
    <string name="loading">Loading...</string>
    <string name="confirm">Confirm</string>
    <string name="navigate_back">Navigate back</string>
    
    <!-- Main UI elements -->
    <string name="medical_reports_analyzer">Medical Reports Analyzer</string>
    <string name="multi_image_analyzer">SmartMed</string>
    <string name="upload_medical_reports">Upload medical reports, lab results, or radiology images to get an AI-powered analysis.</string>
    
    <!-- Settings screen -->
    <string name="ai_settings">AI Settings</string>
    <string name="app_settings">App Settings</string>
    <string name="language">Language</string>
    <string name="language_settings">Language Settings</string>
    <string name="select_language">Select Language</string>
    <string name="english">English</string>
    <string name="arabic">Arabic</string>
    <string name="system_default">System Default</string>
    
    <!-- API Key settings -->
    <string name="set_custom_api_key">Set Custom API Key</string>
    <string name="gemini_api_key">Gemini API Key</string>
    <string name="enter_gemini_api_key">Enter your Gemini API Key</string>
    <string name="save_key">Save Key</string>
    <string name="use_default">Use Default</string>
    <string name="api_key_saved">API Key saved successfully</string>
    <string name="using_default_api_key">Using default API key</string>
    <string name="get_api_key">Get API Key</string>
    
    <!-- File actions -->
    <string name="images">Images</string>
    <string name="pdfs">PDFs</string>
    <string name="camera">Camera</string>
    <string name="zip">ZIP</string>
    <string name="files">Files</string>
    <string name="selected_files">Selected Files (%1$d)</string>
    <string name="extracted_text">Extracted Text</string>
    <string name="analyze_selected_files">Analyze Selected Files</string>
    <string name="select_all">Select All</string>
    <string name="unselect_all">Unselect All</string>
    
    <!-- Analysis results -->
    <string name="medical_analysis">SmartMed Analysis</string>
    <string name="share_results">Share Results</string>
    <string name="send_to_doctor">Send to Doctor</string>
    <string name="share_medical_analysis">Share SmartMed Analysis</string>
    
    <!-- Doctor sharing options -->
    <string name="email">Email</string>
    <string name="my_doctors">My Doctors</string>
    <string name="other_apps">Other Apps</string>
    <string name="doctors_email">Doctor\'s Email</string>
    <string name="selected_doctors">Selected Doctors (%1$d)</string>
    <string name="send">Send</string>
    
    <!-- ZIP file handling -->
    <string name="extracting_zip">Extracting ZIP file</string>
    <string name="process_zip_archive">Processing ZIP archive</string>
    <string name="files_by_date">Files by Date</string>
    <string name="files_by_folder">Files by Folder</string>
    <string name="folders">Folders</string>
    <string name="by_date">By Date</string>
    <string name="filter">Filter</string>
    <string name="files_found">%1$d files found • %2$d selected</string>
    <string name="selected">Selected</string>
    <string name="start_extraction">Start Extraction</string>
    <string name="add_selected_files">Add Selected Files</string>
    
    <!-- MultiImageAnalyzer Screen -->
    <string name="upload_intro">Upload medical reports, lab results, or X-ray images for AI-powered analysis.</string>
    <string name="pdf_document">PDF Document</string>
    <string name="image">Image</string>
    <string name="remove_file">Remove File</string>
    <string name="error_processing_file">Error processing file</string>
    <string name="extracting_text">Extracting text...</string>
    <string name="no_text_detected">No text detected in this file</string>
    <string name="files_selected_for_analysis">%1$d of %2$d files selected for analysis</string>
    <string name="generating_analysis">Generating comprehensive analysis...</string>
    <string name="analysis_sent_success">Analysis sent successfully!</string>
    <string name="analysis_send_failed">Failed to send analysis. Please try again.</string>
    <string name="change_selection">Change Selection</string>
    <string name="share_via_other_apps">Share your medical analysis via other apps</string>
    <string name="share_menu_description">This will open the share menu where you can choose an app to share your analysis with.</string>
    <string name="whatsapp">WhatsApp</string>
    <string name="send_to_doctors">Send to %1$d Doctors</string>
    <string name="select_contact">Select Contact</string>
    <string name="loading_contacts">Loading contacts from WhatsApp chat...</string>
    <string name="failed_filter_contact">Failed to filter by contact</string>
    <string name="prepare_extract">Preparing to extract files from ZIP</string>
    <string name="process_archive">Processing %1$s</string>
    <string name="files_selected">%1$d of %2$d files selected</string>
    <string name="app_version">App version: %1$s</string>
    <string name="files_count">Files: %1$d</string>
    <string name="approaching_limit">Approaching limit</string>
    <string name="open_folder">Open Folder</string>
    <string name="text_rephraser">Text Rephraser</string>
    <string name="qr_scanner">QR Code Scanner</string>
    <string name="activation_code">Activation Code</string>
    <string name="enter_activation_code">Enter activation code</string>
    <string name="activate">Activate</string>
    <string name="activation_success">Activation successful!</string>
    <string name="activation_error">Invalid activation code</string>
    <string name="activation_expired">Your activation code is no longer valid. Please enter a new code.</string>
    <string name="activation_codes_refreshed">Activation codes refreshed</string>
    <string name="refresh_activation">Refresh</string>
    <string name="premium_features">Premium Features</string>
    <string name="premium_features_description">Enter an activation code to unlock premium analysis features</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="text_extraction_free">Text extraction is available for free, but analysis requires activation.</string>
    <string name="server_ip">*************</string>
    
    <!-- QR Scanner Screen -->
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="scanning_qr_code">Position the QR code within the frame</string>
    <string name="toggle_flash">Toggle Flash</string>
    
    <!-- Document Details Screen -->
    <string name="document_details">Document Details</string>
    <string name="more_options">More Options</string>
    <string name="edit_document">Edit Document</string>
    <string name="share_document">Share Document</string>
    <string name="delete_document">Delete Document</string>
    
    <!-- Document Preview -->
    <string name="error_loading_preview">Failed to load document preview</string>
    <string name="error_loading_pdf">Failed to load PDF document</string>
    <string name="previous_page">Previous page</string>
    <string name="next_page">Next page</string>
    
    <!-- Search Screen -->
    <string name="search">Search</string>
    <string name="search_hint">Search documents…</string>
    <string name="filters">Filters</string>
    <string name="document_types">Document Types</string>
    <string name="sort_by">Sort By</string>
    <string name="no_results">No documents found</string>
    
    <!-- Document Edit Screen -->
    <string name="title">Title</string>
    <string name="description">Description</string>
    <string name="tags">Tags</string>
    <string name="remove_tag">Remove tag</string>
    <string name="add_tag">Add tag</string>
    <string name="clear">Clear</string>
    
    <!-- Document Sharing -->
    <string name="share_qr_code">Share QR Code</string>
    <string name="share_document">Share Document</string>
    <string name="share_document_details">Share Document Details</string>
    <string name="share_document_qr">Share Document QR Code</string>
    <string name="share_document_file">Share Document File</string>
    <string name="share_document_all">Share Document with QR Code</string>
    
    <!-- Document Deletion -->
    <string name="delete_confirmation_message">Are you sure you want to delete this document? This action cannot be undone.</string>
    
    <!-- Categories -->
    <string name="categories">Categories</string>
    <string name="category">Category</string>
    <string name="uncategorized">Uncategorized</string>
    <string name="add_category">Add Category</string>
    <string name="edit_category">Edit Category</string>
    <string name="delete_category">Delete Category</string>
    <string name="category_name">Category Name</string>
    <string name="select_icon">Select Icon</string>
    <string name="select_color">Select Color</string>
    <string name="category_name_required">Category name is required</string>
    <string name="category_exists">A category with this name already exists</string>
    <string name="category_deleted">Category deleted</string>
    <string name="category_updated">Category updated</string>
    <string name="category_added">Category added</string>
    
    <!-- Export -->
    <string name="export">Export</string>
    <string name="export_documents">Export Documents</string>
    <string name="export_format">Export Format</string>
    <string name="export_json">Export as JSON</string>
    <string name="export_zip">Export as ZIP</string>
    <string name="exporting">Exporting documents…</string>
    <string name="export_success">Documents exported successfully</string>
    <string name="export_error">Failed to export documents</string>
    <string name="select_export_format">Select Export Format</string>
    <string name="json_format">JSON Format</string>
    <string name="zip_format">ZIP Format (with files)</string>
    
    <!-- Category Management Screen -->
    <string name="categories">Categories</string>
    <string name="add_category">Add Category</string>
    <string name="edit_category">Edit Category</string>
    <string name="delete_category">Delete Category</string>
    <string name="category_name">Category Name</string>
    <string name="select_icon">Select Icon</string>
    <string name="select_color">Select Color</string>
    <string name="category_added">Category added successfully</string>
    <string name="category_updated">Category updated successfully</string>
    <string name="category_deleted">Category deleted successfully</string>
    <string name="category_error_empty_name">Category name cannot be empty</string>
    <string name="category_error_duplicate">A category with this name already exists</string>
    <string name="delete_confirmation_message">Are you sure you want to delete this category? This action cannot be undone.</string>
    
    <!-- Export Screen -->
    <string name="export_documents">Export Documents</string>
    <string name="export_format">Export Format</string>
    <string name="export_selected_documents">Export Selected Documents (%1$d)</string>
    <string name="select_documents_to_export">Select Documents to Export</string>
    <string name="clear_selection">Clear Selection</string>
    <string name="dismiss">Dismiss</string>
    <string name="export_success">Documents exported successfully</string>
    <string name="export_error">Failed to export documents</string>
    <string name="no_documents_selected">Please select at least one document</string>
    
    <!-- Category Statistics -->
    <string name="category_statistics">Category Statistics</string>
    <string name="total_statistics">Total Statistics</string>
    <string name="total_documents_count">Total Documents: %d</string>
    <string name="total_size">Total Size: %s</string>
    <string name="document_count">%d Documents</string>
    <string name="average_age">Average Age: %d days</string>
    <string name="refresh">Refresh</string>
    
    <!-- Navigation Drawer -->
    <string name="open_menu">Open menu</string>
    <string name="home">Home</string>
    <string name="search">Search</string>
    <string name="categories">Categories</string>
    <string name="category_statistics">Category Statistics</string>
    <string name="export">Export</string>
    <string name="settings">Settings</string>
</resources>