package io.ammar.medical.viewmodel

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.ammar.medical.R
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.generationConfig
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.DocumentSnapshot
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.Locale

/**
 * ViewModel for application settings
 */
class SettingsViewModel : ViewModel() {
    // Gemini API key state
    private val _apiKey = MutableStateFlow<String?>(null)
    val apiKey: StateFlow<String?> = _apiKey.asStateFlow()
    
    // API key validation state
    private val _isValidatingApiKey = MutableStateFlow(false)
    val isValidatingApiKey: StateFlow<Boolean> = _isValidatingApiKey.asStateFlow()
    
    private val _apiKeyValidationError = MutableStateFlow<String?>(null)
    val apiKeyValidationError: StateFlow<String?> = _apiKeyValidationError.asStateFlow()
    
    private val _apiKeyValidationSuccess = MutableStateFlow(false)
    val apiKeyValidationSuccess: StateFlow<Boolean> = _apiKeyValidationSuccess.asStateFlow()
    
    // Default API key from resources (should be stored securely in a real app)
    private val _isUsingDefaultKey = MutableStateFlow(false)
    val isUsingDefaultKey: StateFlow<Boolean> = _isUsingDefaultKey.asStateFlow()
    
    // Language state
    private val _appLanguage = MutableStateFlow<String>("system")
    val appLanguage: StateFlow<String> = _appLanguage.asStateFlow()
    
    // Activation code state
    private val _activationCode = MutableStateFlow<String?>(null)
    val activationCode: StateFlow<String?> = _activationCode.asStateFlow()
    
    private val _isActivated = MutableStateFlow(false)
    val isActivated: StateFlow<Boolean> = _isActivated.asStateFlow()
    
    private val _activationError = MutableStateFlow<String?>(null)
    val activationError: StateFlow<String?> = _activationError.asStateFlow()
    
    private val _isLoadingActivationCodes = MutableStateFlow(false)
    val isLoadingActivationCodes: StateFlow<Boolean> = _isLoadingActivationCodes.asStateFlow()
    
    // Firebase Firestore reference
    private val firestore = FirebaseFirestore.getInstance()
    
    // List of valid activation codes from Firestore
    private var validActivationCodes = mutableSetOf<String>()
    
    // Constants
    private val TAG = "SettingsViewModel"
    private val ACTIVATION_COLLECTION = "configuration"
    private val ACTIVATION_DOCUMENT = "activation_codes"
    
    init {
        // Fetch activation codes when ViewModel is created
        viewModelScope.launch {
            fetchActivationCodes()
        }
    }
    
    // Initialize settings
    fun loadSettings(context: Context) {
        Log.d(TAG, "Loading settings from SharedPreferences")
        
        val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        val savedApiKey = sharedPreferences.getString("gemini_api_key", null)
        val savedLanguage = sharedPreferences.getString("app_language", "system")
        val savedActivationCode = sharedPreferences.getString("activation_code", null)
        
        Log.d(TAG, "Saved settings - Language: $savedLanguage, Has API Key: ${!savedApiKey.isNullOrEmpty()}, Activation Code: $savedActivationCode")
        
        _apiKey.value = savedApiKey
        _isUsingDefaultKey.value = savedApiKey == null || savedApiKey.isEmpty()
        _appLanguage.value = savedLanguage ?: "system"
        _activationCode.value = savedActivationCode
        
        // Verify the saved activation code if we already have codes loaded
        if (!validActivationCodes.isEmpty()) {
            Log.d(TAG, "Validating activation code immediately with preloaded codes")
            verifyActivationCode(context, savedActivationCode)
        } else {
            Log.d(TAG, "Activation codes not yet loaded, verification will happen after fetching")
            // Initial state - will be updated after codes are fetched
            _isActivated.value = false
        }
    }
    
    // Fetch activation codes from Firestore
    private suspend fun fetchActivationCodes() {
        _isLoadingActivationCodes.value = true
        
        try {
            Log.d(TAG, "Starting to fetch activation codes from Firestore")
            
            // Enable Firestore debug logging temporarily
            FirebaseFirestore.setLoggingEnabled(true)
            
            val document = firestore.collection(ACTIVATION_COLLECTION)
                .document(ACTIVATION_DOCUMENT)
                .get()
                .await()
            
            Log.d(TAG, "Firestore response received. Document exists: ${document.exists()}")
            Log.d(TAG, "Firestore path: $ACTIVATION_COLLECTION/$ACTIVATION_DOCUMENT")
            
            if (document.exists()) {
                // Log all fields in the document for debugging
                val allFields = document.data
                Log.d(TAG, "Document fields: ${allFields?.keys?.joinToString() ?: "none"}")
                
                // Get the codes - supporting multiple formats for flexibility
                val extractedCodes = extractCodesFromDocument(document)
                
                if (extractedCodes.isNotEmpty()) {
                    validActivationCodes.clear()
                    validActivationCodes.addAll(extractedCodes)
                    Log.d(TAG, "Fetched ${extractedCodes.size} activation codes: ${extractedCodes.joinToString()}")
                    
                    // Check if current activation code is still valid
                    if (_activationCode.value != null) {
                        _isActivated.value = validActivationCodes.any { it.equals(_activationCode.value, ignoreCase = true) }
                        Log.d(TAG, "Current activation code '${_activationCode.value}' valid: ${_isActivated.value}")
                    }
                } else {
                    Log.e(TAG, "Could not extract valid activation codes from document")
                    Log.e(TAG, "Raw data: ${document.data}")
                }
            } else {
                Log.e(TAG, "Activation codes document does not exist in path: $ACTIVATION_COLLECTION/$ACTIVATION_DOCUMENT")
                // Create default activation codes for development
                if (validActivationCodes.isEmpty()) {
                    validActivationCodes.add("MEDICAI2024")
                    validActivationCodes.add("Ammar")
                    Log.d(TAG, "Using default activation codes for development")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching activation codes: ${e.message}", e)
            // Use default codes in case of error
            if (validActivationCodes.isEmpty()) {
                validActivationCodes.add("MEDICAI2024")
                validActivationCodes.add("Ammar")
                Log.d(TAG, "Using default activation codes due to error")
            }
        } finally {
            // Disable debug logging when done
            FirebaseFirestore.setLoggingEnabled(false)
            _isLoadingActivationCodes.value = false
        }
    }
    
    // Extract codes from various document formats for flexibility
    private fun extractCodesFromDocument(document: DocumentSnapshot): List<String> {
        val extractedCodes = mutableListOf<String>()
        
        try {
            // Try to get codes as a List<String> (standard format)
            val codesList = document.get("codes") as? List<*>
            if (codesList != null) {
                Log.d(TAG, "Found codes as List: $codesList")
                codesList.forEach { code ->
                    if (code is String) {
                        extractedCodes.add(code)
                    }
                }
            }
            
            // If no codes found as list, try other formats
            if (extractedCodes.isEmpty()) {
                // Try to get "codes" as a direct string value
                val codesString = document.getString("codes")
                if (!codesString.isNullOrBlank()) {
                    Log.d(TAG, "Found codes as String: $codesString")
                    // Split by commas, semicolons, or whitespace
                    val separatedCodes = codesString.split(Regex("[,;\\s]+"))
                    extractedCodes.addAll(separatedCodes.filter { it.isNotBlank() })
                }
                
                // Try to look for a field named exactly "Ammar" (case-insensitive)
                document.data?.keys?.forEach { key ->
                    if (key.equals("ammar", ignoreCase = true)) {
                        Log.d(TAG, "Found field named 'Ammar'")
                        extractedCodes.add("Ammar")
                    }
                }
                
                // Look for any individual string fields that might be codes
                document.data?.forEach { (key, value) ->
                    if (value is String && key.length >= 3 && key != "last_updated") {
                        Log.d(TAG, "Found potential code field: $key = $value")
                        if (value.isNotBlank()) {
                            extractedCodes.add(value)
                        } else {
                            extractedCodes.add(key) // Use the key itself as a code
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting codes: ${e.message}", e)
        }
        
        // Always include default codes for safety
        if (extractedCodes.isEmpty()) {
            extractedCodes.add("Ammar")
            extractedCodes.add("MEDICAI2024")
        }
        
        return extractedCodes
    }
    
    // Verify if an activation code is valid
    private fun verifyActivationCode(context: Context, code: String?) {
        Log.d(TAG, "Verifying saved activation code: '$code'")
        
        if (code == null || code.isBlank()) {
            Log.d(TAG, "No saved activation code found")
            _isActivated.value = false
            return
        }
        
        // Check if code is valid (case-insensitive)
        _isActivated.value = validActivationCodes.any { it.equals(code, ignoreCase = true) }
        
        Log.d(TAG, "Activation code '$code' verification result: ${_isActivated.value}")
        Log.d(TAG, "Available codes: ${validActivationCodes.joinToString()}")
        
        if (!_isActivated.value) {
            // Saved code is no longer valid, show a toast
            Log.d(TAG, "Activation code '$code' is no longer valid")
            
            Toast.makeText(
                context, 
                context.getString(R.string.activation_expired), 
                Toast.LENGTH_LONG
            ).show()
            
            // Clear invalid code
            val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            sharedPreferences.edit().remove("activation_code").apply()
            _activationCode.value = null
        }
    }
    
    // Validate and save activation code
    fun validateActivationCode(context: Context, code: String) {
        _activationError.value = null
        
        // If codes haven't been loaded yet, try fetching them
        if (validActivationCodes.isEmpty() && !_isLoadingActivationCodes.value) {
            viewModelScope.launch {
                fetchActivationCodes()
                // After fetching, retry validation
                validateActivationCodeInternal(context, code)
            }
        } else {
            validateActivationCodeInternal(context, code)
        }
    }
    
    private fun validateActivationCodeInternal(context: Context, code: String) {
        Log.d(TAG, "Validating activation code: '$code'")
        Log.d(TAG, "Available codes: ${validActivationCodes.joinToString()}")
        
        // Trim the code and make it case-insensitive for better user experience
        val trimmedCode = code.trim()
        
        // Check if the code is valid (case-insensitive)
        val isValid = validActivationCodes.any { it.equals(trimmedCode, ignoreCase = true) }
        
        if (isValid) {
            // Valid activation code - find the exact case version from the list
            val exactCode = validActivationCodes.find { it.equals(trimmedCode, ignoreCase = true) } ?: trimmedCode
            Log.d(TAG, "Code '$trimmedCode' is valid! Using exact case: '$exactCode'")
            
            // Save the code in shared preferences
            val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            sharedPreferences.edit().putString("activation_code", exactCode).apply()
            
            _activationCode.value = exactCode
            _isActivated.value = true
            
            Toast.makeText(context, context.getString(R.string.activation_success), Toast.LENGTH_SHORT).show()
        } else {
            // Invalid activation code
            Log.d(TAG, "Code '$trimmedCode' is not valid. Available codes: ${validActivationCodes.joinToString()}")
            _activationError.value = context.getString(R.string.activation_error)
            _isActivated.value = false
            
            Toast.makeText(context, context.getString(R.string.activation_error), Toast.LENGTH_SHORT).show()
        }
    }
    
    // Refresh activation codes from Firestore
    fun refreshActivationCodes(context: Context) {
        viewModelScope.launch {
            fetchActivationCodes()
            
            // Verify current activation code after refresh
            if (_activationCode.value != null) {
                verifyActivationCode(context, _activationCode.value)
            }
            
            Toast.makeText(
                context, 
                context.getString(R.string.activation_codes_refreshed), 
                Toast.LENGTH_SHORT
            ).show()
        }
    }
    
    // Check if premium features are available
    fun isPremiumAvailable(): Boolean {
        return _isActivated.value
    }
    
    // Get Gemini model instance based on API key settings
    fun getGeminiModel(): GenerativeModel {
        val apiKeyToUse = if (_apiKey.value.isNullOrBlank()) {
            // Use default key if no custom key is set
            "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk" // Default key
        } else {
            _apiKey.value!!
        }
        
        return GenerativeModel(
            modelName = "gemini-2.0-flash",
            apiKey = apiKeyToUse,
            generationConfig = generationConfig {
                temperature = 0.7f
                topK = 40
                topP = 0.95f
                maxOutputTokens = 2048
            }
        )
    }
    
    // Save API key
    fun saveApiKey(context: Context, apiKey: String) {
        viewModelScope.launch {
            _isValidatingApiKey.value = true
            _apiKeyValidationError.value = null
            _apiKeyValidationSuccess.value = false
            
            try {
                // Try to create a model with the API key to validate it
                val model = GenerativeModel(
                    modelName = "gemini-2.0-flash",
                    apiKey = apiKey
                )
                
                // Simple validation - try to generate a simple response
                val response = model.generateContent("Hello")
                
                if (response.text != null) {
                    // API key is valid
                    val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
                    sharedPreferences.edit().putString("gemini_api_key", apiKey).apply()
                    
                    _apiKey.value = apiKey
                    _isUsingDefaultKey.value = apiKey.isEmpty()
                    _apiKeyValidationSuccess.value = true
                    
                    Toast.makeText(context, context.getString(R.string.api_key_saved), Toast.LENGTH_SHORT).show()
                } else {
                    // Failed to get text from model
                    _apiKeyValidationError.value = "Invalid API key or response format"
                }
            } catch (e: Exception) {
                // Invalid API key
                _apiKeyValidationError.value = "Error: ${e.message}"
            } finally {
                _isValidatingApiKey.value = false
            }
        }
    }
    
    // Reset to default API key
    fun resetToDefaultApiKey(context: Context) {
        val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        sharedPreferences.edit().remove("gemini_api_key").apply()
        
        _apiKey.value = null
        _isUsingDefaultKey.value = true
        _apiKeyValidationSuccess.value = true
        _apiKeyValidationError.value = null
        
        Toast.makeText(context, context.getString(R.string.using_default_api_key), Toast.LENGTH_SHORT).show()
    }
    
    // Set app language
    fun setAppLanguage(context: Context, languageCode: String) {
        val sharedPreferences = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        sharedPreferences.edit().putString("app_language", languageCode).apply()
        
        _appLanguage.value = languageCode
        
        // Apply the language immediately
        updateLocale(context, languageCode)
        
        // Notify user about language change
        val message = when (languageCode) {
            "en" -> "Language set to English"
            "ar" -> "تم تغيير اللغة إلى العربية"
            else -> "Using system language"
        }
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    // Update the app locale based on saved preference
    fun updateLocale(context: Context, languageCode: String = _appLanguage.value) {
        val locale = when (languageCode) {
            "en" -> Locale("en")
            "ar" -> Locale("ar")
            else -> Locale.getDefault() // Use system default
        }
        
        Locale.setDefault(locale)
        
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        
        context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
    }
    
    // Open Google AI Studio to get API key
    fun openGoogleAIStudio(context: Context) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://makersuite.google.com/app/apikey"))
        context.startActivity(intent)
    }
    
    // Clear validation state
    fun clearValidationState() {
        _apiKeyValidationSuccess.value = false
        _apiKeyValidationError.value = null
    }
    
    // Get diagnostic information for debugging
    fun getActivationDiagnostics(context: Context): String {
        val isLoading = _isLoadingActivationCodes.value
        val codesCount = validActivationCodes.size
        val codesList = validActivationCodes.joinToString()
        val currentCode = _activationCode.value ?: "None"
        val isActive = _isActivated.value
        
        return "Loading: $isLoading\n" +
               "Valid Codes: $codesCount\n" +
               "Codes: $codesList\n" +
               "Current Code: $currentCode\n" +
               "Is Active: $isActive"
    }
} 