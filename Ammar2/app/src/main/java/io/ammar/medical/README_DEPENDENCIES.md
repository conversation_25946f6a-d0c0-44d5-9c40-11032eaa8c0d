# Google Sign-In Integration

To enable the Google Sign-In functionality for automatic Gemini API key retrieval, you need to add the following dependencies to your app's `build.gradle` file:

```gradle
dependencies {
    // Existing dependencies...
    
    // Google Sign-In
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
}
```

## Additional Setup Required

1. **Google Cloud Project Configuration**:
   - Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
   - Enable the Google Sign-In API
   - Configure the OAuth consent screen
   - Create OAuth 2.0 client IDs for your Android app

2. **Add SHA-1 Certificate Fingerprint**:
   - Add your app's SHA-1 fingerprint to the Google Cloud Console
   - This is required for Google Sign-In to work properly

3. **Backend Integration**:
   - Set up a backend service to exchange the Google authentication token for a Gemini API key
   - The backend should handle the secure API key generation and management

## Implementation Notes

The current implementation in `MultiImageAnalyzerScreen.kt` includes a simulated API key generation for demonstration purposes. In a production environment, you would need to:

1. Send the Google authentication token to your backend
2. Have your backend exchange this token for a Gemini API key
3. Return the API key to the app

This ensures secure handling of API keys and proper authentication flow. 