package com.ammar.medical.presentation.ui.screens.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.preferences.AppPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val preferences: AppPreferences
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        loadSettings()
    }

    private fun loadSettings() {
        viewModelScope.launch {
            combine(
                preferences.getTheme(),
                preferences.getLanguage(),
                preferences.getNotificationsEnabled()
            ) { theme, language, notificationsEnabled ->
                SettingsUiState(
                    theme = theme,
                    language = language,
                    notificationsEnabled = notificationsEnabled
                )
            }.collect { state ->
                _uiState.value = state
            }
        }
    }

    fun setTheme(theme: Theme) {
        viewModelScope.launch {
            preferences.setTheme(theme)
        }
    }

    fun setLanguage(language: Language) {
        viewModelScope.launch {
            preferences.setLanguage(language)
        }
    }

    fun setNotificationsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            preferences.setNotificationsEnabled(enabled)
        }
    }
}

data class SettingsUiState(
    val theme: Theme = Theme.SYSTEM,
    val language: Language = Language.ENGLISH,
    val notificationsEnabled: Boolean = true
)

enum class Theme {
    LIGHT, DARK, SYSTEM
}

enum class Language {
    ENGLISH, ARABIC
} 