package com.ammar.medical.data.repository

import com.ammar.medical.data.model.Category
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CategoryRepository @Inject constructor() {
    private val _categories = MutableStateFlow(Category.PREDEFINED)
    val categories: Flow<List<Category>> = _categories.asStateFlow()

    suspend fun getAllCategories(): List<Category> {
        return _categories.value
    }

    suspend fun getCategoryById(id: Long): Category {
        return _categories.value.find { it.id == id } ?: Category.DEFAULT
    }

    suspend fun addCategory(name: String, color: Long, icon: Category.CategoryIcon): Category {
        val newId = _categories.value.maxOf { it.id } + 1
        val newCategory = Category(newId, name, color, icon)
        _categories.value = _categories.value + newCategory
        return newCategory
    }

    suspend fun updateCategory(category: Category) {
        _categories.value = _categories.value.map {
            if (it.id == category.id) category else it
        }
    }

    suspend fun deleteCategory(categoryId: Long) {
        _categories.value = _categories.value.filter { it.id != categoryId }
    }

    suspend fun getDefaultCategory(): Category {
        return Category.DEFAULT
    }
} 