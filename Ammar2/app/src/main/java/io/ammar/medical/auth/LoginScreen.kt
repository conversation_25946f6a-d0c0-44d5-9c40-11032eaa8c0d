package io.ammar.medical.auth

import android.util.Log
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.common.api.ApiException
import android.app.Activity
import io.ammar.medical.R
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import io.ammar.medical.ui.components.FirebaseStatusDialog
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils

@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class, ExperimentalAnimationApi::class)
@Composable
fun AuthScreen(
    viewModel: LoginViewModel = viewModel()
) {
    var isRegistering by remember { mutableStateOf(false) }
    var showPasswordReset by remember { mutableStateOf(false) }
    var showFirebaseStatusDialog by remember { mutableStateOf(false) }
    var consecutiveErrors by remember { mutableStateOf(0) }
    
    // Collect states with remember and derivedStateOf to prevent unnecessary recompositions
    val uiState by viewModel.uiState.collectAsState()
    val registrationState by viewModel.registrationState.collectAsState()
    val isLoggedIn by viewModel.isLoggedIn.collectAsState()
    val currentUser by viewModel.currentUser.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    // Use derivedStateOf for values that depend on other state
    val isFormEnabled by remember {
        derivedStateOf { !isLoading }
    }
    
    val context = LocalContext.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val scope = rememberCoroutineScope()
    
    // Check network connectivity
    val isNetworkAvailable by remember { mutableStateOf(NetworkUtils.isNetworkAvailable(context)) }

    // Handle screen state changes
    val currentScreen = remember(showPasswordReset, isRegistering) {
        when {
            showPasswordReset -> "reset"
            isRegistering -> "register"
            else -> "login"
        }
    }

    // Function to check for connectivity issues
    fun checkForConnectivityIssues() {
        if (consecutiveErrors >= 2 && !isNetworkAvailable) {
            // Show Firebase status dialog after multiple consecutive errors
            showFirebaseStatusDialog = true
        }
    }

    // Combine multiple LaunchedEffect blocks to reduce overhead
    LaunchedEffect(Unit) {
        Log.d("AuthScreen", "Initial launch - refreshing user state")
        viewModel.refreshUserState()
    }

    LaunchedEffect(registrationState, uiState, isLoggedIn, currentUser) {
        when (registrationState) {
            is RegistrationState.Success -> {
                val user = (registrationState as RegistrationState.Success).user
                Log.d("AuthScreen", "Registration success for user: ${user.username}")
                keyboardController?.hide()
                isRegistering = false
                showPasswordReset = false
                consecutiveErrors = 0
            }
            is RegistrationState.Error -> {
                Log.d("AuthScreen", "Registration error: ${(registrationState as RegistrationState.Error).message}")
                consecutiveErrors++
                checkForConnectivityIssues()
            }
            else -> {}
        }

        when (uiState) {
            is LoginUiState.Success -> {
                val user = (uiState as LoginUiState.Success).user
                Log.d("AuthScreen", "Login success for user: ${user.username}")
                keyboardController?.hide()
                isRegistering = false
                showPasswordReset = false
                consecutiveErrors = 0
            }
            is LoginUiState.Error -> {
                Log.d("AuthScreen", "Login error: ${(uiState as LoginUiState.Error).message}")
                consecutiveErrors++
                checkForConnectivityIssues()
            }
            else -> {}
        }

        if (isLoggedIn && currentUser != null) {
            Log.d("AuthScreen", "Authentication successful - transitioning to main screen")
            keyboardController?.hide()
            isRegistering = false
            showPasswordReset = false
            consecutiveErrors = 0
        }
    }
    
    // Monitor loading state
    LaunchedEffect(isLoading) {
        Log.d("AuthScreen", "Loading state changed: $isLoading")
    }

    // Monitor screen transitions
    LaunchedEffect(currentScreen) {
        Log.d("AuthScreen", "Screen changed to: $currentScreen")
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Medical Files Analyzer",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
            ) {
                Column(
                    modifier = Modifier
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    AnimatedContent(
                        targetState = currentScreen,
                        transitionSpec = { 
                            fadeIn(animationSpec = tween(300)) togetherWith 
                            fadeOut(animationSpec = tween(300))
                        },
                        label = "Auth Screen Animation"
                    ) { screen ->
                        Text(
                            text = when(screen) {
                                "reset" -> "Reset Password"
                                "register" -> "Register"
                                else -> "Login"
                            },
                            style = MaterialTheme.typography.titleLarge
                        )
                    }
                    
                    // Show network warning if network is unavailable
                    if (!isNetworkAvailable) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = "Network Warning",
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "No internet connection",
                                color = MaterialTheme.colorScheme.error,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                        
                        TextButton(
                            onClick = { showFirebaseStatusDialog = true }
                        ) {
                            Text("Check Connection Status")
                        }
                    }

                    when {
                        showPasswordReset -> PasswordResetForm(
                            onResetRequest = { email ->
                                viewModel.resetPassword(email)
                                showPasswordReset = false
                            },
                            onCancel = { showPasswordReset = false }
                        )
                        isRegistering -> RegistrationForm(
                            onRegister = { username, email, password, confirmPassword ->
                                keyboardController?.hide()
                                viewModel.register(username, email, password, confirmPassword)
                            },
                            onBackToLogin = { isRegistering = false },
                            state = registrationState
                        )
                        else -> LoginForm(
                            onLogin = { username, password ->
                                keyboardController?.hide()
                                viewModel.login(username, password)
                            },
                            onRegisterClick = { isRegistering = true },
                            onForgotPassword = { showPasswordReset = true },
                            state = uiState,
                            onSetupBiometric = {
                                (context as? FragmentActivity)?.let { activity ->
                                    viewModel.setupBiometric(activity) {
                                        // Handle successful biometric setup
                                    }
                                }
                            },
                            onBypassLogin = {
                                viewModel.bypassLogin()
                            },
                            onError = { message ->
                                scope.launch {
                                    // You can show a snackbar or handle the error in another way
                                    Log.e("LoginScreen", "Error: $message")
                                }
                            },
                            viewModel = viewModel,
                            isLoading = isLoading
                        )
                    }
                }
            }
        }

        // Overlay loading indicator
        if (isLoading) {
            Surface(
                modifier = Modifier.fillMaxSize(),
                color = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(48.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
        
        // Show Firebase status dialog if needed
        if (showFirebaseStatusDialog) {
            FirebaseStatusDialog(
                onDismiss = { showFirebaseStatusDialog = false },
                onRetry = {
                    viewModel.refreshUserState()
                    consecutiveErrors = 0
                }
            )
        }

        // Add a debug button at the bottom of the screen
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.BottomCenter
        ) {
            OutlinedButton(
                onClick = { viewModel.bypassLogin() },
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.tertiary
                )
            ) {
                Text("Debug Login (Bypass Firebase)")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LoginForm(
    onLogin: (String, String) -> Unit,
    onRegisterClick: () -> Unit,
    onForgotPassword: () -> Unit,
    onSetupBiometric: () -> Unit,
    onBypassLogin: () -> Unit,
    onError: (String) -> Unit,
    state: LoginUiState,
    viewModel: LoginViewModel,
    isLoading: Boolean
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var showPassword by remember { mutableStateOf(false) }

    // Google Sign In launcher
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            try {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                val account = task.getResult(ApiException::class.java)
                account.idToken?.let { token ->
                    viewModel.handleGoogleSignIn(token)
                } ?: run {
                    Log.e("LoginScreen", "Google sign in failed: ID token was null")
                    onError("Google sign in failed: ID token was null")
                }
            } catch (e: ApiException) {
                Log.e("LoginScreen", "Google sign in failed", e)
                onError(e.message ?: "Google sign in failed")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Email, contentDescription = null) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            ),
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Lock, contentDescription = null) },
            trailingIcon = {
                IconButton(onClick = { showPassword = !showPassword }) {
                    Icon(
                        imageVector = if (showPassword) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                        contentDescription = if (showPassword) "Hide password" else "Show password"
                    )
                }
            },
            visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Password,
                imeAction = ImeAction.Done
            ),
            modifier = Modifier.fillMaxWidth()
        )

        if (state is LoginUiState.Error) {
            Text(
                text = state.message,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Button(
            onClick = { onLogin(email, password) },
            modifier = Modifier.fillMaxWidth(),
            enabled = email.isNotBlank() && password.isNotBlank() && state !is LoginUiState.Loading
        ) {
            if (state is LoginUiState.Loading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text("Login")
            }
        }

        // Google Sign In Button
        OutlinedButton(
            onClick = { 
                if (!isLoading) {
                    try {
                        googleSignInLauncher.launch(viewModel.getGoogleSignInIntent())
                    } catch (e: Exception) {
                        Log.e("LoginScreen", "Failed to launch Google Sign-In", e)
                        onError("Failed to launch Google Sign-In: ${e.message}")
                    }
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp)
                .padding(vertical = 4.dp),
            enabled = !isLoading && state !is LoginUiState.Loading,
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.primary,
                disabledContentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
            )
        ) {
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_google),
                    contentDescription = "Google Icon",
                    modifier = Modifier.size(24.dp),
                    tint = if (isLoading || state is LoginUiState.Loading) 
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
                    else 
                        MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when {
                        isLoading -> "Signing in..."
                        state is LoginUiState.Loading -> "Processing..."
                        else -> "Sign in with Google"
                    },
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TextButton(onClick = onForgotPassword) {
                Text("Forgot Password?")
            }
            TextButton(onClick = onRegisterClick) {
                Text("Register")
            }
        }

        OutlinedButton(
            onClick = onSetupBiometric,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Enable Biometric Login")
        }

        OutlinedButton(
            onClick = onBypassLogin,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.tertiary
            )
        ) {
            Text("Bypass Login (Testing Only)")
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun RegistrationForm(
    onRegister: (String, String, String, String) -> Unit,
    onBackToLogin: () -> Unit,
    state: RegistrationState
) {
    var username by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var showPassword by remember { mutableStateOf(false) }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        OutlinedTextField(
            value = username,
            onValueChange = { username = it },
            label = { Text("Username") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Person, contentDescription = null) },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Email, contentDescription = null) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            ),
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Lock, contentDescription = null) },
            trailingIcon = {
                IconButton(onClick = { showPassword = !showPassword }) {
                    Icon(
                        imageVector = if (showPassword) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                        contentDescription = if (showPassword) "Hide password" else "Show password"
                    )
                }
            },
            visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = confirmPassword,
            onValueChange = { confirmPassword = it },
            label = { Text("Confirm Password") },
            singleLine = true,
            leadingIcon = { Icon(imageVector = Icons.Filled.Lock, contentDescription = null) },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )

        if (state is RegistrationState.Error) {
            Text(
                text = state.message,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Button(
            onClick = { onRegister(username, email, password, confirmPassword) },
            modifier = Modifier.fillMaxWidth(),
            enabled = username.isNotBlank() && email.isNotBlank() && 
                     password.isNotBlank() && confirmPassword.isNotBlank() &&
                     state !is RegistrationState.Loading
        ) {
            if (state is RegistrationState.Loading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text("Register")
            }
        }

        TextButton(
            onClick = onBackToLogin,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Back to Login")
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PasswordResetForm(
    onResetRequest: (String) -> Unit,
    onCancel: () -> Unit
) {
    var email by remember { mutableStateOf("") }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Enter your email address to reset your password",
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium
        )

        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email") },
            singleLine = true,
            leadingIcon = { 
                Icon(
                    imageVector = Icons.Filled.Email, 
                    contentDescription = null
                ) 
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Done
            ),
            modifier = Modifier.fillMaxWidth()
        )

        Button(
            onClick = { onResetRequest(email) },
            modifier = Modifier.fillMaxWidth(),
            enabled = email.isNotBlank()
        ) {
            Text("Reset Password")
        }

        TextButton(
            onClick = onCancel,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Cancel")
        }
    }
} 