package com.ammar.medical.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.ammar.medical.data.model.Document

@Database(
    entities = [Document::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun documentDao(): DocumentDao
}

@androidx.room.TypeConverters
class Converters {
    @androidx.room.TypeConverter
    fun fromString(value: String): Map<String, String> {
        return value.split(",").associate { pair ->
            val (key, value) = pair.split("=")
            key to value
        }
    }

    @androidx.room.TypeConverter
    fun toString(map: Map<String, String>): String {
        return map.entries.joinToString(",") { "${it.key}=${it.value}" }
    }

    @androidx.room.TypeConverter
    fun fromList(value: String): List<String> {
        return value.split(",")
    }

    @androidx.room.TypeConverter
    fun toList(list: List<String>): String {
        return list.joinToString(",")
    }
} 