package com.ammar.medical.data.repository

import com.ammar.medical.data.model.Document
import com.ammar.medical.data.model.DocumentType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SearchRepository @Inject constructor(
    private val documentRepository: DocumentRepository
) {
    suspend fun searchDocuments(
        query: String,
        documentTypes: Set<DocumentType> = emptySet(),
        tags: Set<String> = emptySet(),
        dateRange: ClosedFloatingPointRange<Float>? = null,
        sortBy: SortOption = SortOption.DATE_DESC
    ): List<Document> {
        val allDocuments = documentRepository.getAllDocuments()
        
        return allDocuments
            .filter { document ->
                // Filter by search query
                val matchesQuery = query.isEmpty() || 
                    document.title.contains(query, ignoreCase = true) ||
                    document.description?.contains(query, ignoreCase = true) == true ||
                    document.tags.any { it.contains(query, ignoreCase = true) }
                
                // Filter by document type
                val matchesType = documentTypes.isEmpty() || document.type in documentTypes
                
                // Filter by tags
                val matchesTags = tags.isEmpty() || document.tags.any { it in tags }
                
                // Filter by date range
                val matchesDate = dateRange == null || 
                    document.date.toFloat() in dateRange
                
                matchesQuery && matchesType && matchesTags && matchesDate
            }
            .sortedWith(
                when (sortBy) {
                    SortOption.DATE_DESC -> compareByDescending { it.date }
                    SortOption.DATE_ASC -> compareBy { it.date }
                    SortOption.TITLE_ASC -> compareBy { it.title }
                    SortOption.TITLE_DESC -> compareByDescending { it.title }
                    SortOption.TYPE_ASC -> compareBy { it.type }
                    SortOption.TYPE_DESC -> compareByDescending { it.type }
                }
            )
    }

    enum class SortOption {
        DATE_DESC,
        DATE_ASC,
        TITLE_ASC,
        TITLE_DESC,
        TYPE_ASC,
        TYPE_DESC
    }
} 