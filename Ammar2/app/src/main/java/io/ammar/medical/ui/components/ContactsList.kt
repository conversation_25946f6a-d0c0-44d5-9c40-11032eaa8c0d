package io.ammar.medical.ui.components

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.ammar.medical.data.Contact
import io.ammar.medical.viewmodel.ContactViewModel
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

/**
 * A simple component to display a list of contacts
 */
@Composable
fun ContactsList(
    onSelectContact: (Contact) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    var contacts by remember { mutableStateOf<List<Contact>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var error by remember { mutableStateOf<String?>(null) }
    
    // Load contacts when component is first displayed
    LaunchedEffect(Unit) {
        loadContacts(
            onSuccess = { contactsList ->
                contacts = contactsList
                isLoading = false
            },
            onError = { errorMessage ->
                error = errorMessage
                isLoading = false
            }
        )
    }
    
    Column(modifier = modifier.fillMaxWidth()) {
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (error != null) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = error ?: "Unknown error",
                    color = MaterialTheme.colorScheme.error,
                    textAlign = TextAlign.Center
                )
            }
        } else if (contacts.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No contacts found. Add contacts from your profile.",
                    textAlign = TextAlign.Center
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 250.dp)
            ) {
                items(contacts) { contact ->
                    ContactListItem(
                        contact = contact,
                        onClick = { onSelectContact(contact) }
                    )
                    Divider()
                }
            }
        }
    }
}

/**
 * A simple contact list item
 */
@Composable
fun ContactListItem(
    contact: Contact,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Contact icon
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = "Contact",
            modifier = Modifier
                .size(40.dp)
                .padding(end = 8.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        // Contact details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = contact.name,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "ID: ${contact.uid}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        // Message button
        IconButton(onClick = onClick) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = "Message",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * Load contacts from Firestore
 */
private suspend fun loadContacts(
    onSuccess: (List<Contact>) -> Unit,
    onError: (String) -> Unit
) {
    val auth = FirebaseAuth.getInstance()
    val currentUser = auth.currentUser
    
    if (currentUser == null) {
        onError("You must be signed in to view contacts")
        return
    }
    
    try {
        val db = FirebaseFirestore.getInstance()
        val snapshot = db.collection("users")
            .document(currentUser.uid)
            .collection("contacts")
            .orderBy("timestamp", Query.Direction.DESCENDING)
            .get()
            .await()
        
        val contactsList = snapshot.documents.mapNotNull { doc ->
            try {
                Contact(
                    uid = doc.getString("uid") ?: return@mapNotNull null,
                    name = doc.getString("name") ?: "Unknown",
                    timestamp = doc.getLong("timestamp") ?: System.currentTimeMillis()
                )
            } catch (e: Exception) {
                null
            }
        }
        
        onSuccess(contactsList)
    } catch (e: Exception) {
        val errorMsg = if (e.message?.contains("permission denied", ignoreCase = true) == true) {
            "Permission denied. Make sure Firestore rules allow reading contacts."
        } else {
            "Error loading contacts: ${e.message}"
        }
        onError(errorMsg)
    }
} 