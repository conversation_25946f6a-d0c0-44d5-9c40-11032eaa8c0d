#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1062880 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=6488, tid=10400
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\GradleHome\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2

Host: Intel(R) Core(TM) i5-5257U CPU @ 2.70GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4830)
Time: Tue Mar  4 22:57:00 2025 Arab Standard Time elapsed time: 21324.913361 seconds (0d 5h 55m 24s)

---------------  T H R E A D  ---------------

Current thread (0x000001717171efb0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=10400, stack(0x0000006282400000,0x0000006282500000) (1024K)]


Current CompileTask:
C2:21324913 101375   !   4       org.tomlj.internal.TomlParser::simpleKey (186 bytes)

Stack: [0x0000006282400000,0x0000006282500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x3b5ffb]
V  [jvm.dll+0x381f97]
V  [jvm.dll+0x38140a]
V  [jvm.dll+0x247c62]
V  [jvm.dll+0x247231]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000171791b9770, length=41, elements={
0x00000171584f4e00, 0x00000171716ffb80, 0x00000171717005d0, 0x0000017171711c20,
0x0000017171712280, 0x0000017171712cd0, 0x000001717171b810, 0x000001717171efb0,
0x0000017171734490, 0x00000171714e3fc0, 0x0000017176018db0, 0x0000017177c29ec0,
0x0000017177cab9d0, 0x0000017177ec1a60, 0x0000017178000010, 0x00000171780006a0,
0x00000171780013c0, 0x00000171780020e0, 0x0000017177ffec60, 0x0000017179f170a0,
0x0000017179f1a520, 0x0000017179f18450, 0x0000017179f19170, 0x0000017179f16380,
0x0000017178b60f20, 0x000001717d985bc0, 0x000001717f942680, 0x000001710ae66d30,
0x000001717f93d130, 0x000001710afa4410, 0x000001710afa85b0, 0x0000017109b751f0,
0x0000017109b75880, 0x0000017109b73120, 0x0000017109b772c0, 0x0000017109b75f10,
0x0000017109b765a0, 0x000001710afa3060, 0x000001717a7e00f0, 0x000001717a7de020,
0x000001717a7e0780
}

Java Threads: ( => current thread )
  0x00000171584f4e00 JavaThread "main"                              [_thread_blocked, id=1108, stack(0x0000006281600000,0x0000006281700000) (1024K)]
  0x00000171716ffb80 JavaThread "Reference Handler"          daemon [_thread_blocked, id=8924, stack(0x0000006281e00000,0x0000006281f00000) (1024K)]
  0x00000171717005d0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=1964, stack(0x0000006281f00000,0x0000006282000000) (1024K)]
  0x0000017171711c20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=10500, stack(0x0000006282000000,0x0000006282100000) (1024K)]
  0x0000017171712280 JavaThread "Attach Listener"            daemon [_thread_blocked, id=8804, stack(0x0000006282100000,0x0000006282200000) (1024K)]
  0x0000017171712cd0 JavaThread "Service Thread"             daemon [_thread_blocked, id=14140, stack(0x0000006282200000,0x0000006282300000) (1024K)]
  0x000001717171b810 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11560, stack(0x0000006282300000,0x0000006282400000) (1024K)]
=>0x000001717171efb0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=10400, stack(0x0000006282400000,0x0000006282500000) (1024K)]
  0x0000017171734490 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12472, stack(0x0000006282500000,0x0000006282600000) (1024K)]
  0x00000171714e3fc0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=12220, stack(0x0000006282600000,0x0000006282700000) (1024K)]
  0x0000017176018db0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=11776, stack(0x0000006282700000,0x0000006282800000) (1024K)]
  0x0000017177c29ec0 JavaThread "Daemon health stats"               [_thread_blocked, id=11564, stack(0x0000006282c00000,0x0000006282d00000) (1024K)]
  0x0000017177cab9d0 JavaThread "Incoming local TCP Connector on port 50125"        [_thread_in_native, id=6944, stack(0x0000006282d00000,0x0000006282e00000) (1024K)]
  0x0000017177ec1a60 JavaThread "Daemon periodic checks"            [_thread_blocked, id=6744, stack(0x0000006282e00000,0x0000006282f00000) (1024K)]
  0x0000017178000010 JavaThread "Cache worker for journal cache (C:\GradleHome\caches\journal-1)"        [_thread_blocked, id=6876, stack(0x0000006283600000,0x0000006283700000) (1024K)]
  0x00000171780006a0 JavaThread "File lock request listener"        [_thread_in_native, id=6864, stack(0x0000006283700000,0x0000006283800000) (1024K)]
  0x00000171780013c0 JavaThread "Cache worker for file hash cache (C:\GradleHome\caches\8.10.2\fileHashes)"        [_thread_blocked, id=13092, stack(0x0000006283800000,0x0000006283900000) (1024K)]
  0x00000171780020e0 JavaThread "File watcher server"        daemon [_thread_in_native, id=9112, stack(0x0000006283b00000,0x0000006283c00000) (1024K)]
  0x0000017177ffec60 JavaThread "File watcher consumer"      daemon [_thread_in_Java, id=11896, stack(0x0000006283c00000,0x0000006283d00000) (1024K)]
  0x0000017179f170a0 JavaThread "jar transforms"                    [_thread_blocked, id=6420, stack(0x0000006283d00000,0x0000006283e00000) (1024K)]
  0x0000017179f1a520 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=12144, stack(0x0000006283e00000,0x0000006283f00000) (1024K)]
  0x0000017179f18450 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=11416, stack(0x0000006283f00000,0x0000006284000000) (1024K)]
  0x0000017179f19170 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=11544, stack(0x0000006284000000,0x0000006284100000) (1024K)]
  0x0000017179f16380 JavaThread "Cache worker for file content cache (C:\GradleHome\caches\8.10.2\fileContent)"        [_thread_blocked, id=11636, stack(0x0000006284200000,0x0000006284300000) (1024K)]
  0x0000017178b60f20 JavaThread "Memory manager"                    [_thread_blocked, id=3256, stack(0x0000006285800000,0x0000006285900000) (1024K)]
  0x000001717d985bc0 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=13104, stack(0x0000006282800000,0x0000006282900000) (1024K)]
  0x000001717f942680 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=11788, stack(0x0000006288100000,0x0000006288200000) (1024K)]
  0x000001710ae66d30 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=11308, stack(0x0000006287500000,0x0000006287600000) (1024K)]
  0x000001717f93d130 JavaThread "RMI Reaper"                        [_thread_blocked, id=8980, stack(0x0000006287900000,0x0000006287a00000) (1024K)]
  0x000001710afa4410 JavaThread "Daemon Thread 13"                  [_thread_blocked, id=14948, stack(0x0000006281300000,0x0000006281400000) (1024K)]
  0x000001710afa85b0 JavaThread "Handler for socket connection from /127.0.0.1:50125 to /127.0.0.1:53934"        [_thread_in_native, id=13240, stack(0x0000006281400000,0x0000006281500000) (1024K)]
  0x0000017109b751f0 JavaThread "Cancel handler"                    [_thread_blocked, id=9568, stack(0x0000006281500000,0x0000006281600000) (1024K)]
  0x0000017109b75880 JavaThread "Daemon worker Thread 13"           [_thread_in_vm, id=13384, stack(0x0000006282f00000,0x0000006283000000) (1024K)]
  0x0000017109b73120 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50125 to /127.0.0.1:53934"        [_thread_blocked, id=16280, stack(0x0000006283000000,0x0000006283100000) (1024K)]
  0x0000017109b772c0 JavaThread "Stdin handler"                     [_thread_blocked, id=15192, stack(0x0000006283100000,0x0000006283200000) (1024K)]
  0x0000017109b75f10 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=2796, stack(0x0000006283200000,0x0000006283300000) (1024K)]
  0x0000017109b765a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\AndroidStudioProjects\Ammar2\.gradle\8.10.2\fileHashes)"        [_thread_blocked, id=9160, stack(0x0000006283300000,0x0000006283400000) (1024K)]
  0x000001710afa3060 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\AndroidStudioProjects\Ammar2\.gradle\buildOutputCleanup)"        [_thread_blocked, id=11724, stack(0x0000006283400000,0x0000006283500000) (1024K)]
  0x000001717a7e00f0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\AndroidStudioProjects\Ammar2\.gradle\8.10.2\checksums)"        [_thread_blocked, id=5960, stack(0x0000006283500000,0x0000006283600000) (1024K)]
  0x000001717a7de020 JavaThread "Cache worker for cache directory md-rule (C:\GradleHome\caches\8.10.2\md-rule)"        [_thread_blocked, id=12388, stack(0x0000006283900000,0x0000006283a00000) (1024K)]
  0x000001717a7e0780 JavaThread "Cache worker for cache directory md-supplier (C:\GradleHome\caches\8.10.2\md-supplier)"        [_thread_blocked, id=12204, stack(0x0000006283a00000,0x0000006283b00000) (1024K)]
Total: 41

Other Threads:
  0x000001717156dff0 VMThread "VM Thread"                           [id=672, stack(0x0000006281d00000,0x0000006281e00000) (1024K)]
  0x00000171714e2ea0 WatcherThread "VM Periodic Task Thread"        [id=3116, stack(0x0000006281c00000,0x0000006281d00000) (1024K)]
  0x00000171585be020 WorkerThread "GC Thread#0"                     [id=13368, stack(0x0000006281700000,0x0000006281800000) (1024K)]
  0x00000171761da3b0 WorkerThread "GC Thread#1"                     [id=12132, stack(0x0000006282900000,0x0000006282a00000) (1024K)]
  0x00000171761da750 WorkerThread "GC Thread#2"                     [id=6532, stack(0x0000006282a00000,0x0000006282b00000) (1024K)]
  0x0000017176a43a60 WorkerThread "GC Thread#3"                     [id=4996, stack(0x0000006282b00000,0x0000006282c00000) (1024K)]
  0x000001715a7553f0 ConcurrentGCThread "G1 Main Marker"            [id=10556, stack(0x0000006281800000,0x0000006281900000) (1024K)]
  0x000001715a755d10 WorkerThread "G1 Conc#0"                       [id=2392, stack(0x0000006281900000,0x0000006281a00000) (1024K)]
  0x000001715a7ad510 ConcurrentGCThread "G1 Refine#0"               [id=13792, stack(0x0000006281a00000,0x0000006281b00000) (1024K)]
  0x00000171773364c0 ConcurrentGCThread "G1 Refine#1"               [id=13500, stack(0x0000006285400000,0x0000006285500000) (1024K)]
  0x000001715a7ade90 ConcurrentGCThread "G1 Service"                [id=11924, stack(0x0000006281b00000,0x0000006281c00000) (1024K)]
Total: 11

Threads with active compile tasks:
C2 CompilerThread0  21324987 101375   !   4       org.tomlj.internal.TomlParser::simpleKey (186 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 8094M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 1536000K, used 640781K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 3 survivors (3072K)
 Metaspace       used 191666K, committed 196544K, reserved 1245184K
  class space    used 23770K, committed 25536K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x00000000806ffcc8, 0x0000000080700000| 99%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x00000000808ffff8, 0x0000000080900000| 99%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x00000000809ff280, 0x0000000080a00000| 99%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080af1048, 0x0000000080b00000| 94%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080cffff0, 0x0000000080d00000| 99%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080dffc30, 0x0000000080e00000| 99%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080effff0, 0x0000000080f00000| 99%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000080fffff0, 0x0000000081000000| 99%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x00000000812fffe8, 0x0000000081300000| 99%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%|HS|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Complete 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%|HS|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Complete 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HC|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%|HC|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Complete 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%|HC|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Complete 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%|HC|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Complete 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%|HS|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Complete 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HC|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%|HC|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Complete 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%|HS|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Complete 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%|HC|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Complete 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x00000000821f7fd0, 0x0000000082200000| 96%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x00000000822ffff0, 0x0000000082300000| 99%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x00000000823fffe8, 0x0000000082400000| 99%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x00000000825fbcb8, 0x0000000082600000| 98%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x00000000826fffc0, 0x0000000082700000| 99%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082afff90, 0x0000000082b00000| 99%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082bffff8, 0x0000000082c00000| 99%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082cee358, 0x0000000082d00000| 93%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082dfffe8, 0x0000000082e00000| 99%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082efff88, 0x0000000082f00000| 99%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082ffffd8, 0x0000000083000000| 99%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x00000000830fffa8, 0x0000000083100000| 99%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HC|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x00000000835fffe8, 0x0000000083600000| 99%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x00000000836ffff8, 0x0000000083700000| 99%| O|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x00000000837fffe8, 0x0000000083800000| 99%| O|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x00000000838fffd8, 0x0000000083900000| 99%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x00000000839fffe0, 0x0000000083a00000| 99%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083affff8, 0x0000000083b00000| 99%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083bfffd8, 0x0000000083c00000| 99%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083cfff80, 0x0000000083d00000| 99%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083df46d8, 0x0000000083e00000| 95%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083effff0, 0x0000000083f00000| 99%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083fffff8, 0x0000000084000000| 99%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HC|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%|HC|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Complete 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HC|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HC|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HC|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x00000000846fffc0, 0x0000000084700000| 99%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x00000000847fffe0, 0x0000000084800000| 99%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x00000000848ffff8, 0x0000000084900000| 99%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x00000000849ffdc8, 0x0000000084a00000| 99%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084afffe0, 0x0000000084b00000| 99%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084bfff48, 0x0000000084c00000| 99%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084cfffe8, 0x0000000084d00000| 99%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%|HS|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Complete 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%|HC|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Complete 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%|HS|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Complete 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%|HS|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Complete 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HC|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Complete 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%|HS|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Complete 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%|HS|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Complete 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%|HC|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Complete 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%|HC|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Complete 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%|HC|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Complete 
|  88|0x0000000085800000, 0x00000000858fffe8, 0x0000000085900000| 99%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%|HS|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Complete 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%|HC|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Complete 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%|HC|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Complete 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%|HC|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Complete 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%|HC|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Complete 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%|HC|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Complete 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%|HS|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Complete 
|  99|0x0000000086300000, 0x00000000863ffff8, 0x0000000086400000| 99%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x00000000865fffe0, 0x0000000086600000| 99%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x00000000866ffff0, 0x0000000086700000| 99%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x00000000867fffc8, 0x0000000086800000| 99%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x00000000868fffc0, 0x0000000086900000| 99%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x00000000869fffe0, 0x0000000086a00000| 99%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086affff8, 0x0000000086b00000| 99%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086bffff8, 0x0000000086c00000| 99%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086cfffb0, 0x0000000086d00000| 99%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HS|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%|HC|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Complete 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%|HC|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Complete 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%|HC|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x00000000872fffe8, 0x0000000087300000| 99%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x00000000873fffc0, 0x0000000087400000| 99%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x00000000875ffff8, 0x0000000087600000| 99%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x00000000877ffff8, 0x0000000087800000| 99%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x00000000878ffff8, 0x0000000087900000| 99%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HS|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HC|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HS|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HC|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HC|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%|HC|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HC|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HS|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HC|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HC|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x00000000886fffc0, 0x0000000088700000| 99%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x00000000887fffe8, 0x0000000088800000| 99%| O|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x00000000889ffff8, 0x0000000088a00000| 99%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088bf7770, 0x0000000088c00000| 96%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088cffff8, 0x0000000088d00000| 99%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088dfffe0, 0x0000000088e00000| 99%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|Cm|TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000088ff21d0, 0x0000000089000000| 94%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x00000000890ffff8, 0x0000000089100000| 99%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x00000000891ffff0, 0x0000000089200000| 99%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x00000000892fffe0, 0x0000000089300000| 99%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|Cm|TAMS 0x0000000089300000| PB 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x00000000894fffe0, 0x0000000089500000| 99%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x00000000895fffe0, 0x0000000089600000| 99%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x00000000896ffff8, 0x0000000089700000| 99%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x00000000899fffd8, 0x0000000089a00000| 99%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089affff8, 0x0000000089b00000| 99%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089effff0, 0x0000000089f00000| 99%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a0ffff0, 0x000000008a100000| 99%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a4ffff8, 0x000000008a500000| 99%| O|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a6f1558, 0x000000008a700000| 94%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|Cm|TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|Cm|TAMS 0x000000008dd00000| PB 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| O|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| O|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| O|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|Cm|TAMS 0x0000000091100000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| O|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| O|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|Cm|TAMS 0x0000000092a00000| PB 0x0000000092a00000| Complete 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|Cm|TAMS 0x0000000092b00000| PB 0x0000000092b00000| Complete 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|Cm|TAMS 0x0000000092c00000| PB 0x0000000092c00000| Complete 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| O|Cm|TAMS 0x0000000092d00000| PB 0x0000000092d00000| Complete 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| O|Cm|TAMS 0x0000000093600000| PB 0x0000000093600000| Complete 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%| O|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| O|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%| O|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| O|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| O|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| O|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%|HS|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%|HC|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| O|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| O|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| O|Cm|TAMS 0x0000000094b00000| PB 0x0000000094b00000| Complete 
| 332|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| O|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094e00000, 0x0000000094e00000|100%| O|Cm|TAMS 0x0000000094d00000| PB 0x0000000094d00000| Complete 
| 334|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| O|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000095000000, 0x0000000095000000|100%| O|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| O|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| O|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| O|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096c00000, 0x0000000096d00000|  0%| F|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097000000, 0x0000000097100000|  0%| F|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097100000, 0x0000000097200000|  0%| F|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097200000, 0x0000000097300000|  0%| F|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| O|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x0000000097500000, 0x0000000097500000|100%| O|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097700000, 0x0000000097800000|  0%| F|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%| O|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%| O|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097c00000, 0x0000000097c00000|100%| O|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097d00000, 0x0000000097d00000|100%| O|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097e00000, 0x0000000097e00000|100%| O|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098100000, 0x0000000098100000|100%| O|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098200000, 0x0000000098200000|100%| O|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098b00000, 0x0000000098b00000|100%| O|Cm|TAMS 0x0000000098a00000| PB 0x0000000098a00000| Complete 
| 395|0x0000000098b00000, 0x0000000098c00000, 0x0000000098c00000|100%| O|Cm|TAMS 0x0000000098b00000| PB 0x0000000098b00000| Complete 
| 396|0x0000000098c00000, 0x0000000098d00000, 0x0000000098d00000|100%| O|Cm|TAMS 0x0000000098c00000| PB 0x0000000098c00000| Complete 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000099000000, 0x0000000099000000|100%| O|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| O|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| O|Cm|TAMS 0x0000000099100000| PB 0x0000000099100000| Complete 
| 402|0x0000000099200000, 0x0000000099200000, 0x0000000099300000|  0%| F|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099300000, 0x0000000099400000|  0%| F|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked 
| 404|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| O|Cm|TAMS 0x0000000099400000| PB 0x0000000099400000| Complete 
| 405|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| O|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| O|Cm|TAMS 0x0000000099600000| PB 0x0000000099600000| Complete 
| 407|0x0000000099700000, 0x00000000997a2e00, 0x0000000099800000| 63%| O|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| O|Cm|TAMS 0x0000000099800000| PB 0x0000000099800000| Complete 
| 409|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| O|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| O|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099b00000, 0x0000000099c00000|  0%| F|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099c00000, 0x0000000099d00000|  0%| F|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099e00000, 0x0000000099e00000|100%| O|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099f00000, 0x0000000099f00000|100%| O|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x000000009a000000, 0x000000009a000000|100%| O|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a100000, 0x000000009a100000|100%| O|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a200000, 0x000000009a200000|100%| O|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a300000, 0x000000009a300000|100%| O|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a700000, 0x000000009a700000|100%| O|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a800000, 0x000000009a800000|100%| O|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a900000, 0x000000009a900000|100%| O|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ac00000, 0x000000009ac00000|100%| O|Cm|TAMS 0x000000009ab00000| PB 0x000000009ab00000| Complete 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ae00000, 0x000000009ae00000|100%| O|Cm|TAMS 0x000000009ad00000| PB 0x000000009ad00000| Complete 
| 430|0x000000009ae00000, 0x000000009af00000, 0x000000009af00000|100%| O|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009b000000, 0x000000009b000000|100%| O|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b200000, 0x000000009b200000|100%| O|Cm|TAMS 0x000000009b100000| PB 0x000000009b100000| Complete 
| 434|0x000000009b200000, 0x000000009b300000, 0x000000009b300000|100%| O|Cm|TAMS 0x000000009b200000| PB 0x000000009b200000| Complete 
| 435|0x000000009b300000, 0x000000009b400000, 0x000000009b400000|100%| O|Cm|TAMS 0x000000009b300000| PB 0x000000009b300000| Complete 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009bb00000, 0x000000009bb00000|100%| O|Cm|TAMS 0x000000009ba00000| PB 0x000000009ba00000| Complete 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bd00000, 0x000000009bd00000|100%| O|Cm|TAMS 0x000000009bc00000| PB 0x000000009bc00000| Complete 
| 445|0x000000009bd00000, 0x000000009be00000, 0x000000009be00000|100%| O|Cm|TAMS 0x000000009bd00000| PB 0x000000009bd00000| Complete 
| 446|0x000000009be00000, 0x000000009bf00000, 0x000000009bf00000|100%| O|Cm|TAMS 0x000000009be00000| PB 0x000000009be00000| Complete 
| 447|0x000000009bf00000, 0x000000009c000000, 0x000000009c000000|100%| O|Cm|TAMS 0x000000009bf00000| PB 0x000000009bf00000| Complete 
| 448|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c500000, 0x000000009c500000|100%| O|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c600000, 0x000000009c600000|100%| O|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c700000, 0x000000009c700000|100%| O|Cm|TAMS 0x000000009c600000| PB 0x000000009c600000| Complete 
| 455|0x000000009c700000, 0x000000009c800000, 0x000000009c800000|100%| O|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c900000, 0x000000009c900000|100%| O|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009ca00000, 0x000000009ca00000|100%| O|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009cb00000, 0x000000009cb00000|100%| O|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cc00000, 0x000000009cc00000|100%| O|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cd00000, 0x000000009cd00000|100%| O|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009ce00000, 0x000000009ce00000|100%| O|Cm|TAMS 0x000000009cd00000| PB 0x000000009cd00000| Complete 
| 462|0x000000009ce00000, 0x000000009cf00000, 0x000000009cf00000|100%| O|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009d000000, 0x000000009d000000|100%| O|Cm|TAMS 0x000000009cf00000| PB 0x000000009cf00000| Complete 
| 464|0x000000009d000000, 0x000000009d100000, 0x000000009d100000|100%| O|Cm|TAMS 0x000000009d000000| PB 0x000000009d000000| Complete 
| 465|0x000000009d100000, 0x000000009d200000, 0x000000009d200000|100%| O|Cm|TAMS 0x000000009d100000| PB 0x000000009d100000| Complete 
| 466|0x000000009d200000, 0x000000009d300000, 0x000000009d300000|100%| O|Cm|TAMS 0x000000009d200000| PB 0x000000009d200000| Complete 
| 467|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked 
| 468|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked 
| 469|0x000000009d500000, 0x000000009d600000, 0x000000009d600000|100%| O|Cm|TAMS 0x000000009d500000| PB 0x000000009d500000| Complete 
| 470|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 471|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 472|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 473|0x000000009d900000, 0x000000009da00000, 0x000000009da00000|100%| O|Cm|TAMS 0x000000009d900000| PB 0x000000009d900000| Complete 
| 474|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 475|0x000000009db00000, 0x000000009db00000, 0x000000009dc00000|  0%| F|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dd00000, 0x000000009dd00000|100%| O|Cm|TAMS 0x000000009dc00000| PB 0x000000009dc00000| Complete 
| 477|0x000000009dd00000, 0x000000009dd00000, 0x000000009de00000|  0%| F|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Untracked 
| 478|0x000000009de00000, 0x000000009de00000, 0x000000009df00000|  0%| F|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 479|0x000000009df00000, 0x000000009e000000, 0x000000009e000000|100%| O|Cm|TAMS 0x000000009df00000| PB 0x000000009df00000| Complete 
| 480|0x000000009e000000, 0x000000009e100000, 0x000000009e100000|100%| O|Cm|TAMS 0x000000009e000000| PB 0x000000009e000000| Complete 
| 481|0x000000009e100000, 0x000000009e200000, 0x000000009e200000|100%| O|Cm|TAMS 0x000000009e100000| PB 0x000000009e100000| Complete 
| 482|0x000000009e200000, 0x000000009e200000, 0x000000009e300000|  0%| F|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Untracked 
| 483|0x000000009e300000, 0x000000009e300000, 0x000000009e400000|  0%| F|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked 
| 484|0x000000009e400000, 0x000000009e400000, 0x000000009e500000|  0%| F|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked 
| 485|0x000000009e500000, 0x000000009e500000, 0x000000009e600000|  0%| F|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
| 486|0x000000009e600000, 0x000000009e600000, 0x000000009e700000|  0%| F|  |TAMS 0x000000009e600000| PB 0x000000009e600000| Untracked 
| 487|0x000000009e700000, 0x000000009e700000, 0x000000009e800000|  0%| F|  |TAMS 0x000000009e700000| PB 0x000000009e700000| Untracked 
| 488|0x000000009e800000, 0x000000009e800000, 0x000000009e900000|  0%| F|  |TAMS 0x000000009e800000| PB 0x000000009e800000| Untracked 
| 489|0x000000009e900000, 0x000000009e900000, 0x000000009ea00000|  0%| F|  |TAMS 0x000000009e900000| PB 0x000000009e900000| Untracked 
| 490|0x000000009ea00000, 0x000000009eb00000, 0x000000009eb00000|100%| O|Cm|TAMS 0x000000009ea00000| PB 0x000000009ea00000| Complete 
| 491|0x000000009eb00000, 0x000000009ec00000, 0x000000009ec00000|100%| O|Cm|TAMS 0x000000009eb00000| PB 0x000000009eb00000| Complete 
| 492|0x000000009ec00000, 0x000000009ed00000, 0x000000009ed00000|100%| O|Cm|TAMS 0x000000009ec00000| PB 0x000000009ec00000| Complete 
| 493|0x000000009ed00000, 0x000000009ee00000, 0x000000009ee00000|100%| O|Cm|TAMS 0x000000009ed00000| PB 0x000000009ed00000| Complete 
| 494|0x000000009ee00000, 0x000000009ef00000, 0x000000009ef00000|100%| O|Cm|TAMS 0x000000009ee00000| PB 0x000000009ee00000| Complete 
| 495|0x000000009ef00000, 0x000000009ef00000, 0x000000009f000000|  0%| F|  |TAMS 0x000000009ef00000| PB 0x000000009ef00000| Untracked 
| 496|0x000000009f000000, 0x000000009f100000, 0x000000009f100000|100%| O|  |TAMS 0x000000009f000000| PB 0x000000009f000000| Untracked 
| 497|0x000000009f100000, 0x000000009f200000, 0x000000009f200000|100%| O|  |TAMS 0x000000009f100000| PB 0x000000009f100000| Untracked 
| 498|0x000000009f200000, 0x000000009f300000, 0x000000009f300000|100%| O|  |TAMS 0x000000009f200000| PB 0x000000009f200000| Untracked 
| 499|0x000000009f300000, 0x000000009f400000, 0x000000009f400000|100%| O|  |TAMS 0x000000009f300000| PB 0x000000009f300000| Untracked 
| 500|0x000000009f400000, 0x000000009f500000, 0x000000009f500000|100%| O|  |TAMS 0x000000009f400000| PB 0x000000009f400000| Untracked 
| 501|0x000000009f500000, 0x000000009f600000, 0x000000009f600000|100%| O|  |TAMS 0x000000009f500000| PB 0x000000009f500000| Untracked 
| 502|0x000000009f600000, 0x000000009f700000, 0x000000009f700000|100%| O|  |TAMS 0x000000009f600000| PB 0x000000009f600000| Untracked 
| 503|0x000000009f700000, 0x000000009f800000, 0x000000009f800000|100%| O|  |TAMS 0x000000009f700000| PB 0x000000009f700000| Untracked 
| 504|0x000000009f800000, 0x000000009f900000, 0x000000009f900000|100%| O|  |TAMS 0x000000009f800000| PB 0x000000009f800000| Untracked 
| 505|0x000000009f900000, 0x000000009fa00000, 0x000000009fa00000|100%| O|  |TAMS 0x000000009f900000| PB 0x000000009f900000| Untracked 
| 506|0x000000009fa00000, 0x000000009fb00000, 0x000000009fb00000|100%| O|  |TAMS 0x000000009fa00000| PB 0x000000009fa00000| Untracked 
| 507|0x000000009fb00000, 0x000000009fc00000, 0x000000009fc00000|100%| O|  |TAMS 0x000000009fb00000| PB 0x000000009fb00000| Untracked 
| 508|0x000000009fc00000, 0x000000009fd00000, 0x000000009fd00000|100%| O|  |TAMS 0x000000009fc00000| PB 0x000000009fc00000| Untracked 
| 509|0x000000009fd00000, 0x000000009fe00000, 0x000000009fe00000|100%| O|  |TAMS 0x000000009fd00000| PB 0x000000009fd00000| Untracked 
| 510|0x000000009fe00000, 0x000000009ff00000, 0x000000009ff00000|100%| O|  |TAMS 0x000000009fe00000| PB 0x000000009fe00000| Untracked 
| 511|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| O|  |TAMS 0x000000009ff00000| PB 0x000000009ff00000| Untracked 
| 512|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Untracked 
| 513|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Untracked 
| 514|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Untracked 
| 515|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| O|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Untracked 
| 516|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| O|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
| 517|0x00000000a0500000, 0x00000000a0500000, 0x00000000a0600000|  0%| F|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
| 518|0x00000000a0600000, 0x00000000a0600000, 0x00000000a0700000|  0%| F|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
| 519|0x00000000a0700000, 0x00000000a0700000, 0x00000000a0800000|  0%| F|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked 
| 520|0x00000000a0800000, 0x00000000a0800000, 0x00000000a0900000|  0%| F|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Untracked 
| 521|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
| 522|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|Cm|TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Complete 
| 523|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|Cm|TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Complete 
| 524|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|Cm|TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Complete 
| 525|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|Cm|TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Complete 
| 526|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|Cm|TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Complete 
| 527|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|Cm|TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Complete 
| 528|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|Cm|TAMS 0x00000000a1000000| PB 0x00000000a1000000| Complete 
| 529|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|Cm|TAMS 0x00000000a1100000| PB 0x00000000a1100000| Complete 
| 530|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|Cm|TAMS 0x00000000a1200000| PB 0x00000000a1200000| Complete 
| 531|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|Cm|TAMS 0x00000000a1300000| PB 0x00000000a1300000| Complete 
| 532|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|Cm|TAMS 0x00000000a1400000| PB 0x00000000a1400000| Complete 
| 533|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|Cm|TAMS 0x00000000a1500000| PB 0x00000000a1500000| Complete 
| 534|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|Cm|TAMS 0x00000000a1600000| PB 0x00000000a1600000| Complete 
| 535|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|Cm|TAMS 0x00000000a1700000| PB 0x00000000a1700000| Complete 
| 536|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|Cm|TAMS 0x00000000a1800000| PB 0x00000000a1800000| Complete 
| 537|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|Cm|TAMS 0x00000000a1900000| PB 0x00000000a1900000| Complete 
| 538|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|Cm|TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Complete 
| 539|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|Cm|TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Complete 
| 540|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|Cm|TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Complete 
| 541|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|Cm|TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Complete 
| 542|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|Cm|TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Complete 
| 543|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|Cm|TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Complete 
| 544|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|Cm|TAMS 0x00000000a2000000| PB 0x00000000a2000000| Complete 
| 545|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|Cm|TAMS 0x00000000a2100000| PB 0x00000000a2100000| Complete 
| 546|0x00000000a2200000, 0x00000000a2200000, 0x00000000a2300000|  0%| F|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
| 547|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|Cm|TAMS 0x00000000a2300000| PB 0x00000000a2300000| Complete 
| 548|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|Cm|TAMS 0x00000000a2400000| PB 0x00000000a2400000| Complete 
| 549|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|Cm|TAMS 0x00000000a2500000| PB 0x00000000a2500000| Complete 
| 550|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|Cm|TAMS 0x00000000a2600000| PB 0x00000000a2600000| Complete 
| 551|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|Cm|TAMS 0x00000000a2700000| PB 0x00000000a2700000| Complete 
| 552|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|Cm|TAMS 0x00000000a2800000| PB 0x00000000a2800000| Complete 
| 553|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|Cm|TAMS 0x00000000a2900000| PB 0x00000000a2900000| Complete 
| 554|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|Cm|TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Complete 
| 555|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|Cm|TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete 
| 556|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|Cm|TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete 
| 557|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|Cm|TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Complete 
| 558|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|Cm|TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Complete 
| 559|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|Cm|TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete 
| 560|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|Cm|TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete 
| 561|0x00000000a3100000, 0x00000000a3100000, 0x00000000a3200000|  0%| F|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Untracked 
| 562|0x00000000a3200000, 0x00000000a3200000, 0x00000000a3300000|  0%| F|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Untracked 
| 563|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|Cm|TAMS 0x00000000a3300000| PB 0x00000000a3300000| Complete 
| 564|0x00000000a3400000, 0x00000000a3400000, 0x00000000a3500000|  0%| F|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Untracked 
| 565|0x00000000a3500000, 0x00000000a3500000, 0x00000000a3600000|  0%| F|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Untracked 
| 566|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|Cm|TAMS 0x00000000a3600000| PB 0x00000000a3600000| Complete 
| 567|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|Cm|TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete 
| 568|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|Cm|TAMS 0x00000000a3800000| PB 0x00000000a3800000| Complete 
| 569|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|Cm|TAMS 0x00000000a3900000| PB 0x00000000a3900000| Complete 
| 570|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|Cm|TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Complete 
| 571|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|Cm|TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Complete 
| 572|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|Cm|TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Complete 
| 573|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|Cm|TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Complete 
| 574|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|Cm|TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Complete 
| 575|0x00000000a3f00000, 0x00000000a3f00000, 0x00000000a4000000|  0%| F|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked 
| 576|0x00000000a4000000, 0x00000000a4000000, 0x00000000a4100000|  0%| F|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked 
| 577|0x00000000a4100000, 0x00000000a4100000, 0x00000000a4200000|  0%| F|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked 
| 578|0x00000000a4200000, 0x00000000a4200000, 0x00000000a4300000|  0%| F|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Untracked 
| 579|0x00000000a4300000, 0x00000000a4300000, 0x00000000a4400000|  0%| F|  |TAMS 0x00000000a4300000| PB 0x00000000a4300000| Untracked 
| 580|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|Cm|TAMS 0x00000000a4400000| PB 0x00000000a4400000| Complete 
| 581|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|Cm|TAMS 0x00000000a4500000| PB 0x00000000a4500000| Complete 
| 582|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|Cm|TAMS 0x00000000a4600000| PB 0x00000000a4600000| Complete 
| 583|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|Cm|TAMS 0x00000000a4700000| PB 0x00000000a4700000| Complete 
| 584|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|Cm|TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete 
| 585|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Untracked 
| 586|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Untracked 
| 587|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked 
| 588|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|Cm|TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Complete 
| 589|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked 
| 590|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|Cm|TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Complete 
| 591|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|Cm|TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Complete 
| 592|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|Cm|TAMS 0x00000000a5000000| PB 0x00000000a5000000| Complete 
| 593|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|Cm|TAMS 0x00000000a5100000| PB 0x00000000a5100000| Complete 
| 594|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|Cm|TAMS 0x00000000a5200000| PB 0x00000000a5200000| Complete 
| 595|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|Cm|TAMS 0x00000000a5300000| PB 0x00000000a5300000| Complete 
| 596|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|Cm|TAMS 0x00000000a5400000| PB 0x00000000a5400000| Complete 
| 597|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|Cm|TAMS 0x00000000a5500000| PB 0x00000000a5500000| Complete 
| 598|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|Cm|TAMS 0x00000000a5600000| PB 0x00000000a5600000| Complete 
| 599|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|Cm|TAMS 0x00000000a5700000| PB 0x00000000a5700000| Complete 
| 600|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|Cm|TAMS 0x00000000a5800000| PB 0x00000000a5800000| Complete 
| 601|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|Cm|TAMS 0x00000000a5900000| PB 0x00000000a5900000| Complete 
| 602|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|Cm|TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Complete 
| 603|0x00000000a5b00000, 0x00000000a5b00000, 0x00000000a5c00000|  0%| F|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Untracked 
| 604|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|Cm|TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Complete 
| 605|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|Cm|TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Complete 
| 606|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|Cm|TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Complete 
| 607|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|Cm|TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Complete 
| 608|0x00000000a6000000, 0x00000000a6000000, 0x00000000a6100000|  0%| F|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked 
| 609|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|Cm|TAMS 0x00000000a6100000| PB 0x00000000a6100000| Complete 
| 610|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|Cm|TAMS 0x00000000a6200000| PB 0x00000000a6200000| Complete 
| 611|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|Cm|TAMS 0x00000000a6300000| PB 0x00000000a6300000| Complete 
| 612|0x00000000a6400000, 0x00000000a6400000, 0x00000000a6500000|  0%| F|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Untracked 
| 613|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|Cm|TAMS 0x00000000a6500000| PB 0x00000000a6500000| Complete 
| 614|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|Cm|TAMS 0x00000000a6600000| PB 0x00000000a6600000| Complete 
| 615|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|Cm|TAMS 0x00000000a6700000| PB 0x00000000a6700000| Complete 
| 616|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|Cm|TAMS 0x00000000a6800000| PB 0x00000000a6800000| Complete 
| 617|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|Cm|TAMS 0x00000000a6900000| PB 0x00000000a6900000| Complete 
| 618|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|Cm|TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Complete 
| 619|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|Cm|TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Complete 
| 620|0x00000000a6c00000, 0x00000000a6c00000, 0x00000000a6d00000|  0%| F|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked 
| 621|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|Cm|TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Complete 
| 622|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|Cm|TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Complete 
| 623|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|Cm|TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Complete 
| 624|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|Cm|TAMS 0x00000000a7000000| PB 0x00000000a7000000| Complete 
| 625|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|Cm|TAMS 0x00000000a7100000| PB 0x00000000a7100000| Complete 
| 626|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|Cm|TAMS 0x00000000a7200000| PB 0x00000000a7200000| Complete 
| 627|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|Cm|TAMS 0x00000000a7300000| PB 0x00000000a7300000| Complete 
| 628|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|Cm|TAMS 0x00000000a7400000| PB 0x00000000a7400000| Complete 
| 629|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|Cm|TAMS 0x00000000a7500000| PB 0x00000000a7500000| Complete 
| 630|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|Cm|TAMS 0x00000000a7600000| PB 0x00000000a7600000| Complete 
| 631|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|Cm|TAMS 0x00000000a7700000| PB 0x00000000a7700000| Complete 
| 632|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|Cm|TAMS 0x00000000a7800000| PB 0x00000000a7800000| Complete 
| 633|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|Cm|TAMS 0x00000000a7900000| PB 0x00000000a7900000| Complete 
| 634|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|Cm|TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Complete 
| 635|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|Cm|TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Complete 
| 636|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|Cm|TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Complete 
| 637|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|Cm|TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Complete 
| 638|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|Cm|TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Complete 
| 639|0x00000000a7f00000, 0x00000000a7f00000, 0x00000000a8000000|  0%| F|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked 
| 640|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|Cm|TAMS 0x00000000a8000000| PB 0x00000000a8000000| Complete 
| 641|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|Cm|TAMS 0x00000000a8100000| PB 0x00000000a8100000| Complete 
| 642|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|Cm|TAMS 0x00000000a8200000| PB 0x00000000a8200000| Complete 
| 643|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%| O|Cm|TAMS 0x00000000a8300000| PB 0x00000000a8300000| Complete 
| 644|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|Cm|TAMS 0x00000000a8400000| PB 0x00000000a8400000| Complete 
| 645|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|Cm|TAMS 0x00000000a8500000| PB 0x00000000a8500000| Complete 
| 646|0x00000000a8600000, 0x00000000a8600000, 0x00000000a8700000|  0%| F|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked 
| 647|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked 
| 648|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked 
| 649|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|Cm|TAMS 0x00000000a8900000| PB 0x00000000a8900000| Complete 
| 650|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|Cm|TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Complete 
| 651|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|Cm|TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Complete 
| 652|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|Cm|TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Complete 
| 653|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked 
| 654|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|Cm|TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Complete 
| 655|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|Cm|TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Complete 
| 656|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked 
| 657|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|Cm|TAMS 0x00000000a9100000| PB 0x00000000a9100000| Complete 
| 658|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| O|Cm|TAMS 0x00000000a9200000| PB 0x00000000a9200000| Complete 
| 659|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|Cm|TAMS 0x00000000a9300000| PB 0x00000000a9300000| Complete 
| 660|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|Cm|TAMS 0x00000000a9400000| PB 0x00000000a9400000| Complete 
| 661|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|Cm|TAMS 0x00000000a9500000| PB 0x00000000a9500000| Complete 
| 662|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|Cm|TAMS 0x00000000a9600000| PB 0x00000000a9600000| Complete 
| 663|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked 
| 664|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|Cm|TAMS 0x00000000a9800000| PB 0x00000000a9800000| Complete 
| 665|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|Cm|TAMS 0x00000000a9900000| PB 0x00000000a9900000| Complete 
| 666|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|Cm|TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Complete 
| 667|0x00000000a9b00000, 0x00000000a9b00000, 0x00000000a9c00000|  0%| F|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked 
| 668|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|Cm|TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Complete 
| 669|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|Cm|TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Complete 
| 670|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|Cm|TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Complete 
| 671|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|Cm|TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Complete 
| 672|0x00000000aa000000, 0x00000000aa000000, 0x00000000aa100000|  0%| F|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked 
| 673|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|Cm|TAMS 0x00000000aa100000| PB 0x00000000aa100000| Complete 
| 674|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|Cm|TAMS 0x00000000aa200000| PB 0x00000000aa200000| Complete 
| 675|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|Cm|TAMS 0x00000000aa300000| PB 0x00000000aa300000| Complete 
| 676|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|Cm|TAMS 0x00000000aa400000| PB 0x00000000aa400000| Complete 
| 677|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|Cm|TAMS 0x00000000aa500000| PB 0x00000000aa500000| Complete 
| 678|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|Cm|TAMS 0x00000000aa600000| PB 0x00000000aa600000| Complete 
| 679|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|Cm|TAMS 0x00000000aa700000| PB 0x00000000aa700000| Complete 
| 680|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|Cm|TAMS 0x00000000aa800000| PB 0x00000000aa800000| Complete 
| 681|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|Cm|TAMS 0x00000000aa900000| PB 0x00000000aa900000| Complete 
| 682|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|Cm|TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Complete 
| 683|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|Cm|TAMS 0x00000000aab00000| PB 0x00000000aab00000| Complete 
| 684|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|Cm|TAMS 0x00000000aac00000| PB 0x00000000aac00000| Complete 
| 685|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|Cm|TAMS 0x00000000aad00000| PB 0x00000000aad00000| Complete 
| 686|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|Cm|TAMS 0x00000000aae00000| PB 0x00000000aae00000| Complete 
| 687|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|Cm|TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Complete 
| 688|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|Cm|TAMS 0x00000000ab000000| PB 0x00000000ab000000| Complete 
| 689|0x00000000ab100000, 0x00000000ab100000, 0x00000000ab200000|  0%| F|  |TAMS 0x00000000ab100000| PB 0x00000000ab100000| Untracked 
| 690|0x00000000ab200000, 0x00000000ab200000, 0x00000000ab300000|  0%| F|  |TAMS 0x00000000ab200000| PB 0x00000000ab200000| Untracked 
| 691|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|Cm|TAMS 0x00000000ab300000| PB 0x00000000ab300000| Complete 
| 692|0x00000000ab400000, 0x00000000ab400000, 0x00000000ab500000|  0%| F|  |TAMS 0x00000000ab400000| PB 0x00000000ab400000| Untracked 
| 693|0x00000000ab500000, 0x00000000ab500000, 0x00000000ab600000|  0%| F|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked 
| 694|0x00000000ab600000, 0x00000000ab600000, 0x00000000ab700000|  0%| F|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked 
| 695|0x00000000ab700000, 0x00000000ab700000, 0x00000000ab800000|  0%| F|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked 
| 696|0x00000000ab800000, 0x00000000ab800000, 0x00000000ab900000|  0%| F|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked 
| 697|0x00000000ab900000, 0x00000000ab900000, 0x00000000aba00000|  0%| F|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked 
| 698|0x00000000aba00000, 0x00000000aba00000, 0x00000000abb00000|  0%| F|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked 
| 699|0x00000000abb00000, 0x00000000abb00000, 0x00000000abc00000|  0%| F|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked 
| 700|0x00000000abc00000, 0x00000000abc00000, 0x00000000abd00000|  0%| F|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked 
| 701|0x00000000abd00000, 0x00000000abd00000, 0x00000000abe00000|  0%| F|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked 
| 702|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%| O|Cm|TAMS 0x00000000abe00000| PB 0x00000000abe00000| Complete 
| 703|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|Cm|TAMS 0x00000000abf00000| PB 0x00000000abf00000| Complete 
| 704|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|Cm|TAMS 0x00000000ac000000| PB 0x00000000ac000000| Complete 
| 705|0x00000000ac100000, 0x00000000ac100000, 0x00000000ac200000|  0%| F|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked 
| 706|0x00000000ac200000, 0x00000000ac200000, 0x00000000ac300000|  0%| F|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked 
| 707|0x00000000ac300000, 0x00000000ac300000, 0x00000000ac400000|  0%| F|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Untracked 
| 708|0x00000000ac400000, 0x00000000ac400000, 0x00000000ac500000|  0%| F|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked 
| 709|0x00000000ac500000, 0x00000000ac500000, 0x00000000ac600000|  0%| F|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked 
| 710|0x00000000ac600000, 0x00000000ac600000, 0x00000000ac700000|  0%| F|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked 
| 711|0x00000000ac700000, 0x00000000ac700000, 0x00000000ac800000|  0%| F|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked 
| 712|0x00000000ac800000, 0x00000000ac800000, 0x00000000ac900000|  0%| F|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked 
| 713|0x00000000ac900000, 0x00000000ac900000, 0x00000000aca00000|  0%| F|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked 
| 714|0x00000000aca00000, 0x00000000aca00000, 0x00000000acb00000|  0%| F|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked 
| 715|0x00000000acb00000, 0x00000000acb00000, 0x00000000acc00000|  0%| F|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked 
| 716|0x00000000acc00000, 0x00000000acc00000, 0x00000000acd00000|  0%| F|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked 
| 717|0x00000000acd00000, 0x00000000acd00000, 0x00000000ace00000|  0%| F|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked 
| 718|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%|HS|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Complete 
| 719|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked 
| 720|0x00000000ad000000, 0x00000000ad000000, 0x00000000ad100000|  0%| F|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked 
| 721|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked 
| 722|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|Cm|TAMS 0x00000000ad200000| PB 0x00000000ad200000| Complete 
| 723|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked 
| 724|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|Cm|TAMS 0x00000000ad400000| PB 0x00000000ad400000| Complete 
| 725|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|Cm|TAMS 0x00000000ad500000| PB 0x00000000ad500000| Complete 
| 726|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|Cm|TAMS 0x00000000ad600000| PB 0x00000000ad600000| Complete 
| 727|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|Cm|TAMS 0x00000000ad700000| PB 0x00000000ad700000| Complete 
| 728|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|Cm|TAMS 0x00000000ad800000| PB 0x00000000ad800000| Complete 
| 729|0x00000000ad900000, 0x00000000ad900000, 0x00000000ada00000|  0%| F|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked 
| 730|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|Cm|TAMS 0x00000000ada00000| PB 0x00000000ada00000| Complete 
| 731|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|Cm|TAMS 0x00000000adb00000| PB 0x00000000adb00000| Complete 
| 732|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%| O|Cm|TAMS 0x00000000adc00000| PB 0x00000000adc00000| Complete 
| 733|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|Cm|TAMS 0x00000000add00000| PB 0x00000000add00000| Complete 
| 734|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|Cm|TAMS 0x00000000ade00000| PB 0x00000000ade00000| Complete 
| 735|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|Cm|TAMS 0x00000000adf00000| PB 0x00000000adf00000| Complete 
| 736|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|Cm|TAMS 0x00000000ae000000| PB 0x00000000ae000000| Complete 
| 737|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|Cm|TAMS 0x00000000ae100000| PB 0x00000000ae100000| Complete 
| 738|0x00000000ae200000, 0x00000000ae200000, 0x00000000ae300000|  0%| F|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked 
| 739|0x00000000ae300000, 0x00000000ae300000, 0x00000000ae400000|  0%| F|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked 
| 740|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|Cm|TAMS 0x00000000ae400000| PB 0x00000000ae400000| Complete 
| 741|0x00000000ae500000, 0x00000000ae500000, 0x00000000ae600000|  0%| F|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked 
| 742|0x00000000ae600000, 0x00000000ae600000, 0x00000000ae700000|  0%| F|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked 
| 743|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|Cm|TAMS 0x00000000ae700000| PB 0x00000000ae700000| Complete 
| 744|0x00000000ae800000, 0x00000000ae800000, 0x00000000ae900000|  0%| F|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Untracked 
| 745|0x00000000ae900000, 0x00000000ae900000, 0x00000000aea00000|  0%| F|  |TAMS 0x00000000ae900000| PB 0x00000000ae900000| Untracked 
| 746|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|Cm|TAMS 0x00000000aea00000| PB 0x00000000aea00000| Complete 
| 747|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|Cm|TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Complete 
| 748|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|Cm|TAMS 0x00000000aec00000| PB 0x00000000aec00000| Complete 
| 749|0x00000000aed00000, 0x00000000aed00000, 0x00000000aee00000|  0%| F|  |TAMS 0x00000000aed00000| PB 0x00000000aed00000| Untracked 
| 750|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|Cm|TAMS 0x00000000aee00000| PB 0x00000000aee00000| Complete 
| 751|0x00000000aef00000, 0x00000000aef00000, 0x00000000af000000|  0%| F|  |TAMS 0x00000000aef00000| PB 0x00000000aef00000| Untracked 
| 752|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|Cm|TAMS 0x00000000af000000| PB 0x00000000af000000| Complete 
| 753|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|Cm|TAMS 0x00000000af100000| PB 0x00000000af100000| Complete 
| 754|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|Cm|TAMS 0x00000000af200000| PB 0x00000000af200000| Complete 
| 755|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|Cm|TAMS 0x00000000af300000| PB 0x00000000af300000| Complete 
| 756|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|Cm|TAMS 0x00000000af400000| PB 0x00000000af400000| Complete 
| 757|0x00000000af500000, 0x00000000af500000, 0x00000000af600000|  0%| F|  |TAMS 0x00000000af500000| PB 0x00000000af500000| Untracked 
| 758|0x00000000af600000, 0x00000000af600000, 0x00000000af700000|  0%| F|  |TAMS 0x00000000af600000| PB 0x00000000af600000| Untracked 
| 759|0x00000000af700000, 0x00000000af700000, 0x00000000af800000|  0%| F|  |TAMS 0x00000000af700000| PB 0x00000000af700000| Untracked 
| 760|0x00000000af800000, 0x00000000af800000, 0x00000000af900000|  0%| F|  |TAMS 0x00000000af800000| PB 0x00000000af800000| Untracked 
| 761|0x00000000af900000, 0x00000000af900000, 0x00000000afa00000|  0%| F|  |TAMS 0x00000000af900000| PB 0x00000000af900000| Untracked 
| 762|0x00000000afa00000, 0x00000000afa00000, 0x00000000afb00000|  0%| F|  |TAMS 0x00000000afa00000| PB 0x00000000afa00000| Untracked 
| 763|0x00000000afb00000, 0x00000000afb00000, 0x00000000afc00000|  0%| F|  |TAMS 0x00000000afb00000| PB 0x00000000afb00000| Untracked 
| 764|0x00000000afc00000, 0x00000000afc00000, 0x00000000afd00000|  0%| F|  |TAMS 0x00000000afc00000| PB 0x00000000afc00000| Untracked 
| 765|0x00000000afd00000, 0x00000000afd00000, 0x00000000afe00000|  0%| F|  |TAMS 0x00000000afd00000| PB 0x00000000afd00000| Untracked 
| 766|0x00000000afe00000, 0x00000000afe00000, 0x00000000aff00000|  0%| F|  |TAMS 0x00000000afe00000| PB 0x00000000afe00000| Untracked 
| 767|0x00000000aff00000, 0x00000000aff00000, 0x00000000b0000000|  0%| F|  |TAMS 0x00000000aff00000| PB 0x00000000aff00000| Untracked 
| 768|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|Cm|TAMS 0x00000000b0000000| PB 0x00000000b0000000| Complete 
| 769|0x00000000b0100000, 0x00000000b0100000, 0x00000000b0200000|  0%| F|  |TAMS 0x00000000b0100000| PB 0x00000000b0100000| Untracked 
| 770|0x00000000b0200000, 0x00000000b0200000, 0x00000000b0300000|  0%| F|  |TAMS 0x00000000b0200000| PB 0x00000000b0200000| Untracked 
| 771|0x00000000b0300000, 0x00000000b0300000, 0x00000000b0400000|  0%| F|  |TAMS 0x00000000b0300000| PB 0x00000000b0300000| Untracked 
| 772|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|Cm|TAMS 0x00000000b0400000| PB 0x00000000b0400000| Complete 
| 773|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|Cm|TAMS 0x00000000b0500000| PB 0x00000000b0500000| Complete 
| 774|0x00000000b0600000, 0x00000000b0600000, 0x00000000b0700000|  0%| F|  |TAMS 0x00000000b0600000| PB 0x00000000b0600000| Untracked 
| 775|0x00000000b0700000, 0x00000000b0700000, 0x00000000b0800000|  0%| F|  |TAMS 0x00000000b0700000| PB 0x00000000b0700000| Untracked 
| 776|0x00000000b0800000, 0x00000000b0800000, 0x00000000b0900000|  0%| F|  |TAMS 0x00000000b0800000| PB 0x00000000b0800000| Untracked 
| 777|0x00000000b0900000, 0x00000000b0900000, 0x00000000b0a00000|  0%| F|  |TAMS 0x00000000b0900000| PB 0x00000000b0900000| Untracked 
| 778|0x00000000b0a00000, 0x00000000b0a00000, 0x00000000b0b00000|  0%| F|  |TAMS 0x00000000b0a00000| PB 0x00000000b0a00000| Untracked 
| 779|0x00000000b0b00000, 0x00000000b0b00000, 0x00000000b0c00000|  0%| F|  |TAMS 0x00000000b0b00000| PB 0x00000000b0b00000| Untracked 
| 780|0x00000000b0c00000, 0x00000000b0c00000, 0x00000000b0d00000|  0%| F|  |TAMS 0x00000000b0c00000| PB 0x00000000b0c00000| Untracked 
| 781|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|Cm|TAMS 0x00000000b0d00000| PB 0x00000000b0d00000| Complete 
| 782|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0e00000| PB 0x00000000b0e00000| Untracked 
| 783|0x00000000b0f00000, 0x00000000b0f00000, 0x00000000b1000000|  0%| F|  |TAMS 0x00000000b0f00000| PB 0x00000000b0f00000| Untracked 
| 784|0x00000000b1000000, 0x00000000b1000000, 0x00000000b1100000|  0%| F|  |TAMS 0x00000000b1000000| PB 0x00000000b1000000| Untracked 
| 785|0x00000000b1100000, 0x00000000b1100000, 0x00000000b1200000|  0%| F|  |TAMS 0x00000000b1100000| PB 0x00000000b1100000| Untracked 
| 786|0x00000000b1200000, 0x00000000b1200000, 0x00000000b1300000|  0%| F|  |TAMS 0x00000000b1200000| PB 0x00000000b1200000| Untracked 
| 787|0x00000000b1300000, 0x00000000b1300000, 0x00000000b1400000|  0%| F|  |TAMS 0x00000000b1300000| PB 0x00000000b1300000| Untracked 
| 788|0x00000000b1400000, 0x00000000b1400000, 0x00000000b1500000|  0%| F|  |TAMS 0x00000000b1400000| PB 0x00000000b1400000| Untracked 
| 789|0x00000000b1500000, 0x00000000b1500000, 0x00000000b1600000|  0%| F|  |TAMS 0x00000000b1500000| PB 0x00000000b1500000| Untracked 
| 790|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1600000| PB 0x00000000b1600000| Untracked 
| 791|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1700000| PB 0x00000000b1700000| Untracked 
| 792|0x00000000b1800000, 0x00000000b1800000, 0x00000000b1900000|  0%| F|  |TAMS 0x00000000b1800000| PB 0x00000000b1800000| Untracked 
| 793|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000| PB 0x00000000b1900000| Untracked 
| 794|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|Cm|TAMS 0x00000000b1a00000| PB 0x00000000b1a00000| Complete 
| 795|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|Cm|TAMS 0x00000000b1b00000| PB 0x00000000b1b00000| Complete 
| 796|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|Cm|TAMS 0x00000000b1c00000| PB 0x00000000b1c00000| Complete 
| 797|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|Cm|TAMS 0x00000000b1d00000| PB 0x00000000b1d00000| Complete 
| 798|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|Cm|TAMS 0x00000000b1e00000| PB 0x00000000b1e00000| Complete 
| 799|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|Cm|TAMS 0x00000000b1f00000| PB 0x00000000b1f00000| Complete 
| 800|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|Cm|TAMS 0x00000000b2000000| PB 0x00000000b2000000| Complete 
| 801|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|Cm|TAMS 0x00000000b2100000| PB 0x00000000b2100000| Complete 
| 802|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|Cm|TAMS 0x00000000b2200000| PB 0x00000000b2200000| Complete 
| 803|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|Cm|TAMS 0x00000000b2300000| PB 0x00000000b2300000| Complete 
| 804|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|Cm|TAMS 0x00000000b2400000| PB 0x00000000b2400000| Complete 
| 805|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|Cm|TAMS 0x00000000b2500000| PB 0x00000000b2500000| Complete 
| 806|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|Cm|TAMS 0x00000000b2600000| PB 0x00000000b2600000| Complete 
| 807|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|Cm|TAMS 0x00000000b2700000| PB 0x00000000b2700000| Complete 
| 808|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|Cm|TAMS 0x00000000b2800000| PB 0x00000000b2800000| Complete 
| 809|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|Cm|TAMS 0x00000000b2900000| PB 0x00000000b2900000| Complete 
| 810|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|Cm|TAMS 0x00000000b2a00000| PB 0x00000000b2a00000| Complete 
| 811|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2b00000| PB 0x00000000b2b00000| Untracked 
| 812|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| O|Cm|TAMS 0x00000000b2c00000| PB 0x00000000b2c00000| Complete 
| 813|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|Cm|TAMS 0x00000000b2d00000| PB 0x00000000b2d00000| Complete 
| 814|0x00000000b2e00000, 0x00000000b2e00000, 0x00000000b2f00000|  0%| F|  |TAMS 0x00000000b2e00000| PB 0x00000000b2e00000| Untracked 
| 815|0x00000000b2f00000, 0x00000000b2f00000, 0x00000000b3000000|  0%| F|  |TAMS 0x00000000b2f00000| PB 0x00000000b2f00000| Untracked 
| 816|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3000000| PB 0x00000000b3000000| Untracked 
| 817|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3100000| PB 0x00000000b3100000| Untracked 
| 818|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000| PB 0x00000000b3200000| Untracked 
| 819|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|  |TAMS 0x00000000b3300000| PB 0x00000000b3300000| Untracked 
| 820|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3400000| PB 0x00000000b3400000| Untracked 
| 821|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3500000| PB 0x00000000b3500000| Untracked 
| 822|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3600000| PB 0x00000000b3600000| Untracked 
| 823|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3700000| PB 0x00000000b3700000| Untracked 
| 824|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3800000| PB 0x00000000b3800000| Untracked 
| 825|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3900000| PB 0x00000000b3900000| Untracked 
| 826|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3a00000| PB 0x00000000b3a00000| Untracked 
| 827|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|  |TAMS 0x00000000b3b00000| PB 0x00000000b3b00000| Untracked 
| 828|0x00000000b3c00000, 0x00000000b3d00000, 0x00000000b3d00000|100%| O|  |TAMS 0x00000000b3c00000| PB 0x00000000b3c00000| Untracked 
| 829|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%| O|  |TAMS 0x00000000b3d00000| PB 0x00000000b3d00000| Untracked 
| 830|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3e00000| PB 0x00000000b3e00000| Untracked 
| 831|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b3f00000| PB 0x00000000b3f00000| Untracked 
| 832|0x00000000b4000000, 0x00000000b4100000, 0x00000000b4100000|100%| O|  |TAMS 0x00000000b4000000| PB 0x00000000b4000000| Untracked 
| 833|0x00000000b4100000, 0x00000000b4200000, 0x00000000b4200000|100%| O|  |TAMS 0x00000000b4100000| PB 0x00000000b4100000| Untracked 
| 834|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4200000| PB 0x00000000b4200000| Untracked 
| 835|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|Cm|TAMS 0x00000000b4300000| PB 0x00000000b4300000| Complete 
| 836|0x00000000b4400000, 0x00000000b4400000, 0x00000000b4500000|  0%| F|  |TAMS 0x00000000b4400000| PB 0x00000000b4400000| Untracked 
| 837|0x00000000b4500000, 0x00000000b4500000, 0x00000000b4600000|  0%| F|  |TAMS 0x00000000b4500000| PB 0x00000000b4500000| Untracked 
| 838|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4600000| PB 0x00000000b4600000| Untracked 
| 839|0x00000000b4700000, 0x00000000b4700000, 0x00000000b4800000|  0%| F|  |TAMS 0x00000000b4700000| PB 0x00000000b4700000| Untracked 
| 840|0x00000000b4800000, 0x00000000b4800000, 0x00000000b4900000|  0%| F|  |TAMS 0x00000000b4800000| PB 0x00000000b4800000| Untracked 
| 841|0x00000000b4900000, 0x00000000b4900000, 0x00000000b4a00000|  0%| F|  |TAMS 0x00000000b4900000| PB 0x00000000b4900000| Untracked 
| 842|0x00000000b4a00000, 0x00000000b4a00000, 0x00000000b4b00000|  0%| F|  |TAMS 0x00000000b4a00000| PB 0x00000000b4a00000| Untracked 
| 843|0x00000000b4b00000, 0x00000000b4b00000, 0x00000000b4c00000|  0%| F|  |TAMS 0x00000000b4b00000| PB 0x00000000b4b00000| Untracked 
| 844|0x00000000b4c00000, 0x00000000b4c00000, 0x00000000b4d00000|  0%| F|  |TAMS 0x00000000b4c00000| PB 0x00000000b4c00000| Untracked 
| 845|0x00000000b4d00000, 0x00000000b4d00000, 0x00000000b4e00000|  0%| F|  |TAMS 0x00000000b4d00000| PB 0x00000000b4d00000| Untracked 
| 846|0x00000000b4e00000, 0x00000000b4e00000, 0x00000000b4f00000|  0%| F|  |TAMS 0x00000000b4e00000| PB 0x00000000b4e00000| Untracked 
| 847|0x00000000b4f00000, 0x00000000b4f00000, 0x00000000b5000000|  0%| F|  |TAMS 0x00000000b4f00000| PB 0x00000000b4f00000| Untracked 
| 848|0x00000000b5000000, 0x00000000b5000000, 0x00000000b5100000|  0%| F|  |TAMS 0x00000000b5000000| PB 0x00000000b5000000| Untracked 
| 849|0x00000000b5100000, 0x00000000b5100000, 0x00000000b5200000|  0%| F|  |TAMS 0x00000000b5100000| PB 0x00000000b5100000| Untracked 
| 850|0x00000000b5200000, 0x00000000b5200000, 0x00000000b5300000|  0%| F|  |TAMS 0x00000000b5200000| PB 0x00000000b5200000| Untracked 
| 851|0x00000000b5300000, 0x00000000b5300000, 0x00000000b5400000|  0%| F|  |TAMS 0x00000000b5300000| PB 0x00000000b5300000| Untracked 
| 852|0x00000000b5400000, 0x00000000b5400000, 0x00000000b5500000|  0%| F|  |TAMS 0x00000000b5400000| PB 0x00000000b5400000| Untracked 
| 853|0x00000000b5500000, 0x00000000b5500000, 0x00000000b5600000|  0%| F|  |TAMS 0x00000000b5500000| PB 0x00000000b5500000| Untracked 
| 854|0x00000000b5600000, 0x00000000b5600000, 0x00000000b5700000|  0%| F|  |TAMS 0x00000000b5600000| PB 0x00000000b5600000| Untracked 
| 855|0x00000000b5700000, 0x00000000b5700000, 0x00000000b5800000|  0%| F|  |TAMS 0x00000000b5700000| PB 0x00000000b5700000| Untracked 
| 856|0x00000000b5800000, 0x00000000b5800000, 0x00000000b5900000|  0%| F|  |TAMS 0x00000000b5800000| PB 0x00000000b5800000| Untracked 
| 857|0x00000000b5900000, 0x00000000b5900000, 0x00000000b5a00000|  0%| F|  |TAMS 0x00000000b5900000| PB 0x00000000b5900000| Untracked 
| 858|0x00000000b5a00000, 0x00000000b5a00000, 0x00000000b5b00000|  0%| F|  |TAMS 0x00000000b5a00000| PB 0x00000000b5a00000| Untracked 
| 859|0x00000000b5b00000, 0x00000000b5b00000, 0x00000000b5c00000|  0%| F|  |TAMS 0x00000000b5b00000| PB 0x00000000b5b00000| Untracked 
| 860|0x00000000b5c00000, 0x00000000b5c00000, 0x00000000b5d00000|  0%| F|  |TAMS 0x00000000b5c00000| PB 0x00000000b5c00000| Untracked 
| 861|0x00000000b5d00000, 0x00000000b5d00000, 0x00000000b5e00000|  0%| F|  |TAMS 0x00000000b5d00000| PB 0x00000000b5d00000| Untracked 
| 862|0x00000000b5e00000, 0x00000000b5e00000, 0x00000000b5f00000|  0%| F|  |TAMS 0x00000000b5e00000| PB 0x00000000b5e00000| Untracked 
| 863|0x00000000b5f00000, 0x00000000b5f00000, 0x00000000b6000000|  0%| F|  |TAMS 0x00000000b5f00000| PB 0x00000000b5f00000| Untracked 
| 864|0x00000000b6000000, 0x00000000b6000000, 0x00000000b6100000|  0%| F|  |TAMS 0x00000000b6000000| PB 0x00000000b6000000| Untracked 
| 865|0x00000000b6100000, 0x00000000b6100000, 0x00000000b6200000|  0%| F|  |TAMS 0x00000000b6100000| PB 0x00000000b6100000| Untracked 
| 866|0x00000000b6200000, 0x00000000b6200000, 0x00000000b6300000|  0%| F|  |TAMS 0x00000000b6200000| PB 0x00000000b6200000| Untracked 
| 867|0x00000000b6300000, 0x00000000b6300000, 0x00000000b6400000|  0%| F|  |TAMS 0x00000000b6300000| PB 0x00000000b6300000| Untracked 
| 868|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6400000| PB 0x00000000b6400000| Untracked 
| 869|0x00000000b6500000, 0x00000000b6500000, 0x00000000b6600000|  0%| F|  |TAMS 0x00000000b6500000| PB 0x00000000b6500000| Untracked 
| 870|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6600000| PB 0x00000000b6600000| Untracked 
| 871|0x00000000b6700000, 0x00000000b6700000, 0x00000000b6800000|  0%| F|  |TAMS 0x00000000b6700000| PB 0x00000000b6700000| Untracked 
| 872|0x00000000b6800000, 0x00000000b6800000, 0x00000000b6900000|  0%| F|  |TAMS 0x00000000b6800000| PB 0x00000000b6800000| Untracked 
| 873|0x00000000b6900000, 0x00000000b6a00000, 0x00000000b6a00000|100%|HS|  |TAMS 0x00000000b6900000| PB 0x00000000b6900000| Complete 
| 874|0x00000000b6a00000, 0x00000000b6b00000, 0x00000000b6b00000|100%|HC|  |TAMS 0x00000000b6a00000| PB 0x00000000b6a00000| Complete 
| 875|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%|HS|  |TAMS 0x00000000b6b00000| PB 0x00000000b6b00000| Complete 
| 876|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%|HC|  |TAMS 0x00000000b6c00000| PB 0x00000000b6c00000| Complete 
| 877|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%|HS|  |TAMS 0x00000000b6d00000| PB 0x00000000b6d00000| Complete 
| 878|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%|HC|  |TAMS 0x00000000b6e00000| PB 0x00000000b6e00000| Complete 
| 879|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%|HC|  |TAMS 0x00000000b6f00000| PB 0x00000000b6f00000| Complete 
| 880|0x00000000b7000000, 0x00000000b7000000, 0x00000000b7100000|  0%| F|  |TAMS 0x00000000b7000000| PB 0x00000000b7000000| Untracked 
| 881|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|Cm|TAMS 0x00000000b7100000| PB 0x00000000b7100000| Complete 
| 882|0x00000000b7200000, 0x00000000b7200000, 0x00000000b7300000|  0%| F|  |TAMS 0x00000000b7200000| PB 0x00000000b7200000| Untracked 
| 883|0x00000000b7300000, 0x00000000b7300000, 0x00000000b7400000|  0%| F|  |TAMS 0x00000000b7300000| PB 0x00000000b7300000| Untracked 
| 884|0x00000000b7400000, 0x00000000b7400000, 0x00000000b7500000|  0%| F|  |TAMS 0x00000000b7400000| PB 0x00000000b7400000| Untracked 
| 885|0x00000000b7500000, 0x00000000b7600000, 0x00000000b7600000|100%| O|Cm|TAMS 0x00000000b7500000| PB 0x00000000b7500000| Complete 
| 886|0x00000000b7600000, 0x00000000b7700000, 0x00000000b7700000|100%| O|  |TAMS 0x00000000b7600000| PB 0x00000000b7600000| Untracked 
| 887|0x00000000b7700000, 0x00000000b7800000, 0x00000000b7800000|100%| O|  |TAMS 0x00000000b7700000| PB 0x00000000b7700000| Untracked 
| 888|0x00000000b7800000, 0x00000000b7900000, 0x00000000b7900000|100%| O|  |TAMS 0x00000000b7800000| PB 0x00000000b7800000| Untracked 
| 889|0x00000000b7900000, 0x00000000b7a00000, 0x00000000b7a00000|100%| O|  |TAMS 0x00000000b7900000| PB 0x00000000b7900000| Untracked 
| 890|0x00000000b7a00000, 0x00000000b7b00000, 0x00000000b7b00000|100%| O|Cm|TAMS 0x00000000b7a00000| PB 0x00000000b7a00000| Complete 
| 891|0x00000000b7b00000, 0x00000000b7c00000, 0x00000000b7c00000|100%| O|Cm|TAMS 0x00000000b7b00000| PB 0x00000000b7b00000| Complete 
| 892|0x00000000b7c00000, 0x00000000b7c00000, 0x00000000b7d00000|  0%| F|  |TAMS 0x00000000b7c00000| PB 0x00000000b7c00000| Untracked 
| 893|0x00000000b7d00000, 0x00000000b7d00000, 0x00000000b7e00000|  0%| F|  |TAMS 0x00000000b7d00000| PB 0x00000000b7d00000| Untracked 
| 894|0x00000000b7e00000, 0x00000000b7f00000, 0x00000000b7f00000|100%| O|Cm|TAMS 0x00000000b7e00000| PB 0x00000000b7e00000| Complete 
| 895|0x00000000b7f00000, 0x00000000b7f00000, 0x00000000b8000000|  0%| F|  |TAMS 0x00000000b7f00000| PB 0x00000000b7f00000| Untracked 
| 896|0x00000000b8000000, 0x00000000b8100000, 0x00000000b8100000|100%| O|Cm|TAMS 0x00000000b8000000| PB 0x00000000b8000000| Complete 
| 897|0x00000000b8100000, 0x00000000b8100000, 0x00000000b8200000|  0%| F|  |TAMS 0x00000000b8100000| PB 0x00000000b8100000| Untracked 
| 898|0x00000000b8200000, 0x00000000b8200000, 0x00000000b8300000|  0%| F|  |TAMS 0x00000000b8200000| PB 0x00000000b8200000| Untracked 
| 899|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000| PB 0x00000000b8300000| Untracked 
| 900|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000| PB 0x00000000b8400000| Untracked 
| 901|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000| PB 0x00000000b8500000| Untracked 
| 902|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000| PB 0x00000000b8600000| Untracked 
| 903|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000| PB 0x00000000b8700000| Untracked 
| 904|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000| PB 0x00000000b8800000| Untracked 
| 905|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000| PB 0x00000000b8900000| Untracked 
| 906|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000| PB 0x00000000b8a00000| Untracked 
| 907|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000| PB 0x00000000b8b00000| Untracked 
| 908|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000| PB 0x00000000b8c00000| Untracked 
| 909|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000| PB 0x00000000b8d00000| Untracked 
| 910|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000| PB 0x00000000b8e00000| Untracked 
| 911|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000| PB 0x00000000b8f00000| Untracked 
| 912|0x00000000b9000000, 0x00000000b9100000, 0x00000000b9100000|100%| O|Cm|TAMS 0x00000000b9000000| PB 0x00000000b9000000| Complete 
| 913|0x00000000b9100000, 0x00000000b9200000, 0x00000000b9200000|100%| O|  |TAMS 0x00000000b9100000| PB 0x00000000b9100000| Untracked 
| 914|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000| PB 0x00000000b9200000| Untracked 
| 915|0x00000000b9300000, 0x00000000b9400000, 0x00000000b9400000|100%| O|Cm|TAMS 0x00000000b9300000| PB 0x00000000b9300000| Complete 
| 916|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000| PB 0x00000000b9400000| Untracked 
| 917|0x00000000b9500000, 0x00000000b9500000, 0x00000000b9600000|  0%| F|  |TAMS 0x00000000b9500000| PB 0x00000000b9500000| Untracked 
| 918|0x00000000b9600000, 0x00000000b9700000, 0x00000000b9700000|100%| O|  |TAMS 0x00000000b9600000| PB 0x00000000b9600000| Untracked 
| 919|0x00000000b9700000, 0x00000000b9700000, 0x00000000b9800000|  0%| F|  |TAMS 0x00000000b9700000| PB 0x00000000b9700000| Untracked 
| 920|0x00000000b9800000, 0x00000000b9900000, 0x00000000b9900000|100%| O|Cm|TAMS 0x00000000b9800000| PB 0x00000000b9800000| Complete 
| 921|0x00000000b9900000, 0x00000000b9900000, 0x00000000b9a00000|  0%| F|  |TAMS 0x00000000b9900000| PB 0x00000000b9900000| Untracked 
| 922|0x00000000b9a00000, 0x00000000b9b00000, 0x00000000b9b00000|100%| O|  |TAMS 0x00000000b9a00000| PB 0x00000000b9a00000| Untracked 
| 923|0x00000000b9b00000, 0x00000000b9b00000, 0x00000000b9c00000|  0%| F|  |TAMS 0x00000000b9b00000| PB 0x00000000b9b00000| Untracked 
| 924|0x00000000b9c00000, 0x00000000b9d00000, 0x00000000b9d00000|100%| O|Cm|TAMS 0x00000000b9c00000| PB 0x00000000b9c00000| Complete 
| 925|0x00000000b9d00000, 0x00000000b9e00000, 0x00000000b9e00000|100%| O|Cm|TAMS 0x00000000b9d00000| PB 0x00000000b9d00000| Complete 
| 926|0x00000000b9e00000, 0x00000000b9e00000, 0x00000000b9f00000|  0%| F|  |TAMS 0x00000000b9e00000| PB 0x00000000b9e00000| Untracked 
| 927|0x00000000b9f00000, 0x00000000b9f00000, 0x00000000ba000000|  0%| F|  |TAMS 0x00000000b9f00000| PB 0x00000000b9f00000| Untracked 
| 928|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000| PB 0x00000000ba000000| Untracked 
| 929|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000| PB 0x00000000ba100000| Untracked 
| 930|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000| PB 0x00000000ba200000| Untracked 
| 931|0x00000000ba300000, 0x00000000ba300000, 0x00000000ba400000|  0%| F|  |TAMS 0x00000000ba300000| PB 0x00000000ba300000| Untracked 
| 932|0x00000000ba400000, 0x00000000ba400000, 0x00000000ba500000|  0%| F|  |TAMS 0x00000000ba400000| PB 0x00000000ba400000| Untracked 
| 933|0x00000000ba500000, 0x00000000ba500000, 0x00000000ba600000|  0%| F|  |TAMS 0x00000000ba500000| PB 0x00000000ba500000| Untracked 
| 934|0x00000000ba600000, 0x00000000ba600000, 0x00000000ba700000|  0%| F|  |TAMS 0x00000000ba600000| PB 0x00000000ba600000| Untracked 
| 935|0x00000000ba700000, 0x00000000ba700000, 0x00000000ba800000|  0%| F|  |TAMS 0x00000000ba700000| PB 0x00000000ba700000| Untracked 
| 936|0x00000000ba800000, 0x00000000ba800000, 0x00000000ba900000|  0%| F|  |TAMS 0x00000000ba800000| PB 0x00000000ba800000| Untracked 
| 937|0x00000000ba900000, 0x00000000ba900000, 0x00000000baa00000|  0%| F|  |TAMS 0x00000000ba900000| PB 0x00000000ba900000| Untracked 
| 938|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000| PB 0x00000000baa00000| Untracked 
| 939|0x00000000bab00000, 0x00000000bab00000, 0x00000000bac00000|  0%| F|  |TAMS 0x00000000bab00000| PB 0x00000000bab00000| Untracked 
| 940|0x00000000bac00000, 0x00000000bac00000, 0x00000000bad00000|  0%| F|  |TAMS 0x00000000bac00000| PB 0x00000000bac00000| Untracked 
| 941|0x00000000bad00000, 0x00000000bad00000, 0x00000000bae00000|  0%| F|  |TAMS 0x00000000bad00000| PB 0x00000000bad00000| Untracked 
| 942|0x00000000bae00000, 0x00000000bae00000, 0x00000000baf00000|  0%| F|  |TAMS 0x00000000bae00000| PB 0x00000000bae00000| Untracked 
| 943|0x00000000baf00000, 0x00000000baf00000, 0x00000000bb000000|  0%| F|  |TAMS 0x00000000baf00000| PB 0x00000000baf00000| Untracked 
| 944|0x00000000bb000000, 0x00000000bb000000, 0x00000000bb100000|  0%| F|  |TAMS 0x00000000bb000000| PB 0x00000000bb000000| Untracked 
| 945|0x00000000bb100000, 0x00000000bb100000, 0x00000000bb200000|  0%| F|  |TAMS 0x00000000bb100000| PB 0x00000000bb100000| Untracked 
| 946|0x00000000bb200000, 0x00000000bb200000, 0x00000000bb300000|  0%| F|  |TAMS 0x00000000bb200000| PB 0x00000000bb200000| Untracked 
| 947|0x00000000bb300000, 0x00000000bb300000, 0x00000000bb400000|  0%| F|  |TAMS 0x00000000bb300000| PB 0x00000000bb300000| Untracked 
| 948|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000| PB 0x00000000bb400000| Untracked 
| 949|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000| PB 0x00000000bb500000| Untracked 
| 950|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000| PB 0x00000000bb600000| Untracked 
| 951|0x00000000bb700000, 0x00000000bb700000, 0x00000000bb800000|  0%| F|  |TAMS 0x00000000bb700000| PB 0x00000000bb700000| Untracked 
| 952|0x00000000bb800000, 0x00000000bb800000, 0x00000000bb900000|  0%| F|  |TAMS 0x00000000bb800000| PB 0x00000000bb800000| Untracked 
| 953|0x00000000bb900000, 0x00000000bb900000, 0x00000000bba00000|  0%| F|  |TAMS 0x00000000bb900000| PB 0x00000000bb900000| Untracked 
| 954|0x00000000bba00000, 0x00000000bba00000, 0x00000000bbb00000|  0%| F|  |TAMS 0x00000000bba00000| PB 0x00000000bba00000| Untracked 
| 955|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000| PB 0x00000000bbb00000| Untracked 
| 956|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000| PB 0x00000000bbc00000| Untracked 
| 957|0x00000000bbd00000, 0x00000000bbd00000, 0x00000000bbe00000|  0%| F|  |TAMS 0x00000000bbd00000| PB 0x00000000bbd00000| Untracked 
| 958|0x00000000bbe00000, 0x00000000bbe00000, 0x00000000bbf00000|  0%| F|  |TAMS 0x00000000bbe00000| PB 0x00000000bbe00000| Untracked 
| 959|0x00000000bbf00000, 0x00000000bbf00000, 0x00000000bc000000|  0%| F|  |TAMS 0x00000000bbf00000| PB 0x00000000bbf00000| Untracked 
| 960|0x00000000bc000000, 0x00000000bc000000, 0x00000000bc100000|  0%| F|  |TAMS 0x00000000bc000000| PB 0x00000000bc000000| Untracked 
| 961|0x00000000bc100000, 0x00000000bc100000, 0x00000000bc200000|  0%| F|  |TAMS 0x00000000bc100000| PB 0x00000000bc100000| Untracked 
| 962|0x00000000bc200000, 0x00000000bc200000, 0x00000000bc300000|  0%| F|  |TAMS 0x00000000bc200000| PB 0x00000000bc200000| Untracked 
| 963|0x00000000bc300000, 0x00000000bc300000, 0x00000000bc400000|  0%| F|  |TAMS 0x00000000bc300000| PB 0x00000000bc300000| Untracked 
| 964|0x00000000bc400000, 0x00000000bc400000, 0x00000000bc500000|  0%| F|  |TAMS 0x00000000bc400000| PB 0x00000000bc400000| Untracked 
| 965|0x00000000bc500000, 0x00000000bc500000, 0x00000000bc600000|  0%| F|  |TAMS 0x00000000bc500000| PB 0x00000000bc500000| Untracked 
| 966|0x00000000bc600000, 0x00000000bc600000, 0x00000000bc700000|  0%| F|  |TAMS 0x00000000bc600000| PB 0x00000000bc600000| Untracked 
| 967|0x00000000bc700000, 0x00000000bc700000, 0x00000000bc800000|  0%| F|  |TAMS 0x00000000bc700000| PB 0x00000000bc700000| Untracked 
| 968|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000| PB 0x00000000bc800000| Untracked 
| 969|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000| PB 0x00000000bc900000| Untracked 
| 970|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000| PB 0x00000000bca00000| Untracked 
| 971|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000| PB 0x00000000bcb00000| Untracked 
| 972|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000| PB 0x00000000bcc00000| Untracked 
| 973|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000| PB 0x00000000bcd00000| Untracked 
| 974|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000| PB 0x00000000bce00000| Untracked 
| 975|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000| PB 0x00000000bcf00000| Untracked 
| 976|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000| PB 0x00000000bd000000| Untracked 
| 977|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000| PB 0x00000000bd100000| Untracked 
| 978|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000| PB 0x00000000bd200000| Untracked 
| 979|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000| PB 0x00000000bd300000| Untracked 
| 980|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000| PB 0x00000000bd400000| Untracked 
| 981|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000| PB 0x00000000bd500000| Untracked 
| 982|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000| PB 0x00000000bd600000| Untracked 
| 983|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000| PB 0x00000000bd700000| Untracked 
| 984|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000| PB 0x00000000bd800000| Untracked 
| 985|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000| PB 0x00000000bd900000| Untracked 
| 986|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000| PB 0x00000000bda00000| Untracked 
| 987|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000| PB 0x00000000bdb00000| Untracked 
| 988|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000| PB 0x00000000bdc00000| Untracked 
| 989|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000| PB 0x00000000bdd00000| Untracked 
| 990|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000| PB 0x00000000bde00000| Untracked 
| 991|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000| PB 0x00000000bdf00000| Untracked 
| 992|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000| PB 0x00000000be000000| Untracked 
| 993|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000| PB 0x00000000be100000| Untracked 
| 994|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000| PB 0x00000000be200000| Untracked 
| 995|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000| PB 0x00000000be300000| Untracked 
| 996|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000| PB 0x00000000be400000| Untracked 
| 997|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000| PB 0x00000000be500000| Untracked 
| 998|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000| PB 0x00000000be600000| Untracked 
| 999|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000| PB 0x00000000be700000| Untracked 
|1000|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000| PB 0x00000000be800000| Untracked 
|1001|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000| PB 0x00000000be900000| Untracked 
|1002|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000| PB 0x00000000bea00000| Untracked 
|1003|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000| PB 0x00000000beb00000| Untracked 
|1004|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000| PB 0x00000000bec00000| Untracked 
|1005|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000| PB 0x00000000bed00000| Untracked 
|1006|0x00000000bee00000, 0x00000000bef00000, 0x00000000bef00000|100%| O|  |TAMS 0x00000000bee00000| PB 0x00000000bee00000| Untracked 
|1007|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000| PB 0x00000000bef00000| Untracked 
|1008|0x00000000bf000000, 0x00000000bf100000, 0x00000000bf100000|100%| O|  |TAMS 0x00000000bf000000| PB 0x00000000bf000000| Untracked 
|1009|0x00000000bf100000, 0x00000000bf200000, 0x00000000bf200000|100%| O|Cm|TAMS 0x00000000bf100000| PB 0x00000000bf100000| Complete 
|1010|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000| PB 0x00000000bf200000| Untracked 
|1011|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000| PB 0x00000000bf300000| Untracked 
|1012|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000| PB 0x00000000bf400000| Untracked 
|1013|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000| PB 0x00000000bf500000| Untracked 
|1014|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000| PB 0x00000000bf600000| Untracked 
|1015|0x00000000bf700000, 0x00000000bf700000, 0x00000000bf800000|  0%| F|  |TAMS 0x00000000bf700000| PB 0x00000000bf700000| Untracked 
|1016|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000| PB 0x00000000bf800000| Untracked 
|1017|0x00000000bf900000, 0x00000000bf900000, 0x00000000bfa00000|  0%| F|  |TAMS 0x00000000bf900000| PB 0x00000000bf900000| Untracked 
|1018|0x00000000bfa00000, 0x00000000bfa00000, 0x00000000bfb00000|  0%| F|  |TAMS 0x00000000bfa00000| PB 0x00000000bfa00000| Untracked 
|1019|0x00000000bfb00000, 0x00000000bfb00000, 0x00000000bfc00000|  0%| F|  |TAMS 0x00000000bfb00000| PB 0x00000000bfb00000| Untracked 
|1020|0x00000000bfc00000, 0x00000000bfc00000, 0x00000000bfd00000|  0%| F|  |TAMS 0x00000000bfc00000| PB 0x00000000bfc00000| Untracked 
|1021|0x00000000bfd00000, 0x00000000bfd00000, 0x00000000bfe00000|  0%| F|  |TAMS 0x00000000bfd00000| PB 0x00000000bfd00000| Untracked 
|1022|0x00000000bfe00000, 0x00000000bfe00000, 0x00000000bff00000|  0%| F|  |TAMS 0x00000000bfe00000| PB 0x00000000bfe00000| Untracked 
|1023|0x00000000bff00000, 0x00000000bff00000, 0x00000000c0000000|  0%| F|  |TAMS 0x00000000bff00000| PB 0x00000000bff00000| Untracked 
|1024|0x00000000c0000000, 0x00000000c0000000, 0x00000000c0100000|  0%| F|  |TAMS 0x00000000c0000000| PB 0x00000000c0000000| Untracked 
|1025|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000| PB 0x00000000c0100000| Untracked 
|1026|0x00000000c0200000, 0x00000000c0200000, 0x00000000c0300000|  0%| F|  |TAMS 0x00000000c0200000| PB 0x00000000c0200000| Untracked 
|1027|0x00000000c0300000, 0x00000000c0300000, 0x00000000c0400000|  0%| F|  |TAMS 0x00000000c0300000| PB 0x00000000c0300000| Untracked 
|1028|0x00000000c0400000, 0x00000000c0400000, 0x00000000c0500000|  0%| F|  |TAMS 0x00000000c0400000| PB 0x00000000c0400000| Untracked 
|1029|0x00000000c0500000, 0x00000000c0500000, 0x00000000c0600000|  0%| F|  |TAMS 0x00000000c0500000| PB 0x00000000c0500000| Untracked 
|1030|0x00000000c0600000, 0x00000000c0600000, 0x00000000c0700000|  0%| F|  |TAMS 0x00000000c0600000| PB 0x00000000c0600000| Untracked 
|1031|0x00000000c0700000, 0x00000000c0700000, 0x00000000c0800000|  0%| F|  |TAMS 0x00000000c0700000| PB 0x00000000c0700000| Untracked 
|1032|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
|1033|0x00000000c0900000, 0x00000000c0900000, 0x00000000c0a00000|  0%| F|  |TAMS 0x00000000c0900000| PB 0x00000000c0900000| Untracked 
|1034|0x00000000c0a00000, 0x00000000c0a00000, 0x00000000c0b00000|  0%| F|  |TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Untracked 
|1035|0x00000000c0b00000, 0x00000000c0b00000, 0x00000000c0c00000|  0%| F|  |TAMS 0x00000000c0b00000| PB 0x00000000c0b00000| Untracked 
|1036|0x00000000c0c00000, 0x00000000c0c00000, 0x00000000c0d00000|  0%| F|  |TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Untracked 
|1037|0x00000000c0d00000, 0x00000000c0d00000, 0x00000000c0e00000|  0%| F|  |TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Untracked 
|1038|0x00000000c0e00000, 0x00000000c0e00000, 0x00000000c0f00000|  0%| F|  |TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Untracked 
|1039|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| O|Cm|TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Complete 
|1040|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| O|  |TAMS 0x00000000c1000000| PB 0x00000000c1000000| Untracked 
|1041|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| O|Cm|TAMS 0x00000000c1100000| PB 0x00000000c1100000| Complete 
|1042|0x00000000c1200000, 0x00000000c1200000, 0x00000000c1300000|  0%| F|  |TAMS 0x00000000c1200000| PB 0x00000000c1200000| Untracked 
|1043|0x00000000c1300000, 0x00000000c1300000, 0x00000000c1400000|  0%| F|  |TAMS 0x00000000c1300000| PB 0x00000000c1300000| Untracked 
|1044|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000| PB 0x00000000c1400000| Untracked 
|1045|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000| PB 0x00000000c1500000| Untracked 
|1046|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000| PB 0x00000000c1600000| Untracked 
|1047|0x00000000c1700000, 0x00000000c1700000, 0x00000000c1800000|  0%| F|  |TAMS 0x00000000c1700000| PB 0x00000000c1700000| Untracked 
|1048|0x00000000c1800000, 0x00000000c1800000, 0x00000000c1900000|  0%| F|  |TAMS 0x00000000c1800000| PB 0x00000000c1800000| Untracked 
|1049|0x00000000c1900000, 0x00000000c1900000, 0x00000000c1a00000|  0%| F|  |TAMS 0x00000000c1900000| PB 0x00000000c1900000| Untracked 
|1050|0x00000000c1a00000, 0x00000000c1a00000, 0x00000000c1b00000|  0%| F|  |TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Untracked 
|1051|0x00000000c1b00000, 0x00000000c1b00000, 0x00000000c1c00000|  0%| F|  |TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Untracked 
|1052|0x00000000c1c00000, 0x00000000c1c00000, 0x00000000c1d00000|  0%| F|  |TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Untracked 
|1053|0x00000000c1d00000, 0x00000000c1d00000, 0x00000000c1e00000|  0%| F|  |TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Untracked 
|1054|0x00000000c1e00000, 0x00000000c1e00000, 0x00000000c1f00000|  0%| F|  |TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Untracked 
|1055|0x00000000c1f00000, 0x00000000c1f00000, 0x00000000c2000000|  0%| F|  |TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Untracked 
|1056|0x00000000c2000000, 0x00000000c2000000, 0x00000000c2100000|  0%| F|  |TAMS 0x00000000c2000000| PB 0x00000000c2000000| Untracked 
|1057|0x00000000c2100000, 0x00000000c2100000, 0x00000000c2200000|  0%| F|  |TAMS 0x00000000c2100000| PB 0x00000000c2100000| Untracked 
|1058|0x00000000c2200000, 0x00000000c2200000, 0x00000000c2300000|  0%| F|  |TAMS 0x00000000c2200000| PB 0x00000000c2200000| Untracked 
|1059|0x00000000c2300000, 0x00000000c2300000, 0x00000000c2400000|  0%| F|  |TAMS 0x00000000c2300000| PB 0x00000000c2300000| Untracked 
|1060|0x00000000c2400000, 0x00000000c2400000, 0x00000000c2500000|  0%| F|  |TAMS 0x00000000c2400000| PB 0x00000000c2400000| Untracked 
|1061|0x00000000c2500000, 0x00000000c2500000, 0x00000000c2600000|  0%| F|  |TAMS 0x00000000c2500000| PB 0x00000000c2500000| Untracked 
|1062|0x00000000c2600000, 0x00000000c2600000, 0x00000000c2700000|  0%| F|  |TAMS 0x00000000c2600000| PB 0x00000000c2600000| Untracked 
|1063|0x00000000c2700000, 0x00000000c2700000, 0x00000000c2800000|  0%| F|  |TAMS 0x00000000c2700000| PB 0x00000000c2700000| Untracked 
|1064|0x00000000c2800000, 0x00000000c2800000, 0x00000000c2900000|  0%| F|  |TAMS 0x00000000c2800000| PB 0x00000000c2800000| Untracked 
|1065|0x00000000c2900000, 0x00000000c2900000, 0x00000000c2a00000|  0%| F|  |TAMS 0x00000000c2900000| PB 0x00000000c2900000| Untracked 
|1066|0x00000000c2a00000, 0x00000000c2a00000, 0x00000000c2b00000|  0%| F|  |TAMS 0x00000000c2a00000| PB 0x00000000c2a00000| Untracked 
|1067|0x00000000c2b00000, 0x00000000c2b00000, 0x00000000c2c00000|  0%| F|  |TAMS 0x00000000c2b00000| PB 0x00000000c2b00000| Untracked 
|1068|0x00000000c2c00000, 0x00000000c2c00000, 0x00000000c2d00000|  0%| F|  |TAMS 0x00000000c2c00000| PB 0x00000000c2c00000| Untracked 
|1069|0x00000000c2d00000, 0x00000000c2d00000, 0x00000000c2e00000|  0%| F|  |TAMS 0x00000000c2d00000| PB 0x00000000c2d00000| Untracked 
|1070|0x00000000c2e00000, 0x00000000c2e00000, 0x00000000c2f00000|  0%| F|  |TAMS 0x00000000c2e00000| PB 0x00000000c2e00000| Untracked 
|1071|0x00000000c2f00000, 0x00000000c2f00000, 0x00000000c3000000|  0%| F|  |TAMS 0x00000000c2f00000| PB 0x00000000c2f00000| Untracked 
|1072|0x00000000c3000000, 0x00000000c3000000, 0x00000000c3100000|  0%| F|  |TAMS 0x00000000c3000000| PB 0x00000000c3000000| Untracked 
|1073|0x00000000c3100000, 0x00000000c3100000, 0x00000000c3200000|  0%| F|  |TAMS 0x00000000c3100000| PB 0x00000000c3100000| Untracked 
|1074|0x00000000c3200000, 0x00000000c3200000, 0x00000000c3300000|  0%| F|  |TAMS 0x00000000c3200000| PB 0x00000000c3200000| Untracked 
|1075|0x00000000c3300000, 0x00000000c3300000, 0x00000000c3400000|  0%| F|  |TAMS 0x00000000c3300000| PB 0x00000000c3300000| Untracked 
|1076|0x00000000c3400000, 0x00000000c3400000, 0x00000000c3500000|  0%| F|  |TAMS 0x00000000c3400000| PB 0x00000000c3400000| Untracked 
|1077|0x00000000c3500000, 0x00000000c3500000, 0x00000000c3600000|  0%| F|  |TAMS 0x00000000c3500000| PB 0x00000000c3500000| Untracked 
|1078|0x00000000c3600000, 0x00000000c3600000, 0x00000000c3700000|  0%| F|  |TAMS 0x00000000c3600000| PB 0x00000000c3600000| Untracked 
|1079|0x00000000c3700000, 0x00000000c3700000, 0x00000000c3800000|  0%| F|  |TAMS 0x00000000c3700000| PB 0x00000000c3700000| Untracked 
|1080|0x00000000c3800000, 0x00000000c3800000, 0x00000000c3900000|  0%| F|  |TAMS 0x00000000c3800000| PB 0x00000000c3800000| Untracked 
|1081|0x00000000c3900000, 0x00000000c3900000, 0x00000000c3a00000|  0%| F|  |TAMS 0x00000000c3900000| PB 0x00000000c3900000| Untracked 
|1082|0x00000000c3a00000, 0x00000000c3a00000, 0x00000000c3b00000|  0%| F|  |TAMS 0x00000000c3a00000| PB 0x00000000c3a00000| Untracked 
|1083|0x00000000c3b00000, 0x00000000c3b00000, 0x00000000c3c00000|  0%| F|  |TAMS 0x00000000c3b00000| PB 0x00000000c3b00000| Untracked 
|1084|0x00000000c3c00000, 0x00000000c3c00000, 0x00000000c3d00000|  0%| F|  |TAMS 0x00000000c3c00000| PB 0x00000000c3c00000| Untracked 
|1085|0x00000000c3d00000, 0x00000000c3d00000, 0x00000000c3e00000|  0%| F|  |TAMS 0x00000000c3d00000| PB 0x00000000c3d00000| Untracked 
|1086|0x00000000c3e00000, 0x00000000c3e00000, 0x00000000c3f00000|  0%| F|  |TAMS 0x00000000c3e00000| PB 0x00000000c3e00000| Untracked 
|1087|0x00000000c3f00000, 0x00000000c3f00000, 0x00000000c4000000|  0%| F|  |TAMS 0x00000000c3f00000| PB 0x00000000c3f00000| Untracked 
|1088|0x00000000c4000000, 0x00000000c4000000, 0x00000000c4100000|  0%| F|  |TAMS 0x00000000c4000000| PB 0x00000000c4000000| Untracked 
|1089|0x00000000c4100000, 0x00000000c4100000, 0x00000000c4200000|  0%| F|  |TAMS 0x00000000c4100000| PB 0x00000000c4100000| Untracked 
|1090|0x00000000c4200000, 0x00000000c4200000, 0x00000000c4300000|  0%| F|  |TAMS 0x00000000c4200000| PB 0x00000000c4200000| Untracked 
|1091|0x00000000c4300000, 0x00000000c4300000, 0x00000000c4400000|  0%| F|  |TAMS 0x00000000c4300000| PB 0x00000000c4300000| Untracked 
|1092|0x00000000c4400000, 0x00000000c4400000, 0x00000000c4500000|  0%| F|  |TAMS 0x00000000c4400000| PB 0x00000000c4400000| Untracked 
|1093|0x00000000c4500000, 0x00000000c4500000, 0x00000000c4600000|  0%| F|  |TAMS 0x00000000c4500000| PB 0x00000000c4500000| Untracked 
|1094|0x00000000c4600000, 0x00000000c4600000, 0x00000000c4700000|  0%| F|  |TAMS 0x00000000c4600000| PB 0x00000000c4600000| Untracked 
|1095|0x00000000c4700000, 0x00000000c4700000, 0x00000000c4800000|  0%| F|  |TAMS 0x00000000c4700000| PB 0x00000000c4700000| Untracked 
|1096|0x00000000c4800000, 0x00000000c4800000, 0x00000000c4900000|  0%| F|  |TAMS 0x00000000c4800000| PB 0x00000000c4800000| Untracked 
|1097|0x00000000c4900000, 0x00000000c4900000, 0x00000000c4a00000|  0%| F|  |TAMS 0x00000000c4900000| PB 0x00000000c4900000| Untracked 
|1098|0x00000000c4a00000, 0x00000000c4a00000, 0x00000000c4b00000|  0%| F|  |TAMS 0x00000000c4a00000| PB 0x00000000c4a00000| Untracked 
|1099|0x00000000c4b00000, 0x00000000c4b00000, 0x00000000c4c00000|  0%| F|  |TAMS 0x00000000c4b00000| PB 0x00000000c4b00000| Untracked 
|1100|0x00000000c4c00000, 0x00000000c4c00000, 0x00000000c4d00000|  0%| F|  |TAMS 0x00000000c4c00000| PB 0x00000000c4c00000| Untracked 
|1101|0x00000000c4d00000, 0x00000000c4d00000, 0x00000000c4e00000|  0%| F|  |TAMS 0x00000000c4d00000| PB 0x00000000c4d00000| Untracked 
|1102|0x00000000c4e00000, 0x00000000c4e00000, 0x00000000c4f00000|  0%| F|  |TAMS 0x00000000c4e00000| PB 0x00000000c4e00000| Untracked 
|1103|0x00000000c4f00000, 0x00000000c4f00000, 0x00000000c5000000|  0%| F|  |TAMS 0x00000000c4f00000| PB 0x00000000c4f00000| Untracked 
|1104|0x00000000c5000000, 0x00000000c5000000, 0x00000000c5100000|  0%| F|  |TAMS 0x00000000c5000000| PB 0x00000000c5000000| Untracked 
|1105|0x00000000c5100000, 0x00000000c5100000, 0x00000000c5200000|  0%| F|  |TAMS 0x00000000c5100000| PB 0x00000000c5100000| Untracked 
|1106|0x00000000c5200000, 0x00000000c5200000, 0x00000000c5300000|  0%| F|  |TAMS 0x00000000c5200000| PB 0x00000000c5200000| Untracked 
|1107|0x00000000c5300000, 0x00000000c5300000, 0x00000000c5400000|  0%| F|  |TAMS 0x00000000c5300000| PB 0x00000000c5300000| Untracked 
|1108|0x00000000c5400000, 0x00000000c5400000, 0x00000000c5500000|  0%| F|  |TAMS 0x00000000c5400000| PB 0x00000000c5400000| Untracked 
|1109|0x00000000c5500000, 0x00000000c5500000, 0x00000000c5600000|  0%| F|  |TAMS 0x00000000c5500000| PB 0x00000000c5500000| Untracked 
|1110|0x00000000c5600000, 0x00000000c5600000, 0x00000000c5700000|  0%| F|  |TAMS 0x00000000c5600000| PB 0x00000000c5600000| Untracked 
|1111|0x00000000c5700000, 0x00000000c5700000, 0x00000000c5800000|  0%| F|  |TAMS 0x00000000c5700000| PB 0x00000000c5700000| Untracked 
|1112|0x00000000c5800000, 0x00000000c5800000, 0x00000000c5900000|  0%| F|  |TAMS 0x00000000c5800000| PB 0x00000000c5800000| Untracked 
|1113|0x00000000c5900000, 0x00000000c5900000, 0x00000000c5a00000|  0%| F|  |TAMS 0x00000000c5900000| PB 0x00000000c5900000| Untracked 
|1114|0x00000000c5a00000, 0x00000000c5a00000, 0x00000000c5b00000|  0%| F|  |TAMS 0x00000000c5a00000| PB 0x00000000c5a00000| Untracked 
|1115|0x00000000c5b00000, 0x00000000c5b00000, 0x00000000c5c00000|  0%| F|  |TAMS 0x00000000c5b00000| PB 0x00000000c5b00000| Untracked 
|1116|0x00000000c5c00000, 0x00000000c5c00000, 0x00000000c5d00000|  0%| F|  |TAMS 0x00000000c5c00000| PB 0x00000000c5c00000| Untracked 
|1117|0x00000000c5d00000, 0x00000000c5d00000, 0x00000000c5e00000|  0%| F|  |TAMS 0x00000000c5d00000| PB 0x00000000c5d00000| Untracked 
|1118|0x00000000c5e00000, 0x00000000c5e00000, 0x00000000c5f00000|  0%| F|  |TAMS 0x00000000c5e00000| PB 0x00000000c5e00000| Untracked 
|1119|0x00000000c5f00000, 0x00000000c5f00000, 0x00000000c6000000|  0%| F|  |TAMS 0x00000000c5f00000| PB 0x00000000c5f00000| Untracked 
|1120|0x00000000c6000000, 0x00000000c6000000, 0x00000000c6100000|  0%| F|  |TAMS 0x00000000c6000000| PB 0x00000000c6000000| Untracked 
|1121|0x00000000c6100000, 0x00000000c6100000, 0x00000000c6200000|  0%| F|  |TAMS 0x00000000c6100000| PB 0x00000000c6100000| Untracked 
|1122|0x00000000c6200000, 0x00000000c6200000, 0x00000000c6300000|  0%| F|  |TAMS 0x00000000c6200000| PB 0x00000000c6200000| Untracked 
|1123|0x00000000c6300000, 0x00000000c6300000, 0x00000000c6400000|  0%| F|  |TAMS 0x00000000c6300000| PB 0x00000000c6300000| Untracked 
|1124|0x00000000c6400000, 0x00000000c6400000, 0x00000000c6500000|  0%| F|  |TAMS 0x00000000c6400000| PB 0x00000000c6400000| Untracked 
|1125|0x00000000c6500000, 0x00000000c6500000, 0x00000000c6600000|  0%| F|  |TAMS 0x00000000c6500000| PB 0x00000000c6500000| Untracked 
|1126|0x00000000c6600000, 0x00000000c6600000, 0x00000000c6700000|  0%| F|  |TAMS 0x00000000c6600000| PB 0x00000000c6600000| Untracked 
|1127|0x00000000c6700000, 0x00000000c6700000, 0x00000000c6800000|  0%| F|  |TAMS 0x00000000c6700000| PB 0x00000000c6700000| Untracked 
|1128|0x00000000c6800000, 0x00000000c6800000, 0x00000000c6900000|  0%| F|  |TAMS 0x00000000c6800000| PB 0x00000000c6800000| Untracked 
|1129|0x00000000c6900000, 0x00000000c6900000, 0x00000000c6a00000|  0%| F|  |TAMS 0x00000000c6900000| PB 0x00000000c6900000| Untracked 
|1130|0x00000000c6a00000, 0x00000000c6a00000, 0x00000000c6b00000|  0%| F|  |TAMS 0x00000000c6a00000| PB 0x00000000c6a00000| Untracked 
|1131|0x00000000c6b00000, 0x00000000c6b00000, 0x00000000c6c00000|  0%| F|  |TAMS 0x00000000c6b00000| PB 0x00000000c6b00000| Untracked 
|1132|0x00000000c6c00000, 0x00000000c6c00000, 0x00000000c6d00000|  0%| F|  |TAMS 0x00000000c6c00000| PB 0x00000000c6c00000| Untracked 
|1133|0x00000000c6d00000, 0x00000000c6d00000, 0x00000000c6e00000|  0%| F|  |TAMS 0x00000000c6d00000| PB 0x00000000c6d00000| Untracked 
|1134|0x00000000c6e00000, 0x00000000c6e00000, 0x00000000c6f00000|  0%| F|  |TAMS 0x00000000c6e00000| PB 0x00000000c6e00000| Untracked 
|1135|0x00000000c6f00000, 0x00000000c6f00000, 0x00000000c7000000|  0%| F|  |TAMS 0x00000000c6f00000| PB 0x00000000c6f00000| Untracked 
|1136|0x00000000c7000000, 0x00000000c7000000, 0x00000000c7100000|  0%| F|  |TAMS 0x00000000c7000000| PB 0x00000000c7000000| Untracked 
|1137|0x00000000c7100000, 0x00000000c7100000, 0x00000000c7200000|  0%| F|  |TAMS 0x00000000c7100000| PB 0x00000000c7100000| Untracked 
|1138|0x00000000c7200000, 0x00000000c7200000, 0x00000000c7300000|  0%| F|  |TAMS 0x00000000c7200000| PB 0x00000000c7200000| Untracked 
|1139|0x00000000c7300000, 0x00000000c7300000, 0x00000000c7400000|  0%| F|  |TAMS 0x00000000c7300000| PB 0x00000000c7300000| Untracked 
|1140|0x00000000c7400000, 0x00000000c7400000, 0x00000000c7500000|  0%| F|  |TAMS 0x00000000c7400000| PB 0x00000000c7400000| Untracked 
|1141|0x00000000c7500000, 0x00000000c7500000, 0x00000000c7600000|  0%| F|  |TAMS 0x00000000c7500000| PB 0x00000000c7500000| Untracked 
|1142|0x00000000c7600000, 0x00000000c7600000, 0x00000000c7700000|  0%| F|  |TAMS 0x00000000c7600000| PB 0x00000000c7600000| Untracked 
|1143|0x00000000c7700000, 0x00000000c7800000, 0x00000000c7800000|100%| O|Cm|TAMS 0x00000000c7700000| PB 0x00000000c7700000| Complete 
|1144|0x00000000c7800000, 0x00000000c7800000, 0x00000000c7900000|  0%| F|  |TAMS 0x00000000c7800000| PB 0x00000000c7800000| Untracked 
|1145|0x00000000c7900000, 0x00000000c7900000, 0x00000000c7a00000|  0%| F|  |TAMS 0x00000000c7900000| PB 0x00000000c7900000| Untracked 
|1146|0x00000000c7a00000, 0x00000000c7a00000, 0x00000000c7b00000|  0%| F|  |TAMS 0x00000000c7a00000| PB 0x00000000c7a00000| Untracked 
|1147|0x00000000c7b00000, 0x00000000c7b00000, 0x00000000c7c00000|  0%| F|  |TAMS 0x00000000c7b00000| PB 0x00000000c7b00000| Untracked 
|1148|0x00000000c7c00000, 0x00000000c7c00000, 0x00000000c7d00000|  0%| F|  |TAMS 0x00000000c7c00000| PB 0x00000000c7c00000| Untracked 
|1149|0x00000000c7d00000, 0x00000000c7e00000, 0x00000000c7e00000|100%| O|Cm|TAMS 0x00000000c7d00000| PB 0x00000000c7d00000| Complete 
|1150|0x00000000c7e00000, 0x00000000c7e00000, 0x00000000c7f00000|  0%| F|  |TAMS 0x00000000c7e00000| PB 0x00000000c7e00000| Untracked 
|1151|0x00000000c7f00000, 0x00000000c8000000, 0x00000000c8000000|100%| O|  |TAMS 0x00000000c7f00000| PB 0x00000000c7f00000| Untracked 
|1152|0x00000000c8000000, 0x00000000c8000000, 0x00000000c8100000|  0%| F|  |TAMS 0x00000000c8000000| PB 0x00000000c8000000| Untracked 
|1153|0x00000000c8100000, 0x00000000c8100000, 0x00000000c8200000|  0%| F|  |TAMS 0x00000000c8100000| PB 0x00000000c8100000| Untracked 
|1154|0x00000000c8200000, 0x00000000c8200000, 0x00000000c8300000|  0%| F|  |TAMS 0x00000000c8200000| PB 0x00000000c8200000| Untracked 
|1155|0x00000000c8300000, 0x00000000c8300000, 0x00000000c8400000|  0%| F|  |TAMS 0x00000000c8300000| PB 0x00000000c8300000| Untracked 
|1156|0x00000000c8400000, 0x00000000c8500000, 0x00000000c8500000|100%| O|Cm|TAMS 0x00000000c8400000| PB 0x00000000c8400000| Complete 
|1157|0x00000000c8500000, 0x00000000c8600000, 0x00000000c8600000|100%| O|Cm|TAMS 0x00000000c8500000| PB 0x00000000c8500000| Complete 
|1158|0x00000000c8600000, 0x00000000c8700000, 0x00000000c8700000|100%| O|  |TAMS 0x00000000c8600000| PB 0x00000000c8600000| Untracked 
|1159|0x00000000c8700000, 0x00000000c8700000, 0x00000000c8800000|  0%| F|  |TAMS 0x00000000c8700000| PB 0x00000000c8700000| Untracked 
|1160|0x00000000c8800000, 0x00000000c8800000, 0x00000000c8900000|  0%| F|  |TAMS 0x00000000c8800000| PB 0x00000000c8800000| Untracked 
|1161|0x00000000c8900000, 0x00000000c8900000, 0x00000000c8a00000|  0%| F|  |TAMS 0x00000000c8900000| PB 0x00000000c8900000| Untracked 
|1162|0x00000000c8a00000, 0x00000000c8a00000, 0x00000000c8b00000|  0%| F|  |TAMS 0x00000000c8a00000| PB 0x00000000c8a00000| Untracked 
|1163|0x00000000c8b00000, 0x00000000c8b00000, 0x00000000c8c00000|  0%| F|  |TAMS 0x00000000c8b00000| PB 0x00000000c8b00000| Untracked 
|1164|0x00000000c8c00000, 0x00000000c8c00000, 0x00000000c8d00000|  0%| F|  |TAMS 0x00000000c8c00000| PB 0x00000000c8c00000| Untracked 
|1165|0x00000000c8d00000, 0x00000000c8d00000, 0x00000000c8e00000|  0%| F|  |TAMS 0x00000000c8d00000| PB 0x00000000c8d00000| Untracked 
|1166|0x00000000c8e00000, 0x00000000c8e00000, 0x00000000c8f00000|  0%| F|  |TAMS 0x00000000c8e00000| PB 0x00000000c8e00000| Untracked 
|1167|0x00000000c8f00000, 0x00000000c8f00000, 0x00000000c9000000|  0%| F|  |TAMS 0x00000000c8f00000| PB 0x00000000c8f00000| Untracked 
|1168|0x00000000c9000000, 0x00000000c9000000, 0x00000000c9100000|  0%| F|  |TAMS 0x00000000c9000000| PB 0x00000000c9000000| Untracked 
|1169|0x00000000c9100000, 0x00000000c9100000, 0x00000000c9200000|  0%| F|  |TAMS 0x00000000c9100000| PB 0x00000000c9100000| Untracked 
|1170|0x00000000c9200000, 0x00000000c9200000, 0x00000000c9300000|  0%| F|  |TAMS 0x00000000c9200000| PB 0x00000000c9200000| Untracked 
|1171|0x00000000c9300000, 0x00000000c9300000, 0x00000000c9400000|  0%| F|  |TAMS 0x00000000c9300000| PB 0x00000000c9300000| Untracked 
|1172|0x00000000c9400000, 0x00000000c9400000, 0x00000000c9500000|  0%| F|  |TAMS 0x00000000c9400000| PB 0x00000000c9400000| Untracked 
|1173|0x00000000c9500000, 0x00000000c9500000, 0x00000000c9600000|  0%| F|  |TAMS 0x00000000c9500000| PB 0x00000000c9500000| Untracked 
|1174|0x00000000c9600000, 0x00000000c9600000, 0x00000000c9700000|  0%| F|  |TAMS 0x00000000c9600000| PB 0x00000000c9600000| Untracked 
|1175|0x00000000c9700000, 0x00000000c9700000, 0x00000000c9800000|  0%| F|  |TAMS 0x00000000c9700000| PB 0x00000000c9700000| Untracked 
|1176|0x00000000c9800000, 0x00000000c9800000, 0x00000000c9900000|  0%| F|  |TAMS 0x00000000c9800000| PB 0x00000000c9800000| Untracked 
|1177|0x00000000c9900000, 0x00000000c9900000, 0x00000000c9a00000|  0%| F|  |TAMS 0x00000000c9900000| PB 0x00000000c9900000| Untracked 
|1178|0x00000000c9a00000, 0x00000000c9a00000, 0x00000000c9b00000|  0%| F|  |TAMS 0x00000000c9a00000| PB 0x00000000c9a00000| Untracked 
|1179|0x00000000c9b00000, 0x00000000c9b00000, 0x00000000c9c00000|  0%| F|  |TAMS 0x00000000c9b00000| PB 0x00000000c9b00000| Untracked 
|1180|0x00000000c9c00000, 0x00000000c9c00000, 0x00000000c9d00000|  0%| F|  |TAMS 0x00000000c9c00000| PB 0x00000000c9c00000| Untracked 
|1181|0x00000000c9d00000, 0x00000000c9d00000, 0x00000000c9e00000|  0%| F|  |TAMS 0x00000000c9d00000| PB 0x00000000c9d00000| Untracked 
|1182|0x00000000c9e00000, 0x00000000c9e00000, 0x00000000c9f00000|  0%| F|  |TAMS 0x00000000c9e00000| PB 0x00000000c9e00000| Untracked 
|1183|0x00000000c9f00000, 0x00000000c9f00000, 0x00000000ca000000|  0%| F|  |TAMS 0x00000000c9f00000| PB 0x00000000c9f00000| Untracked 
|1184|0x00000000ca000000, 0x00000000ca000000, 0x00000000ca100000|  0%| F|  |TAMS 0x00000000ca000000| PB 0x00000000ca000000| Untracked 
|1185|0x00000000ca100000, 0x00000000ca100000, 0x00000000ca200000|  0%| F|  |TAMS 0x00000000ca100000| PB 0x00000000ca100000| Untracked 
|1186|0x00000000ca200000, 0x00000000ca200000, 0x00000000ca300000|  0%| F|  |TAMS 0x00000000ca200000| PB 0x00000000ca200000| Untracked 
|1187|0x00000000ca300000, 0x00000000ca300000, 0x00000000ca400000|  0%| F|  |TAMS 0x00000000ca300000| PB 0x00000000ca300000| Untracked 
|1188|0x00000000ca400000, 0x00000000ca400000, 0x00000000ca500000|  0%| F|  |TAMS 0x00000000ca400000| PB 0x00000000ca400000| Untracked 
|1189|0x00000000ca500000, 0x00000000ca500000, 0x00000000ca600000|  0%| F|  |TAMS 0x00000000ca500000| PB 0x00000000ca500000| Untracked 
|1190|0x00000000ca600000, 0x00000000ca600000, 0x00000000ca700000|  0%| F|  |TAMS 0x00000000ca600000| PB 0x00000000ca600000| Untracked 
|1191|0x00000000ca700000, 0x00000000ca700000, 0x00000000ca800000|  0%| F|  |TAMS 0x00000000ca700000| PB 0x00000000ca700000| Untracked 
|1192|0x00000000ca800000, 0x00000000ca800000, 0x00000000ca900000|  0%| F|  |TAMS 0x00000000ca800000| PB 0x00000000ca800000| Untracked 
|1193|0x00000000ca900000, 0x00000000ca900000, 0x00000000caa00000|  0%| F|  |TAMS 0x00000000ca900000| PB 0x00000000ca900000| Untracked 
|1194|0x00000000caa00000, 0x00000000caa00000, 0x00000000cab00000|  0%| F|  |TAMS 0x00000000caa00000| PB 0x00000000caa00000| Untracked 
|1195|0x00000000cab00000, 0x00000000cab00000, 0x00000000cac00000|  0%| F|  |TAMS 0x00000000cab00000| PB 0x00000000cab00000| Untracked 
|1196|0x00000000cac00000, 0x00000000cac00000, 0x00000000cad00000|  0%| F|  |TAMS 0x00000000cac00000| PB 0x00000000cac00000| Untracked 
|1197|0x00000000cad00000, 0x00000000cad00000, 0x00000000cae00000|  0%| F|  |TAMS 0x00000000cad00000| PB 0x00000000cad00000| Untracked 
|1198|0x00000000cae00000, 0x00000000cae00000, 0x00000000caf00000|  0%| F|  |TAMS 0x00000000cae00000| PB 0x00000000cae00000| Untracked 
|1199|0x00000000caf00000, 0x00000000caf00000, 0x00000000cb000000|  0%| F|  |TAMS 0x00000000caf00000| PB 0x00000000caf00000| Untracked 
|1200|0x00000000cb000000, 0x00000000cb000000, 0x00000000cb100000|  0%| F|  |TAMS 0x00000000cb000000| PB 0x00000000cb000000| Untracked 
|1201|0x00000000cb100000, 0x00000000cb100000, 0x00000000cb200000|  0%| F|  |TAMS 0x00000000cb100000| PB 0x00000000cb100000| Untracked 
|1202|0x00000000cb200000, 0x00000000cb200000, 0x00000000cb300000|  0%| F|  |TAMS 0x00000000cb200000| PB 0x00000000cb200000| Untracked 
|1203|0x00000000cb300000, 0x00000000cb300000, 0x00000000cb400000|  0%| F|  |TAMS 0x00000000cb300000| PB 0x00000000cb300000| Untracked 
|1204|0x00000000cb400000, 0x00000000cb400000, 0x00000000cb500000|  0%| F|  |TAMS 0x00000000cb400000| PB 0x00000000cb400000| Untracked 
|1205|0x00000000cb500000, 0x00000000cb500000, 0x00000000cb600000|  0%| F|  |TAMS 0x00000000cb500000| PB 0x00000000cb500000| Untracked 
|1206|0x00000000cb600000, 0x00000000cb600000, 0x00000000cb700000|  0%| F|  |TAMS 0x00000000cb600000| PB 0x00000000cb600000| Untracked 
|1207|0x00000000cb700000, 0x00000000cb700000, 0x00000000cb800000|  0%| F|  |TAMS 0x00000000cb700000| PB 0x00000000cb700000| Untracked 
|1208|0x00000000cb800000, 0x00000000cb800000, 0x00000000cb900000|  0%| F|  |TAMS 0x00000000cb800000| PB 0x00000000cb800000| Untracked 
|1209|0x00000000cb900000, 0x00000000cb900000, 0x00000000cba00000|  0%| F|  |TAMS 0x00000000cb900000| PB 0x00000000cb900000| Untracked 
|1210|0x00000000cba00000, 0x00000000cba00000, 0x00000000cbb00000|  0%| F|  |TAMS 0x00000000cba00000| PB 0x00000000cba00000| Untracked 
|1211|0x00000000cbb00000, 0x00000000cbb00000, 0x00000000cbc00000|  0%| F|  |TAMS 0x00000000cbb00000| PB 0x00000000cbb00000| Untracked 
|1212|0x00000000cbc00000, 0x00000000cbc00000, 0x00000000cbd00000|  0%| F|  |TAMS 0x00000000cbc00000| PB 0x00000000cbc00000| Untracked 
|1213|0x00000000cbd00000, 0x00000000cbd00000, 0x00000000cbe00000|  0%| F|  |TAMS 0x00000000cbd00000| PB 0x00000000cbd00000| Untracked 
|1214|0x00000000cbe00000, 0x00000000cbe00000, 0x00000000cbf00000|  0%| F|  |TAMS 0x00000000cbe00000| PB 0x00000000cbe00000| Untracked 
|1215|0x00000000cbf00000, 0x00000000cbf00000, 0x00000000cc000000|  0%| F|  |TAMS 0x00000000cbf00000| PB 0x00000000cbf00000| Untracked 
|1216|0x00000000cc000000, 0x00000000cc000000, 0x00000000cc100000|  0%| F|  |TAMS 0x00000000cc000000| PB 0x00000000cc000000| Untracked 
|1217|0x00000000cc100000, 0x00000000cc100000, 0x00000000cc200000|  0%| F|  |TAMS 0x00000000cc100000| PB 0x00000000cc100000| Untracked 
|1218|0x00000000cc200000, 0x00000000cc200000, 0x00000000cc300000|  0%| F|  |TAMS 0x00000000cc200000| PB 0x00000000cc200000| Untracked 
|1219|0x00000000cc300000, 0x00000000cc300000, 0x00000000cc400000|  0%| F|  |TAMS 0x00000000cc300000| PB 0x00000000cc300000| Untracked 
|1220|0x00000000cc400000, 0x00000000cc400000, 0x00000000cc500000|  0%| F|  |TAMS 0x00000000cc400000| PB 0x00000000cc400000| Untracked 
|1221|0x00000000cc500000, 0x00000000cc500000, 0x00000000cc600000|  0%| F|  |TAMS 0x00000000cc500000| PB 0x00000000cc500000| Untracked 
|1222|0x00000000cc600000, 0x00000000cc600000, 0x00000000cc700000|  0%| F|  |TAMS 0x00000000cc600000| PB 0x00000000cc600000| Untracked 
|1223|0x00000000cc700000, 0x00000000cc700000, 0x00000000cc800000|  0%| F|  |TAMS 0x00000000cc700000| PB 0x00000000cc700000| Untracked 
|1224|0x00000000cc800000, 0x00000000cc800000, 0x00000000cc900000|  0%| F|  |TAMS 0x00000000cc800000| PB 0x00000000cc800000| Untracked 
|1225|0x00000000cc900000, 0x00000000cc900000, 0x00000000cca00000|  0%| F|  |TAMS 0x00000000cc900000| PB 0x00000000cc900000| Untracked 
|1226|0x00000000cca00000, 0x00000000cca00000, 0x00000000ccb00000|  0%| F|  |TAMS 0x00000000cca00000| PB 0x00000000cca00000| Untracked 
|1227|0x00000000ccb00000, 0x00000000ccb00000, 0x00000000ccc00000|  0%| F|  |TAMS 0x00000000ccb00000| PB 0x00000000ccb00000| Untracked 
|1228|0x00000000ccc00000, 0x00000000ccc00000, 0x00000000ccd00000|  0%| F|  |TAMS 0x00000000ccc00000| PB 0x00000000ccc00000| Untracked 
|1229|0x00000000ccd00000, 0x00000000ccd00000, 0x00000000cce00000|  0%| F|  |TAMS 0x00000000ccd00000| PB 0x00000000ccd00000| Untracked 
|1230|0x00000000cce00000, 0x00000000cce00000, 0x00000000ccf00000|  0%| F|  |TAMS 0x00000000cce00000| PB 0x00000000cce00000| Untracked 
|1231|0x00000000ccf00000, 0x00000000ccf00000, 0x00000000cd000000|  0%| F|  |TAMS 0x00000000ccf00000| PB 0x00000000ccf00000| Untracked 
|1232|0x00000000cd000000, 0x00000000cd000000, 0x00000000cd100000|  0%| F|  |TAMS 0x00000000cd000000| PB 0x00000000cd000000| Untracked 
|1233|0x00000000cd100000, 0x00000000cd100000, 0x00000000cd200000|  0%| F|  |TAMS 0x00000000cd100000| PB 0x00000000cd100000| Untracked 
|1234|0x00000000cd200000, 0x00000000cd200000, 0x00000000cd300000|  0%| F|  |TAMS 0x00000000cd200000| PB 0x00000000cd200000| Untracked 
|1235|0x00000000cd300000, 0x00000000cd300000, 0x00000000cd400000|  0%| F|  |TAMS 0x00000000cd300000| PB 0x00000000cd300000| Untracked 
|1236|0x00000000cd400000, 0x00000000cd400000, 0x00000000cd500000|  0%| F|  |TAMS 0x00000000cd400000| PB 0x00000000cd400000| Untracked 
|1237|0x00000000cd500000, 0x00000000cd500000, 0x00000000cd600000|  0%| F|  |TAMS 0x00000000cd500000| PB 0x00000000cd500000| Untracked 
|1238|0x00000000cd600000, 0x00000000cd600000, 0x00000000cd700000|  0%| F|  |TAMS 0x00000000cd600000| PB 0x00000000cd600000| Untracked 
|1239|0x00000000cd700000, 0x00000000cd700000, 0x00000000cd800000|  0%| F|  |TAMS 0x00000000cd700000| PB 0x00000000cd700000| Untracked 
|1240|0x00000000cd800000, 0x00000000cd800000, 0x00000000cd900000|  0%| F|  |TAMS 0x00000000cd800000| PB 0x00000000cd800000| Untracked 
|1241|0x00000000cd900000, 0x00000000cd900000, 0x00000000cda00000|  0%| F|  |TAMS 0x00000000cd900000| PB 0x00000000cd900000| Untracked 
|1242|0x00000000cda00000, 0x00000000cda00000, 0x00000000cdb00000|  0%| F|  |TAMS 0x00000000cda00000| PB 0x00000000cda00000| Untracked 
|1243|0x00000000cdb00000, 0x00000000cdb00000, 0x00000000cdc00000|  0%| F|  |TAMS 0x00000000cdb00000| PB 0x00000000cdb00000| Untracked 
|1244|0x00000000cdc00000, 0x00000000cdc00000, 0x00000000cdd00000|  0%| F|  |TAMS 0x00000000cdc00000| PB 0x00000000cdc00000| Untracked 
|1245|0x00000000cdd00000, 0x00000000cdd00000, 0x00000000cde00000|  0%| F|  |TAMS 0x00000000cdd00000| PB 0x00000000cdd00000| Untracked 
|1246|0x00000000cde00000, 0x00000000cde00000, 0x00000000cdf00000|  0%| F|  |TAMS 0x00000000cde00000| PB 0x00000000cde00000| Untracked 
|1247|0x00000000cdf00000, 0x00000000cdf00000, 0x00000000ce000000|  0%| F|  |TAMS 0x00000000cdf00000| PB 0x00000000cdf00000| Untracked 
|1248|0x00000000ce000000, 0x00000000ce000000, 0x00000000ce100000|  0%| F|  |TAMS 0x00000000ce000000| PB 0x00000000ce000000| Untracked 
|1249|0x00000000ce100000, 0x00000000ce100000, 0x00000000ce200000|  0%| F|  |TAMS 0x00000000ce100000| PB 0x00000000ce100000| Untracked 
|1250|0x00000000ce200000, 0x00000000ce200000, 0x00000000ce300000|  0%| F|  |TAMS 0x00000000ce200000| PB 0x00000000ce200000| Untracked 
|1251|0x00000000ce300000, 0x00000000ce300000, 0x00000000ce400000|  0%| F|  |TAMS 0x00000000ce300000| PB 0x00000000ce300000| Untracked 
|1252|0x00000000ce400000, 0x00000000ce400000, 0x00000000ce500000|  0%| F|  |TAMS 0x00000000ce400000| PB 0x00000000ce400000| Untracked 
|1253|0x00000000ce500000, 0x00000000ce500000, 0x00000000ce600000|  0%| F|  |TAMS 0x00000000ce500000| PB 0x00000000ce500000| Untracked 
|1254|0x00000000ce600000, 0x00000000ce600000, 0x00000000ce700000|  0%| F|  |TAMS 0x00000000ce600000| PB 0x00000000ce600000| Untracked 
|1255|0x00000000ce700000, 0x00000000ce700000, 0x00000000ce800000|  0%| F|  |TAMS 0x00000000ce700000| PB 0x00000000ce700000| Untracked 
|1256|0x00000000ce800000, 0x00000000ce800000, 0x00000000ce900000|  0%| F|  |TAMS 0x00000000ce800000| PB 0x00000000ce800000| Untracked 
|1257|0x00000000ce900000, 0x00000000ce900000, 0x00000000cea00000|  0%| F|  |TAMS 0x00000000ce900000| PB 0x00000000ce900000| Untracked 
|1258|0x00000000cea00000, 0x00000000cea00000, 0x00000000ceb00000|  0%| F|  |TAMS 0x00000000cea00000| PB 0x00000000cea00000| Untracked 
|1259|0x00000000ceb00000, 0x00000000ceb00000, 0x00000000cec00000|  0%| F|  |TAMS 0x00000000ceb00000| PB 0x00000000ceb00000| Untracked 
|1260|0x00000000cec00000, 0x00000000cec00000, 0x00000000ced00000|  0%| F|  |TAMS 0x00000000cec00000| PB 0x00000000cec00000| Untracked 
|1261|0x00000000ced00000, 0x00000000ced00000, 0x00000000cee00000|  0%| F|  |TAMS 0x00000000ced00000| PB 0x00000000ced00000| Untracked 
|1262|0x00000000cee00000, 0x00000000cee00000, 0x00000000cef00000|  0%| F|  |TAMS 0x00000000cee00000| PB 0x00000000cee00000| Untracked 
|1263|0x00000000cef00000, 0x00000000cef00000, 0x00000000cf000000|  0%| F|  |TAMS 0x00000000cef00000| PB 0x00000000cef00000| Untracked 
|1264|0x00000000cf000000, 0x00000000cf000000, 0x00000000cf100000|  0%| F|  |TAMS 0x00000000cf000000| PB 0x00000000cf000000| Untracked 
|1265|0x00000000cf100000, 0x00000000cf100000, 0x00000000cf200000|  0%| F|  |TAMS 0x00000000cf100000| PB 0x00000000cf100000| Untracked 
|1266|0x00000000cf200000, 0x00000000cf200000, 0x00000000cf300000|  0%| F|  |TAMS 0x00000000cf200000| PB 0x00000000cf200000| Untracked 
|1267|0x00000000cf300000, 0x00000000cf300000, 0x00000000cf400000|  0%| F|  |TAMS 0x00000000cf300000| PB 0x00000000cf300000| Untracked 
|1268|0x00000000cf400000, 0x00000000cf400000, 0x00000000cf500000|  0%| F|  |TAMS 0x00000000cf400000| PB 0x00000000cf400000| Untracked 
|1269|0x00000000cf500000, 0x00000000cf500000, 0x00000000cf600000|  0%| F|  |TAMS 0x00000000cf500000| PB 0x00000000cf500000| Untracked 
|1270|0x00000000cf600000, 0x00000000cf600000, 0x00000000cf700000|  0%| F|  |TAMS 0x00000000cf600000| PB 0x00000000cf600000| Untracked 
|1271|0x00000000cf700000, 0x00000000cf700000, 0x00000000cf800000|  0%| F|  |TAMS 0x00000000cf700000| PB 0x00000000cf700000| Untracked 
|1272|0x00000000cf800000, 0x00000000cf800000, 0x00000000cf900000|  0%| F|  |TAMS 0x00000000cf800000| PB 0x00000000cf800000| Untracked 
|1273|0x00000000cf900000, 0x00000000cf900000, 0x00000000cfa00000|  0%| F|  |TAMS 0x00000000cf900000| PB 0x00000000cf900000| Untracked 
|1274|0x00000000cfa00000, 0x00000000cfa00000, 0x00000000cfb00000|  0%| F|  |TAMS 0x00000000cfa00000| PB 0x00000000cfa00000| Untracked 
|1275|0x00000000cfb00000, 0x00000000cfb00000, 0x00000000cfc00000|  0%| F|  |TAMS 0x00000000cfb00000| PB 0x00000000cfb00000| Untracked 
|1276|0x00000000cfc00000, 0x00000000cfc00000, 0x00000000cfd00000|  0%| F|  |TAMS 0x00000000cfc00000| PB 0x00000000cfc00000| Untracked 
|1277|0x00000000cfd00000, 0x00000000cfd00000, 0x00000000cfe00000|  0%| F|  |TAMS 0x00000000cfd00000| PB 0x00000000cfd00000| Untracked 
|1278|0x00000000cfe00000, 0x00000000cfe00000, 0x00000000cff00000|  0%| F|  |TAMS 0x00000000cfe00000| PB 0x00000000cfe00000| Untracked 
|1279|0x00000000cff00000, 0x00000000cff00000, 0x00000000d0000000|  0%| F|  |TAMS 0x00000000cff00000| PB 0x00000000cff00000| Untracked 
|1280|0x00000000d0000000, 0x00000000d0000000, 0x00000000d0100000|  0%| F|  |TAMS 0x00000000d0000000| PB 0x00000000d0000000| Untracked 
|1281|0x00000000d0100000, 0x00000000d0100000, 0x00000000d0200000|  0%| F|  |TAMS 0x00000000d0100000| PB 0x00000000d0100000| Untracked 
|1282|0x00000000d0200000, 0x00000000d0200000, 0x00000000d0300000|  0%| F|  |TAMS 0x00000000d0200000| PB 0x00000000d0200000| Untracked 
|1283|0x00000000d0300000, 0x00000000d0300000, 0x00000000d0400000|  0%| F|  |TAMS 0x00000000d0300000| PB 0x00000000d0300000| Untracked 
|1284|0x00000000d0400000, 0x00000000d0400000, 0x00000000d0500000|  0%| F|  |TAMS 0x00000000d0400000| PB 0x00000000d0400000| Untracked 
|1285|0x00000000d0500000, 0x00000000d0500000, 0x00000000d0600000|  0%| F|  |TAMS 0x00000000d0500000| PB 0x00000000d0500000| Untracked 
|1286|0x00000000d0600000, 0x00000000d0600000, 0x00000000d0700000|  0%| F|  |TAMS 0x00000000d0600000| PB 0x00000000d0600000| Untracked 
|1287|0x00000000d0700000, 0x00000000d0700000, 0x00000000d0800000|  0%| F|  |TAMS 0x00000000d0700000| PB 0x00000000d0700000| Untracked 
|1288|0x00000000d0800000, 0x00000000d0800000, 0x00000000d0900000|  0%| F|  |TAMS 0x00000000d0800000| PB 0x00000000d0800000| Untracked 
|1289|0x00000000d0900000, 0x00000000d0900000, 0x00000000d0a00000|  0%| F|  |TAMS 0x00000000d0900000| PB 0x00000000d0900000| Untracked 
|1290|0x00000000d0a00000, 0x00000000d0a00000, 0x00000000d0b00000|  0%| F|  |TAMS 0x00000000d0a00000| PB 0x00000000d0a00000| Untracked 
|1291|0x00000000d0b00000, 0x00000000d0b00000, 0x00000000d0c00000|  0%| F|  |TAMS 0x00000000d0b00000| PB 0x00000000d0b00000| Untracked 
|1292|0x00000000d0c00000, 0x00000000d0c00000, 0x00000000d0d00000|  0%| F|  |TAMS 0x00000000d0c00000| PB 0x00000000d0c00000| Untracked 
|1293|0x00000000d0d00000, 0x00000000d0d00000, 0x00000000d0e00000|  0%| F|  |TAMS 0x00000000d0d00000| PB 0x00000000d0d00000| Untracked 
|1294|0x00000000d0e00000, 0x00000000d0e00000, 0x00000000d0f00000|  0%| F|  |TAMS 0x00000000d0e00000| PB 0x00000000d0e00000| Untracked 
|1295|0x00000000d0f00000, 0x00000000d0f00000, 0x00000000d1000000|  0%| F|  |TAMS 0x00000000d0f00000| PB 0x00000000d0f00000| Untracked 
|1296|0x00000000d1000000, 0x00000000d1000000, 0x00000000d1100000|  0%| F|  |TAMS 0x00000000d1000000| PB 0x00000000d1000000| Untracked 
|1297|0x00000000d1100000, 0x00000000d1100000, 0x00000000d1200000|  0%| F|  |TAMS 0x00000000d1100000| PB 0x00000000d1100000| Untracked 
|1298|0x00000000d1200000, 0x00000000d1200000, 0x00000000d1300000|  0%| F|  |TAMS 0x00000000d1200000| PB 0x00000000d1200000| Untracked 
|1299|0x00000000d1300000, 0x00000000d1300000, 0x00000000d1400000|  0%| F|  |TAMS 0x00000000d1300000| PB 0x00000000d1300000| Untracked 
|1300|0x00000000d1400000, 0x00000000d1400000, 0x00000000d1500000|  0%| F|  |TAMS 0x00000000d1400000| PB 0x00000000d1400000| Untracked 
|1301|0x00000000d1500000, 0x00000000d1500000, 0x00000000d1600000|  0%| F|  |TAMS 0x00000000d1500000| PB 0x00000000d1500000| Untracked 
|1302|0x00000000d1600000, 0x00000000d1600000, 0x00000000d1700000|  0%| F|  |TAMS 0x00000000d1600000| PB 0x00000000d1600000| Untracked 
|1303|0x00000000d1700000, 0x00000000d1700000, 0x00000000d1800000|  0%| F|  |TAMS 0x00000000d1700000| PB 0x00000000d1700000| Untracked 
|1304|0x00000000d1800000, 0x00000000d1800000, 0x00000000d1900000|  0%| F|  |TAMS 0x00000000d1800000| PB 0x00000000d1800000| Untracked 
|1305|0x00000000d1900000, 0x00000000d1900000, 0x00000000d1a00000|  0%| F|  |TAMS 0x00000000d1900000| PB 0x00000000d1900000| Untracked 
|1306|0x00000000d1a00000, 0x00000000d1a00000, 0x00000000d1b00000|  0%| F|  |TAMS 0x00000000d1a00000| PB 0x00000000d1a00000| Untracked 
|1307|0x00000000d1b00000, 0x00000000d1b00000, 0x00000000d1c00000|  0%| F|  |TAMS 0x00000000d1b00000| PB 0x00000000d1b00000| Untracked 
|1308|0x00000000d1c00000, 0x00000000d1c00000, 0x00000000d1d00000|  0%| F|  |TAMS 0x00000000d1c00000| PB 0x00000000d1c00000| Untracked 
|1309|0x00000000d1d00000, 0x00000000d1d00000, 0x00000000d1e00000|  0%| F|  |TAMS 0x00000000d1d00000| PB 0x00000000d1d00000| Untracked 
|1310|0x00000000d1e00000, 0x00000000d1e00000, 0x00000000d1f00000|  0%| F|  |TAMS 0x00000000d1e00000| PB 0x00000000d1e00000| Untracked 
|1311|0x00000000d1f00000, 0x00000000d1f00000, 0x00000000d2000000|  0%| F|  |TAMS 0x00000000d1f00000| PB 0x00000000d1f00000| Untracked 
|1312|0x00000000d2000000, 0x00000000d2000000, 0x00000000d2100000|  0%| F|  |TAMS 0x00000000d2000000| PB 0x00000000d2000000| Untracked 
|1313|0x00000000d2100000, 0x00000000d2100000, 0x00000000d2200000|  0%| F|  |TAMS 0x00000000d2100000| PB 0x00000000d2100000| Untracked 
|1314|0x00000000d2200000, 0x00000000d2200000, 0x00000000d2300000|  0%| F|  |TAMS 0x00000000d2200000| PB 0x00000000d2200000| Untracked 
|1315|0x00000000d2300000, 0x00000000d2300000, 0x00000000d2400000|  0%| F|  |TAMS 0x00000000d2300000| PB 0x00000000d2300000| Untracked 
|1316|0x00000000d2400000, 0x00000000d2400000, 0x00000000d2500000|  0%| F|  |TAMS 0x00000000d2400000| PB 0x00000000d2400000| Untracked 
|1317|0x00000000d2500000, 0x00000000d2500000, 0x00000000d2600000|  0%| F|  |TAMS 0x00000000d2500000| PB 0x00000000d2500000| Untracked 
|1318|0x00000000d2600000, 0x00000000d2600000, 0x00000000d2700000|  0%| F|  |TAMS 0x00000000d2600000| PB 0x00000000d2600000| Untracked 
|1319|0x00000000d2700000, 0x00000000d2700000, 0x00000000d2800000|  0%| F|  |TAMS 0x00000000d2700000| PB 0x00000000d2700000| Untracked 
|1320|0x00000000d2800000, 0x00000000d2800000, 0x00000000d2900000|  0%| F|  |TAMS 0x00000000d2800000| PB 0x00000000d2800000| Untracked 
|1321|0x00000000d2900000, 0x00000000d2900000, 0x00000000d2a00000|  0%| F|  |TAMS 0x00000000d2900000| PB 0x00000000d2900000| Untracked 
|1322|0x00000000d2a00000, 0x00000000d2a00000, 0x00000000d2b00000|  0%| F|  |TAMS 0x00000000d2a00000| PB 0x00000000d2a00000| Untracked 
|1323|0x00000000d2b00000, 0x00000000d2b00000, 0x00000000d2c00000|  0%| F|  |TAMS 0x00000000d2b00000| PB 0x00000000d2b00000| Untracked 
|1324|0x00000000d2c00000, 0x00000000d2c00000, 0x00000000d2d00000|  0%| F|  |TAMS 0x00000000d2c00000| PB 0x00000000d2c00000| Untracked 
|1325|0x00000000d2d00000, 0x00000000d2d00000, 0x00000000d2e00000|  0%| F|  |TAMS 0x00000000d2d00000| PB 0x00000000d2d00000| Untracked 
|1326|0x00000000d2e00000, 0x00000000d2e00000, 0x00000000d2f00000|  0%| F|  |TAMS 0x00000000d2e00000| PB 0x00000000d2e00000| Untracked 
|1327|0x00000000d2f00000, 0x00000000d2f00000, 0x00000000d3000000|  0%| F|  |TAMS 0x00000000d2f00000| PB 0x00000000d2f00000| Untracked 
|1328|0x00000000d3000000, 0x00000000d3000000, 0x00000000d3100000|  0%| F|  |TAMS 0x00000000d3000000| PB 0x00000000d3000000| Untracked 
|1329|0x00000000d3100000, 0x00000000d3100000, 0x00000000d3200000|  0%| F|  |TAMS 0x00000000d3100000| PB 0x00000000d3100000| Untracked 
|1330|0x00000000d3200000, 0x00000000d3200000, 0x00000000d3300000|  0%| F|  |TAMS 0x00000000d3200000| PB 0x00000000d3200000| Untracked 
|1331|0x00000000d3300000, 0x00000000d3300000, 0x00000000d3400000|  0%| F|  |TAMS 0x00000000d3300000| PB 0x00000000d3300000| Untracked 
|1332|0x00000000d3400000, 0x00000000d3400000, 0x00000000d3500000|  0%| F|  |TAMS 0x00000000d3400000| PB 0x00000000d3400000| Untracked 
|1333|0x00000000d3500000, 0x00000000d3500000, 0x00000000d3600000|  0%| F|  |TAMS 0x00000000d3500000| PB 0x00000000d3500000| Untracked 
|1334|0x00000000d3600000, 0x00000000d3600000, 0x00000000d3700000|  0%| F|  |TAMS 0x00000000d3600000| PB 0x00000000d3600000| Untracked 
|1335|0x00000000d3700000, 0x00000000d3700000, 0x00000000d3800000|  0%| F|  |TAMS 0x00000000d3700000| PB 0x00000000d3700000| Untracked 
|1336|0x00000000d3800000, 0x00000000d3800000, 0x00000000d3900000|  0%| F|  |TAMS 0x00000000d3800000| PB 0x00000000d3800000| Untracked 
|1337|0x00000000d3900000, 0x00000000d3900000, 0x00000000d3a00000|  0%| F|  |TAMS 0x00000000d3900000| PB 0x00000000d3900000| Untracked 
|1338|0x00000000d3a00000, 0x00000000d3a00000, 0x00000000d3b00000|  0%| F|  |TAMS 0x00000000d3a00000| PB 0x00000000d3a00000| Untracked 
|1339|0x00000000d3b00000, 0x00000000d3b00000, 0x00000000d3c00000|  0%| F|  |TAMS 0x00000000d3b00000| PB 0x00000000d3b00000| Untracked 
|1340|0x00000000d3c00000, 0x00000000d3c00000, 0x00000000d3d00000|  0%| F|  |TAMS 0x00000000d3c00000| PB 0x00000000d3c00000| Untracked 
|1341|0x00000000d3d00000, 0x00000000d3d00000, 0x00000000d3e00000|  0%| F|  |TAMS 0x00000000d3d00000| PB 0x00000000d3d00000| Untracked 
|1342|0x00000000d3e00000, 0x00000000d3e00000, 0x00000000d3f00000|  0%| F|  |TAMS 0x00000000d3e00000| PB 0x00000000d3e00000| Untracked 
|1343|0x00000000d3f00000, 0x00000000d3f00000, 0x00000000d4000000|  0%| F|  |TAMS 0x00000000d3f00000| PB 0x00000000d3f00000| Untracked 
|1344|0x00000000d4000000, 0x00000000d4000000, 0x00000000d4100000|  0%| F|  |TAMS 0x00000000d4000000| PB 0x00000000d4000000| Untracked 
|1345|0x00000000d4100000, 0x00000000d4100000, 0x00000000d4200000|  0%| F|  |TAMS 0x00000000d4100000| PB 0x00000000d4100000| Untracked 
|1346|0x00000000d4200000, 0x00000000d4200000, 0x00000000d4300000|  0%| F|  |TAMS 0x00000000d4200000| PB 0x00000000d4200000| Untracked 
|1347|0x00000000d4300000, 0x00000000d4300000, 0x00000000d4400000|  0%| F|  |TAMS 0x00000000d4300000| PB 0x00000000d4300000| Untracked 
|1348|0x00000000d4400000, 0x00000000d4400000, 0x00000000d4500000|  0%| F|  |TAMS 0x00000000d4400000| PB 0x00000000d4400000| Untracked 
|1349|0x00000000d4500000, 0x00000000d4500000, 0x00000000d4600000|  0%| F|  |TAMS 0x00000000d4500000| PB 0x00000000d4500000| Untracked 
|1350|0x00000000d4600000, 0x00000000d4600000, 0x00000000d4700000|  0%| F|  |TAMS 0x00000000d4600000| PB 0x00000000d4600000| Untracked 
|1351|0x00000000d4700000, 0x00000000d4700000, 0x00000000d4800000|  0%| F|  |TAMS 0x00000000d4700000| PB 0x00000000d4700000| Untracked 
|1352|0x00000000d4800000, 0x00000000d4800000, 0x00000000d4900000|  0%| F|  |TAMS 0x00000000d4800000| PB 0x00000000d4800000| Untracked 
|1353|0x00000000d4900000, 0x00000000d4900000, 0x00000000d4a00000|  0%| F|  |TAMS 0x00000000d4900000| PB 0x00000000d4900000| Untracked 
|1354|0x00000000d4a00000, 0x00000000d4a00000, 0x00000000d4b00000|  0%| F|  |TAMS 0x00000000d4a00000| PB 0x00000000d4a00000| Untracked 
|1355|0x00000000d4b00000, 0x00000000d4b00000, 0x00000000d4c00000|  0%| F|  |TAMS 0x00000000d4b00000| PB 0x00000000d4b00000| Untracked 
|1356|0x00000000d4c00000, 0x00000000d4c00000, 0x00000000d4d00000|  0%| F|  |TAMS 0x00000000d4c00000| PB 0x00000000d4c00000| Untracked 
|1357|0x00000000d4d00000, 0x00000000d4d00000, 0x00000000d4e00000|  0%| F|  |TAMS 0x00000000d4d00000| PB 0x00000000d4d00000| Untracked 
|1358|0x00000000d4e00000, 0x00000000d4e00000, 0x00000000d4f00000|  0%| F|  |TAMS 0x00000000d4e00000| PB 0x00000000d4e00000| Untracked 
|1359|0x00000000d4f00000, 0x00000000d4f00000, 0x00000000d5000000|  0%| F|  |TAMS 0x00000000d4f00000| PB 0x00000000d4f00000| Untracked 
|1360|0x00000000d5000000, 0x00000000d5000000, 0x00000000d5100000|  0%| F|  |TAMS 0x00000000d5000000| PB 0x00000000d5000000| Untracked 
|1361|0x00000000d5100000, 0x00000000d5100000, 0x00000000d5200000|  0%| F|  |TAMS 0x00000000d5100000| PB 0x00000000d5100000| Untracked 
|1362|0x00000000d5200000, 0x00000000d5200000, 0x00000000d5300000|  0%| F|  |TAMS 0x00000000d5200000| PB 0x00000000d5200000| Untracked 
|1363|0x00000000d5300000, 0x00000000d5300000, 0x00000000d5400000|  0%| F|  |TAMS 0x00000000d5300000| PB 0x00000000d5300000| Untracked 
|1364|0x00000000d5400000, 0x00000000d5400000, 0x00000000d5500000|  0%| F|  |TAMS 0x00000000d5400000| PB 0x00000000d5400000| Untracked 
|1365|0x00000000d5500000, 0x00000000d5500000, 0x00000000d5600000|  0%| F|  |TAMS 0x00000000d5500000| PB 0x00000000d5500000| Untracked 
|1366|0x00000000d5600000, 0x00000000d5600000, 0x00000000d5700000|  0%| F|  |TAMS 0x00000000d5600000| PB 0x00000000d5600000| Untracked 
|1367|0x00000000d5700000, 0x00000000d5700000, 0x00000000d5800000|  0%| F|  |TAMS 0x00000000d5700000| PB 0x00000000d5700000| Untracked 
|1368|0x00000000d5800000, 0x00000000d5800000, 0x00000000d5900000|  0%| F|  |TAMS 0x00000000d5800000| PB 0x00000000d5800000| Untracked 
|1369|0x00000000d5900000, 0x00000000d5900000, 0x00000000d5a00000|  0%| F|  |TAMS 0x00000000d5900000| PB 0x00000000d5900000| Untracked 
|1370|0x00000000d5a00000, 0x00000000d5a00000, 0x00000000d5b00000|  0%| F|  |TAMS 0x00000000d5a00000| PB 0x00000000d5a00000| Untracked 
|1371|0x00000000d5b00000, 0x00000000d5b00000, 0x00000000d5c00000|  0%| F|  |TAMS 0x00000000d5b00000| PB 0x00000000d5b00000| Untracked 
|1372|0x00000000d5c00000, 0x00000000d5c00000, 0x00000000d5d00000|  0%| F|  |TAMS 0x00000000d5c00000| PB 0x00000000d5c00000| Untracked 
|1373|0x00000000d5d00000, 0x00000000d5d00000, 0x00000000d5e00000|  0%| F|  |TAMS 0x00000000d5d00000| PB 0x00000000d5d00000| Untracked 
|1374|0x00000000d5e00000, 0x00000000d5e00000, 0x00000000d5f00000|  0%| F|  |TAMS 0x00000000d5e00000| PB 0x00000000d5e00000| Untracked 
|1375|0x00000000d5f00000, 0x00000000d5f00000, 0x00000000d6000000|  0%| F|  |TAMS 0x00000000d5f00000| PB 0x00000000d5f00000| Untracked 
|1376|0x00000000d6000000, 0x00000000d6000000, 0x00000000d6100000|  0%| F|  |TAMS 0x00000000d6000000| PB 0x00000000d6000000| Untracked 
|1377|0x00000000d6100000, 0x00000000d6100000, 0x00000000d6200000|  0%| F|  |TAMS 0x00000000d6100000| PB 0x00000000d6100000| Untracked 
|1378|0x00000000d6200000, 0x00000000d6200000, 0x00000000d6300000|  0%| F|  |TAMS 0x00000000d6200000| PB 0x00000000d6200000| Untracked 
|1379|0x00000000d6300000, 0x00000000d6300000, 0x00000000d6400000|  0%| F|  |TAMS 0x00000000d6300000| PB 0x00000000d6300000| Untracked 
|1380|0x00000000d6400000, 0x00000000d6400000, 0x00000000d6500000|  0%| F|  |TAMS 0x00000000d6400000| PB 0x00000000d6400000| Untracked 
|1381|0x00000000d6500000, 0x00000000d6500000, 0x00000000d6600000|  0%| F|  |TAMS 0x00000000d6500000| PB 0x00000000d6500000| Untracked 
|1382|0x00000000d6600000, 0x00000000d6600000, 0x00000000d6700000|  0%| F|  |TAMS 0x00000000d6600000| PB 0x00000000d6600000| Untracked 
|1383|0x00000000d6700000, 0x00000000d6700000, 0x00000000d6800000|  0%| F|  |TAMS 0x00000000d6700000| PB 0x00000000d6700000| Untracked 
|1384|0x00000000d6800000, 0x00000000d6800000, 0x00000000d6900000|  0%| F|  |TAMS 0x00000000d6800000| PB 0x00000000d6800000| Untracked 
|1385|0x00000000d6900000, 0x00000000d6900000, 0x00000000d6a00000|  0%| F|  |TAMS 0x00000000d6900000| PB 0x00000000d6900000| Untracked 
|1386|0x00000000d6a00000, 0x00000000d6a00000, 0x00000000d6b00000|  0%| F|  |TAMS 0x00000000d6a00000| PB 0x00000000d6a00000| Untracked 
|1387|0x00000000d6b00000, 0x00000000d6b00000, 0x00000000d6c00000|  0%| F|  |TAMS 0x00000000d6b00000| PB 0x00000000d6b00000| Untracked 
|1388|0x00000000d6c00000, 0x00000000d6c00000, 0x00000000d6d00000|  0%| F|  |TAMS 0x00000000d6c00000| PB 0x00000000d6c00000| Untracked 
|1389|0x00000000d6d00000, 0x00000000d6d00000, 0x00000000d6e00000|  0%| F|  |TAMS 0x00000000d6d00000| PB 0x00000000d6d00000| Untracked 
|1390|0x00000000d6e00000, 0x00000000d6e00000, 0x00000000d6f00000|  0%| F|  |TAMS 0x00000000d6e00000| PB 0x00000000d6e00000| Untracked 
|1391|0x00000000d6f00000, 0x00000000d6f00000, 0x00000000d7000000|  0%| F|  |TAMS 0x00000000d6f00000| PB 0x00000000d6f00000| Untracked 
|1392|0x00000000d7000000, 0x00000000d7000000, 0x00000000d7100000|  0%| F|  |TAMS 0x00000000d7000000| PB 0x00000000d7000000| Untracked 
|1393|0x00000000d7100000, 0x00000000d7100000, 0x00000000d7200000|  0%| F|  |TAMS 0x00000000d7100000| PB 0x00000000d7100000| Untracked 
|1394|0x00000000d7200000, 0x00000000d7200000, 0x00000000d7300000|  0%| F|  |TAMS 0x00000000d7200000| PB 0x00000000d7200000| Untracked 
|1395|0x00000000d7300000, 0x00000000d7300000, 0x00000000d7400000|  0%| F|  |TAMS 0x00000000d7300000| PB 0x00000000d7300000| Untracked 
|1396|0x00000000d7400000, 0x00000000d7400000, 0x00000000d7500000|  0%| F|  |TAMS 0x00000000d7400000| PB 0x00000000d7400000| Untracked 
|1397|0x00000000d7500000, 0x00000000d7500000, 0x00000000d7600000|  0%| F|  |TAMS 0x00000000d7500000| PB 0x00000000d7500000| Untracked 
|1398|0x00000000d7600000, 0x00000000d7600000, 0x00000000d7700000|  0%| F|  |TAMS 0x00000000d7600000| PB 0x00000000d7600000| Untracked 
|1399|0x00000000d7700000, 0x00000000d7700000, 0x00000000d7800000|  0%| F|  |TAMS 0x00000000d7700000| PB 0x00000000d7700000| Untracked 
|1400|0x00000000d7800000, 0x00000000d7800000, 0x00000000d7900000|  0%| F|  |TAMS 0x00000000d7800000| PB 0x00000000d7800000| Untracked 
|1401|0x00000000d7900000, 0x00000000d7900000, 0x00000000d7a00000|  0%| F|  |TAMS 0x00000000d7900000| PB 0x00000000d7900000| Untracked 
|1402|0x00000000d7a00000, 0x00000000d7a00000, 0x00000000d7b00000|  0%| F|  |TAMS 0x00000000d7a00000| PB 0x00000000d7a00000| Untracked 
|1403|0x00000000d7b00000, 0x00000000d7b00000, 0x00000000d7c00000|  0%| F|  |TAMS 0x00000000d7b00000| PB 0x00000000d7b00000| Untracked 
|1404|0x00000000d7c00000, 0x00000000d7c00000, 0x00000000d7d00000|  0%| F|  |TAMS 0x00000000d7c00000| PB 0x00000000d7c00000| Untracked 
|1405|0x00000000d7d00000, 0x00000000d7d00000, 0x00000000d7e00000|  0%| F|  |TAMS 0x00000000d7d00000| PB 0x00000000d7d00000| Untracked 
|1406|0x00000000d7e00000, 0x00000000d7e00000, 0x00000000d7f00000|  0%| F|  |TAMS 0x00000000d7e00000| PB 0x00000000d7e00000| Untracked 
|1407|0x00000000d7f00000, 0x00000000d7f00000, 0x00000000d8000000|  0%| F|  |TAMS 0x00000000d7f00000| PB 0x00000000d7f00000| Untracked 
|1408|0x00000000d8000000, 0x00000000d8000000, 0x00000000d8100000|  0%| F|  |TAMS 0x00000000d8000000| PB 0x00000000d8000000| Untracked 
|1409|0x00000000d8100000, 0x00000000d8100000, 0x00000000d8200000|  0%| F|  |TAMS 0x00000000d8100000| PB 0x00000000d8100000| Untracked 
|1410|0x00000000d8200000, 0x00000000d8200000, 0x00000000d8300000|  0%| F|  |TAMS 0x00000000d8200000| PB 0x00000000d8200000| Untracked 
|1411|0x00000000d8300000, 0x00000000d8300000, 0x00000000d8400000|  0%| F|  |TAMS 0x00000000d8300000| PB 0x00000000d8300000| Untracked 
|1412|0x00000000d8400000, 0x00000000d8400000, 0x00000000d8500000|  0%| F|  |TAMS 0x00000000d8400000| PB 0x00000000d8400000| Untracked 
|1413|0x00000000d8500000, 0x00000000d8500000, 0x00000000d8600000|  0%| F|  |TAMS 0x00000000d8500000| PB 0x00000000d8500000| Untracked 
|1414|0x00000000d8600000, 0x00000000d8600000, 0x00000000d8700000|  0%| F|  |TAMS 0x00000000d8600000| PB 0x00000000d8600000| Untracked 
|1415|0x00000000d8700000, 0x00000000d8700000, 0x00000000d8800000|  0%| F|  |TAMS 0x00000000d8700000| PB 0x00000000d8700000| Untracked 
|1416|0x00000000d8800000, 0x00000000d8800000, 0x00000000d8900000|  0%| F|  |TAMS 0x00000000d8800000| PB 0x00000000d8800000| Untracked 
|1417|0x00000000d8900000, 0x00000000d8900000, 0x00000000d8a00000|  0%| F|  |TAMS 0x00000000d8900000| PB 0x00000000d8900000| Untracked 
|1418|0x00000000d8a00000, 0x00000000d8a00000, 0x00000000d8b00000|  0%| F|  |TAMS 0x00000000d8a00000| PB 0x00000000d8a00000| Untracked 
|1419|0x00000000d8b00000, 0x00000000d8b00000, 0x00000000d8c00000|  0%| F|  |TAMS 0x00000000d8b00000| PB 0x00000000d8b00000| Untracked 
|1420|0x00000000d8c00000, 0x00000000d8c80000, 0x00000000d8d00000| 50%| S|CS|TAMS 0x00000000d8c00000| PB 0x00000000d8c00000| Complete 
|1421|0x00000000d8d00000, 0x00000000d8e00000, 0x00000000d8e00000|100%| S|CS|TAMS 0x00000000d8d00000| PB 0x00000000d8d00000| Complete 
|1422|0x00000000d8e00000, 0x00000000d8f00000, 0x00000000d8f00000|100%| S|CS|TAMS 0x00000000d8e00000| PB 0x00000000d8e00000| Complete 
|1423|0x00000000d8f00000, 0x00000000d8f00000, 0x00000000d9000000|  0%| F|  |TAMS 0x00000000d8f00000| PB 0x00000000d8f00000| Untracked 
|1424|0x00000000d9000000, 0x00000000d9000000, 0x00000000d9100000|  0%| F|  |TAMS 0x00000000d9000000| PB 0x00000000d9000000| Untracked 
|1425|0x00000000d9100000, 0x00000000d9100000, 0x00000000d9200000|  0%| F|  |TAMS 0x00000000d9100000| PB 0x00000000d9100000| Untracked 
|1426|0x00000000d9200000, 0x00000000d9200000, 0x00000000d9300000|  0%| F|  |TAMS 0x00000000d9200000| PB 0x00000000d9200000| Untracked 
|1427|0x00000000d9300000, 0x00000000d9300000, 0x00000000d9400000|  0%| F|  |TAMS 0x00000000d9300000| PB 0x00000000d9300000| Untracked 
|1428|0x00000000d9400000, 0x00000000d9400000, 0x00000000d9500000|  0%| F|  |TAMS 0x00000000d9400000| PB 0x00000000d9400000| Untracked 
|1429|0x00000000d9500000, 0x00000000d9500000, 0x00000000d9600000|  0%| F|  |TAMS 0x00000000d9500000| PB 0x00000000d9500000| Untracked 
|1430|0x00000000d9600000, 0x00000000d9600000, 0x00000000d9700000|  0%| F|  |TAMS 0x00000000d9600000| PB 0x00000000d9600000| Untracked 
|1431|0x00000000d9700000, 0x00000000d9700000, 0x00000000d9800000|  0%| F|  |TAMS 0x00000000d9700000| PB 0x00000000d9700000| Untracked 
|1432|0x00000000d9800000, 0x00000000d9800000, 0x00000000d9900000|  0%| F|  |TAMS 0x00000000d9800000| PB 0x00000000d9800000| Untracked 
|1433|0x00000000d9900000, 0x00000000d9900000, 0x00000000d9a00000|  0%| F|  |TAMS 0x00000000d9900000| PB 0x00000000d9900000| Untracked 
|1434|0x00000000d9a00000, 0x00000000d9a00000, 0x00000000d9b00000|  0%| F|  |TAMS 0x00000000d9a00000| PB 0x00000000d9a00000| Untracked 
|1435|0x00000000d9b00000, 0x00000000d9b00000, 0x00000000d9c00000|  0%| F|  |TAMS 0x00000000d9b00000| PB 0x00000000d9b00000| Untracked 
|1436|0x00000000d9c00000, 0x00000000d9c00000, 0x00000000d9d00000|  0%| F|  |TAMS 0x00000000d9c00000| PB 0x00000000d9c00000| Untracked 
|1437|0x00000000d9d00000, 0x00000000d9d00000, 0x00000000d9e00000|  0%| F|  |TAMS 0x00000000d9d00000| PB 0x00000000d9d00000| Untracked 
|1438|0x00000000d9e00000, 0x00000000d9e00000, 0x00000000d9f00000|  0%| F|  |TAMS 0x00000000d9e00000| PB 0x00000000d9e00000| Untracked 
|1439|0x00000000d9f00000, 0x00000000d9f00000, 0x00000000da000000|  0%| F|  |TAMS 0x00000000d9f00000| PB 0x00000000d9f00000| Untracked 
|1440|0x00000000da000000, 0x00000000da000000, 0x00000000da100000|  0%| F|  |TAMS 0x00000000da000000| PB 0x00000000da000000| Untracked 
|1441|0x00000000da100000, 0x00000000da100000, 0x00000000da200000|  0%| F|  |TAMS 0x00000000da100000| PB 0x00000000da100000| Untracked 
|1442|0x00000000da200000, 0x00000000da200000, 0x00000000da300000|  0%| F|  |TAMS 0x00000000da200000| PB 0x00000000da200000| Untracked 
|1443|0x00000000da300000, 0x00000000da300000, 0x00000000da400000|  0%| F|  |TAMS 0x00000000da300000| PB 0x00000000da300000| Untracked 
|1444|0x00000000da400000, 0x00000000da400000, 0x00000000da500000|  0%| F|  |TAMS 0x00000000da400000| PB 0x00000000da400000| Untracked 
|1445|0x00000000da500000, 0x00000000da500000, 0x00000000da600000|  0%| F|  |TAMS 0x00000000da500000| PB 0x00000000da500000| Untracked 
|1446|0x00000000da600000, 0x00000000da600000, 0x00000000da700000|  0%| F|  |TAMS 0x00000000da600000| PB 0x00000000da600000| Untracked 
|1447|0x00000000da700000, 0x00000000da700000, 0x00000000da800000|  0%| F|  |TAMS 0x00000000da700000| PB 0x00000000da700000| Untracked 
|1448|0x00000000da800000, 0x00000000da800000, 0x00000000da900000|  0%| F|  |TAMS 0x00000000da800000| PB 0x00000000da800000| Untracked 
|1449|0x00000000da900000, 0x00000000da900000, 0x00000000daa00000|  0%| F|  |TAMS 0x00000000da900000| PB 0x00000000da900000| Untracked 
|1450|0x00000000daa00000, 0x00000000daa00000, 0x00000000dab00000|  0%| F|  |TAMS 0x00000000daa00000| PB 0x00000000daa00000| Untracked 
|1451|0x00000000dab00000, 0x00000000dab00000, 0x00000000dac00000|  0%| F|  |TAMS 0x00000000dab00000| PB 0x00000000dab00000| Untracked 
|1452|0x00000000dac00000, 0x00000000dac00000, 0x00000000dad00000|  0%| F|  |TAMS 0x00000000dac00000| PB 0x00000000dac00000| Untracked 
|1453|0x00000000dad00000, 0x00000000dad00000, 0x00000000dae00000|  0%| F|  |TAMS 0x00000000dad00000| PB 0x00000000dad00000| Untracked 
|1454|0x00000000dae00000, 0x00000000dae00000, 0x00000000daf00000|  0%| F|  |TAMS 0x00000000dae00000| PB 0x00000000dae00000| Untracked 
|1455|0x00000000daf00000, 0x00000000daf00000, 0x00000000db000000|  0%| F|  |TAMS 0x00000000daf00000| PB 0x00000000daf00000| Untracked 
|1456|0x00000000db000000, 0x00000000db000000, 0x00000000db100000|  0%| F|  |TAMS 0x00000000db000000| PB 0x00000000db000000| Untracked 
|1457|0x00000000db100000, 0x00000000db100000, 0x00000000db200000|  0%| F|  |TAMS 0x00000000db100000| PB 0x00000000db100000| Untracked 
|1458|0x00000000db200000, 0x00000000db200000, 0x00000000db300000|  0%| F|  |TAMS 0x00000000db200000| PB 0x00000000db200000| Untracked 
|1459|0x00000000db300000, 0x00000000db300000, 0x00000000db400000|  0%| F|  |TAMS 0x00000000db300000| PB 0x00000000db300000| Untracked 
|1460|0x00000000db400000, 0x00000000db400000, 0x00000000db500000|  0%| F|  |TAMS 0x00000000db400000| PB 0x00000000db400000| Untracked 
|1461|0x00000000db500000, 0x00000000db500000, 0x00000000db600000|  0%| F|  |TAMS 0x00000000db500000| PB 0x00000000db500000| Untracked 
|1462|0x00000000db600000, 0x00000000db600000, 0x00000000db700000|  0%| F|  |TAMS 0x00000000db600000| PB 0x00000000db600000| Untracked 
|1463|0x00000000db700000, 0x00000000db700000, 0x00000000db800000|  0%| F|  |TAMS 0x00000000db700000| PB 0x00000000db700000| Untracked 
|1464|0x00000000db800000, 0x00000000db800000, 0x00000000db900000|  0%| F|  |TAMS 0x00000000db800000| PB 0x00000000db800000| Untracked 
|1465|0x00000000db900000, 0x00000000db900000, 0x00000000dba00000|  0%| F|  |TAMS 0x00000000db900000| PB 0x00000000db900000| Untracked 
|1466|0x00000000dba00000, 0x00000000dba00000, 0x00000000dbb00000|  0%| F|  |TAMS 0x00000000dba00000| PB 0x00000000dba00000| Untracked 
|1467|0x00000000dbb00000, 0x00000000dbb00000, 0x00000000dbc00000|  0%| F|  |TAMS 0x00000000dbb00000| PB 0x00000000dbb00000| Untracked 
|1468|0x00000000dbc00000, 0x00000000dbc00000, 0x00000000dbd00000|  0%| F|  |TAMS 0x00000000dbc00000| PB 0x00000000dbc00000| Untracked 
|1469|0x00000000dbd00000, 0x00000000dbd00000, 0x00000000dbe00000|  0%| F|  |TAMS 0x00000000dbd00000| PB 0x00000000dbd00000| Untracked 
|1470|0x00000000dbe00000, 0x00000000dbe00000, 0x00000000dbf00000|  0%| F|  |TAMS 0x00000000dbe00000| PB 0x00000000dbe00000| Untracked 
|1471|0x00000000dbf00000, 0x00000000dbf00000, 0x00000000dc000000|  0%| F|  |TAMS 0x00000000dbf00000| PB 0x00000000dbf00000| Untracked 
|1472|0x00000000dc000000, 0x00000000dc000000, 0x00000000dc100000|  0%| F|  |TAMS 0x00000000dc000000| PB 0x00000000dc000000| Untracked 
|1473|0x00000000dc100000, 0x00000000dc100000, 0x00000000dc200000|  0%| F|  |TAMS 0x00000000dc100000| PB 0x00000000dc100000| Untracked 
|1474|0x00000000dc200000, 0x00000000dc275758, 0x00000000dc300000| 45%| E|  |TAMS 0x00000000dc200000| PB 0x00000000dc200000| Complete 
|1475|0x00000000dc300000, 0x00000000dc400000, 0x00000000dc400000|100%| E|CS|TAMS 0x00000000dc300000| PB 0x00000000dc300000| Complete 
|1476|0x00000000dc400000, 0x00000000dc500000, 0x00000000dc500000|100%| E|CS|TAMS 0x00000000dc400000| PB 0x00000000dc400000| Complete 
|1477|0x00000000dc500000, 0x00000000dc600000, 0x00000000dc600000|100%| E|CS|TAMS 0x00000000dc500000| PB 0x00000000dc500000| Complete 
|1478|0x00000000dc600000, 0x00000000dc700000, 0x00000000dc700000|100%| E|CS|TAMS 0x00000000dc600000| PB 0x00000000dc600000| Complete 
|1479|0x00000000dc700000, 0x00000000dc800000, 0x00000000dc800000|100%| E|CS|TAMS 0x00000000dc700000| PB 0x00000000dc700000| Complete 
|1480|0x00000000dc800000, 0x00000000dc900000, 0x00000000dc900000|100%| E|CS|TAMS 0x00000000dc800000| PB 0x00000000dc800000| Complete 
|1481|0x00000000dc900000, 0x00000000dca00000, 0x00000000dca00000|100%| E|CS|TAMS 0x00000000dc900000| PB 0x00000000dc900000| Complete 
|1482|0x00000000dca00000, 0x00000000dcb00000, 0x00000000dcb00000|100%| E|CS|TAMS 0x00000000dca00000| PB 0x00000000dca00000| Complete 
|1483|0x00000000dcb00000, 0x00000000dcc00000, 0x00000000dcc00000|100%| E|CS|TAMS 0x00000000dcb00000| PB 0x00000000dcb00000| Complete 
|1484|0x00000000dcc00000, 0x00000000dcd00000, 0x00000000dcd00000|100%| E|CS|TAMS 0x00000000dcc00000| PB 0x00000000dcc00000| Complete 
|1485|0x00000000dcd00000, 0x00000000dce00000, 0x00000000dce00000|100%| E|CS|TAMS 0x00000000dcd00000| PB 0x00000000dcd00000| Complete 
|1486|0x00000000dce00000, 0x00000000dcf00000, 0x00000000dcf00000|100%| E|CS|TAMS 0x00000000dce00000| PB 0x00000000dce00000| Complete 
|1487|0x00000000dcf00000, 0x00000000dd000000, 0x00000000dd000000|100%| E|CS|TAMS 0x00000000dcf00000| PB 0x00000000dcf00000| Complete 
|1488|0x00000000dd000000, 0x00000000dd100000, 0x00000000dd100000|100%| E|CS|TAMS 0x00000000dd000000| PB 0x00000000dd000000| Complete 
|1489|0x00000000dd100000, 0x00000000dd200000, 0x00000000dd200000|100%| E|CS|TAMS 0x00000000dd100000| PB 0x00000000dd100000| Complete 
|1490|0x00000000dd200000, 0x00000000dd300000, 0x00000000dd300000|100%| E|CS|TAMS 0x00000000dd200000| PB 0x00000000dd200000| Complete 
|1491|0x00000000dd300000, 0x00000000dd400000, 0x00000000dd400000|100%| E|CS|TAMS 0x00000000dd300000| PB 0x00000000dd300000| Complete 
|1492|0x00000000dd400000, 0x00000000dd500000, 0x00000000dd500000|100%| E|CS|TAMS 0x00000000dd400000| PB 0x00000000dd400000| Complete 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x000001716cc40000,0x000001716d040000] _byte_map_base: 0x000001716c840000

Marking Bits: (CMBitMap*) 0x00000171585be530
 Bits: [0x000001716d040000, 0x000001716f040000)

Polling page: 0x0000017159eb0000

Metaspace:

Usage:
  Non-class:    163.96 MB used.
      Class:     23.21 MB used.
       Both:    187.17 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     167.00 MB ( 87%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      24.94 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     191.94 MB ( 16%) committed. 

Chunk freelists:
   Non-Class:  11.35 MB
       Class:  7.89 MB
        Both:  19.24 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 320.44 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 9308.
num_arena_deaths: 6624.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 3187.
num_space_uncommitted: 78.
num_chunks_returned_to_freelist: 9442.
num_chunks_taken_from_freelist: 20720.
num_chunk_merges: 2801.
num_chunk_splits: 10971.
num_chunks_enlarged: 6493.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=31179Kb max_used=37296Kb free=88821Kb
 bounds [0x0000017165310000, 0x0000017167780000, 0x000001716c840000]
CodeHeap 'profiled nmethods': size=120000Kb used=30071Kb max_used=54877Kb free=89929Kb
 bounds [0x000001715d840000, 0x0000017160de0000, 0x0000017164d70000]
CodeHeap 'non-nmethods': size=5760Kb used=3147Kb max_used=3235Kb free=2613Kb
 bounds [0x0000017164d70000, 0x00000171650b0000, 0x0000017165310000]
 total_blobs=20914 nmethods=19736 adapters=1078
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 21324.803 Thread 0x0000017171734490 101365       1       org.antlr.v4.runtime.atn.LexerCustomAction::isPositionDependent (2 bytes)
Event: 21324.803 Thread 0x0000017171734490 nmethod 101365 0x0000017166513810 code [0x00000171665139a0, 0x0000017166513a68]
Event: 21324.803 Thread 0x0000017171734490 101371       1       org.antlr.v4.runtime.atn.LexerPushModeAction::isPositionDependent (2 bytes)
Event: 21324.804 Thread 0x0000017171734490 nmethod 101371 0x0000017166513510 code [0x00000171665136a0, 0x0000017166513768]
Event: 21324.804 Thread 0x0000017171734490 101372       1       org.antlr.v4.runtime.atn.LexerPopModeAction::isPositionDependent (2 bytes)
Event: 21324.804 Thread 0x0000017171734490 nmethod 101372 0x0000017166513210 code [0x00000171665133a0, 0x0000017166513468]
Event: 21324.804 Thread 0x0000017171734490 101373       1       org.antlr.v4.runtime.atn.LexerModeAction::isPositionDependent (2 bytes)
Event: 21324.805 Thread 0x0000017171734490 nmethod 101373 0x0000017166512f10 code [0x00000171665130a0, 0x0000017166513168]
Event: 21324.805 Thread 0x0000017171734490 101381       3       org.tomlj.TomlPosition::<init> (7 bytes)
Event: 21324.806 Thread 0x0000017171734490 nmethod 101381 0x000001715dc33810 code [0x000001715dc339c0, 0x000001715dc33cc0]
Event: 21324.816 Thread 0x0000017171734490 101391       3       org.tomlj.LineVisitor::defaultResult (5 bytes)
Event: 21324.817 Thread 0x0000017171734490 nmethod 101391 0x000001715dc33490 code [0x000001715dc33620, 0x000001715dc33740]
Event: 21324.817 Thread 0x0000017171734490 101392       3       org.tomlj.InlineTableVisitor::aggregateResult (13 bytes)
Event: 21324.817 Thread 0x0000017171734490 nmethod 101392 0x000001715dc32e90 code [0x000001715dc33040, 0x000001715dc33370]
Event: 21324.818 Thread 0x0000017171734490 101394       1       org.tomlj.InlineTableVisitor::defaultResult (5 bytes)
Event: 21324.818 Thread 0x0000017171734490 nmethod 101394 0x0000017166512c10 code [0x0000017166512da0, 0x0000017166512e68]
Event: 21324.864 Thread 0x0000017171734490 101395       3       org.tomlj.TomlTable::getString (16 bytes)
Event: 21324.864 Thread 0x0000017171734490 nmethod 101395 0x000001715dc32810 code [0x000001715dc329e0, 0x000001715dc32d40]
Event: 21324.892 Thread 0x0000017171734490 101396       3       org.tomlj.TomlTable::getString (68 bytes)
Event: 21324.895 Thread 0x0000017171734490 nmethod 101396 0x000001715dc31710 code [0x000001715dc319c0, 0x000001715dc32440]

GC Heap History (20 events):
Event: 15530.245 GC heap before
{Heap before GC invocations=289 (full 2):
 garbage-first heap   total 1336320K, used 1092737K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 258 young (264192K), 18 survivors (18432K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15530.356 GC heap after
{Heap after GC invocations=290 (full 2):
 garbage-first heap   total 1336320K, used 847745K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 33 young (33792K), 33 survivors (33792K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15531.774 GC heap before
{Heap before GC invocations=290 (full 2):
 garbage-first heap   total 1336320K, used 959361K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 144 young (147456K), 33 survivors (33792K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15531.890 GC heap after
{Heap after GC invocations=291 (full 2):
 garbage-first heap   total 1336320K, used 863617K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15534.796 GC heap before
{Heap before GC invocations=291 (full 2):
 garbage-first heap   total 1515520K, used 1217921K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 320 young (327680K), 16 survivors (16384K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15534.907 GC heap after
{Heap after GC invocations=292 (full 2):
 garbage-first heap   total 1515520K, used 956033K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 40 young (40960K), 40 survivors (40960K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15537.408 GC heap before
{Heap before GC invocations=293 (full 2):
 garbage-first heap   total 1515520K, used 1370753K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 335 young (343040K), 40 survivors (40960K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15537.513 GC heap after
{Heap after GC invocations=294 (full 2):
 garbage-first heap   total 1515520K, used 1076481K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 27 young (27648K), 27 survivors (27648K)
 Metaspace       used 191313K, committed 196544K, reserved 1245184K
  class space    used 23738K, committed 25536K, reserved 1048576K
}
Event: 15546.919 GC heap before
{Heap before GC invocations=294 (full 2):
 garbage-first heap   total 1515520K, used 1447169K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 318 young (325632K), 27 survivors (27648K)
 Metaspace       used 191354K, committed 196544K, reserved 1245184K
  class space    used 23744K, committed 25536K, reserved 1048576K
}
Event: 15546.981 GC heap after
{Heap after GC invocations=295 (full 2):
 garbage-first heap   total 1515520K, used 981633K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 191354K, committed 196544K, reserved 1245184K
  class space    used 23744K, committed 25536K, reserved 1048576K
}
Event: 15550.130 GC heap before
{Heap before GC invocations=295 (full 2):
 garbage-first heap   total 1515520K, used 1343105K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 360 young (368640K), 7 survivors (7168K)
 Metaspace       used 191376K, committed 196544K, reserved 1245184K
  class space    used 23745K, committed 25536K, reserved 1048576K
}
Event: 15550.146 GC heap after
{Heap after GC invocations=296 (full 2):
 garbage-first heap   total 1515520K, used 981175K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 191376K, committed 196544K, reserved 1245184K
  class space    used 23745K, committed 25536K, reserved 1048576K
}
Event: 15560.353 GC heap before
{Heap before GC invocations=296 (full 2):
 garbage-first heap   total 1515520K, used 1242295K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 264 young (270336K), 7 survivors (7168K)
 Metaspace       used 191387K, committed 196544K, reserved 1245184K
  class space    used 23746K, committed 25536K, reserved 1048576K
}
Event: 15560.363 GC heap after
{Heap after GC invocations=297 (full 2):
 garbage-first heap   total 1515520K, used 979235K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 191387K, committed 196544K, reserved 1245184K
  class space    used 23746K, committed 25536K, reserved 1048576K
}
Event: 15568.479 GC heap before
{Heap before GC invocations=298 (full 2):
 garbage-first heap   total 1515520K, used 1092899K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 395 young (404480K), 5 survivors (5120K)
 Metaspace       used 191440K, committed 196544K, reserved 1245184K
  class space    used 23746K, committed 25536K, reserved 1048576K
}
Event: 15568.534 GC heap after
{Heap after GC invocations=299 (full 2):
 garbage-first heap   total 1515520K, used 729217K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 43 young (44032K), 43 survivors (44032K)
 Metaspace       used 191440K, committed 196544K, reserved 1245184K
  class space    used 23746K, committed 25536K, reserved 1048576K
}
Event: 20990.967 GC heap before
{Heap before GC invocations=299 (full 2):
 garbage-first heap   total 1515520K, used 1510529K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 581 young (594944K), 43 survivors (44032K)
 Metaspace       used 191640K, committed 196544K, reserved 1245184K
  class space    used 23769K, committed 25536K, reserved 1048576K
}
Event: 20991.617 GC heap after
{Heap after GC invocations=300 (full 2):
 garbage-first heap   total 1536000K, used 663410K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 191640K, committed 196544K, reserved 1245184K
  class space    used 23769K, committed 25536K, reserved 1048576K
}
Event: 20997.201 GC heap before
{Heap before GC invocations=300 (full 2):
 garbage-first heap   total 1536000K, used 940914K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 19 survivors (19456K)
 Metaspace       used 191650K, committed 196544K, reserved 1245184K
  class space    used 23769K, committed 25536K, reserved 1048576K
}
Event: 20997.457 GC heap after
{Heap after GC invocations=301 (full 2):
 garbage-first heap   total 1536000K, used 619277K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 191650K, committed 196544K, reserved 1245184K
  class space    used 23769K, committed 25536K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.013 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.018 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.630 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 20980.264 Thread 0x000001717fc77080 DEOPT PACKING pc=0x000001715d943e3e sp=0x0000006284cfb060
Event: 20980.264 Thread 0x000001717fc77080 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006284cfa560 mode 0
Event: 20980.763 Thread 0x000001717fc77080 DEOPT PACKING pc=0x000001715db5cea7 sp=0x0000006284cfb6f0
Event: 20980.765 Thread 0x000001717fc77080 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006284cfac20 mode 0
Event: 20980.899 Thread 0x000001717fc77080 DEOPT PACKING pc=0x000001715db5cea7 sp=0x0000006284cfb6f0
Event: 20980.901 Thread 0x000001717fc77080 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006284cfac20 mode 0
Event: 20991.921 Thread 0x000001717fc71b30 DEOPT PACKING pc=0x000001715db69f40 sp=0x0000006285cfd970
Event: 20991.921 Thread 0x000001717fc71b30 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006285cfcf08 mode 0
Event: 20992.162 Thread 0x000001717fc71b30 DEOPT PACKING pc=0x000001715db69f40 sp=0x0000006285cfd970
Event: 20992.162 Thread 0x000001717fc71b30 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006285cfcf08 mode 0
Event: 20992.600 Thread 0x000001717fc71b30 DEOPT PACKING pc=0x000001715dcbf5fd sp=0x0000006285cfd7c0
Event: 20992.600 Thread 0x000001717fc71b30 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006285cfccd0 mode 0
Event: 20992.623 Thread 0x000001717fc71b30 DEOPT PACKING pc=0x000001715dcbf5fd sp=0x0000006285cfd7c0
Event: 20992.623 Thread 0x000001717fc71b30 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x0000006285cfccd0 mode 0
Event: 20993.943 Thread 0x000001717fc77080 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000017166a07da0 relative=0x00000000000007e0
Event: 20993.943 Thread 0x000001717fc77080 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000017166a07da0 method=org.gradle.internal.file.FilePathUtil.sizeOfCommonPrefix(Ljava/lang/String;Ljava/lang/String;IC)I @ 97 c2
Event: 20993.943 Thread 0x000001717fc77080 DEOPT PACKING pc=0x0000017166a07da0 sp=0x0000006284cfbc20
Event: 20993.943 Thread 0x000001717fc77080 DEOPT UNPACKING pc=0x0000017164dc46a2 sp=0x0000006284cfbac8 mode 2
Event: 20994.213 Thread 0x000001717fc75640 DEOPT PACKING pc=0x000001715ed6dda7 sp=0x00000062853fe160
Event: 20994.213 Thread 0x000001717fc75640 DEOPT UNPACKING pc=0x0000017164dc4e42 sp=0x00000062853fd5f8 mode 0

Classes loaded (20 events):
Event: 10053.195 Loading class sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeySpec
Event: 10053.195 Loading class sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeySpec done
Event: 10136.161 Loading class sun/security/ssl/Alert
Event: 10136.162 Loading class sun/security/ssl/Alert done
Event: 10136.163 Loading class sun/security/ssl/Alert$AlertConsumer
Event: 10136.163 Loading class sun/security/ssl/Alert$AlertConsumer done
Event: 10136.163 Loading class sun/security/ssl/Alert$Level
Event: 10136.163 Loading class sun/security/ssl/Alert$Level done
Event: 10153.705 Loading class java/io/BufferedReader$1
Event: 10153.706 Loading class java/io/BufferedReader$1 done
Event: 10154.308 Loading class java/util/zip/ZipFile$EntrySpliterator
Event: 10154.308 Loading class java/util/Spliterators$AbstractSpliterator
Event: 10154.309 Loading class java/util/Spliterators$AbstractSpliterator done
Event: 10154.309 Loading class java/util/zip/ZipFile$EntrySpliterator done
Event: 11268.884 Loading class java/lang/Throwable$WrappedPrintWriter
Event: 11268.886 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 11268.888 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 11268.888 Loading class java/lang/Throwable$WrappedPrintWriter done
Event: 11269.438 Loading class java/util/Collections$EmptyListIterator
Event: 11269.440 Loading class java/util/Collections$EmptyListIterator done

Classes unloaded (20 events):
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013c0400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor752'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013c0000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor751'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013cc400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor750'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013cc000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor749'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x0000000101204400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor748'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x0000000101204000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor747'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001008bfc00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor746'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001008bf800 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor745'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x0000000101080c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor744'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x0000000101080800 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor743'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001011f4400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor742'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001011f4000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor741'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013a9c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor740'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001013aa000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor739'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001015d0c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor738'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001015d0000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor737'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x000000010082b400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor706'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x00000001017f3000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor663'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x000000010113c000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor627'
Event: 15498.663 Thread 0x000001717156dff0 Unloading class 0x0000000101311400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor507'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 20997.671 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd2012a0}> (0x00000000dd2012a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.673 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd2083a0}> (0x00000000dd2083a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.673 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd209150}> (0x00000000dd209150) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.673 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd20a4e0}> (0x00000000dd20a4e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.675 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd216718}> (0x00000000dd216718) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.676 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd2174b0}> (0x00000000dd2174b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.676 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd218810}> (0x00000000dd218810) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.677 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd21de18}> (0x00000000dd21de18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.677 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd21ebc8}> (0x00000000dd21ebc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.678 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd21ff58}> (0x00000000dd21ff58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.681 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd22e830}> (0x00000000dd22e830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.681 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd2314a0}> (0x00000000dd2314a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 20997.681 Thread 0x000001710afa57c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dd234688}> (0x00000000dd234688) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21323.474 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc3786f0}> (0x00000000dc3786f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21323.482 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc379a58}> (0x00000000dc379a58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21323.482 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc37adb8}> (0x00000000dc37adb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21323.483 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc37be10}> (0x00000000dc37be10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21324.633 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc3bfb70}> (0x00000000dc3bfb70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21324.639 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc3c1100}> (0x00000000dc3c1100) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 21324.670 Thread 0x0000017109b75880 Exception <a 'sun/nio/fs/WindowsException'{0x00000000dc3c45f8}> (0x00000000dc3c45f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 20996.684 Executing VM operation: Cleanup
Event: 20996.685 Executing VM operation: Cleanup done
Event: 20997.201 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 20997.457 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 20998.522 Executing VM operation: Cleanup
Event: 20998.522 Executing VM operation: Cleanup done
Event: 21008.574 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 21008.574 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 21008.574 Executing VM operation: RendezvousGCThreads
Event: 21008.574 Executing VM operation: RendezvousGCThreads done
Event: 21055.601 Executing VM operation: Cleanup
Event: 21055.601 Executing VM operation: Cleanup done
Event: 21313.203 Executing VM operation: Cleanup
Event: 21313.203 Executing VM operation: Cleanup done
Event: 21321.226 Executing VM operation: Cleanup
Event: 21321.228 Executing VM operation: Cleanup done
Event: 21322.229 Executing VM operation: Cleanup
Event: 21322.229 Executing VM operation: Cleanup done
Event: 21323.230 Executing VM operation: Cleanup
Event: 21323.230 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171607ebf90
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171607ee690
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171607eea90
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171607f2910
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171607f2d10
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160888110
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x000001716088d790
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608cd990
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608cdd10
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608ce310
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608cea10
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608d0910
Event: 15561.446 Thread 0x000001717156dff0 flushing  nmethod 0x00000171608d2510
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x000001716097cf10
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160adb810
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160bfaf10
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160cba390
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160d08c10
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160d13f90
Event: 15561.447 Thread 0x000001717156dff0 flushing  nmethod 0x0000017160d7e490

Events (20 events):
Event: 21056.605 Thread 0x000001717aa88ed0 Thread exited: 0x000001717aa88ed0
Event: 21057.500 Thread 0x000001717aa846a0 Thread exited: 0x000001717aa846a0
Event: 21057.501 Thread 0x000001717fc69e80 Thread exited: 0x000001717fc69e80
Event: 21057.690 Thread 0x000001710afa57c0 Thread exited: 0x000001710afa57c0
Event: 21066.442 Thread 0x000001717f942680 Thread added: 0x0000017109b71d70
Event: 21075.924 Thread 0x000001717fc79150 Thread exited: 0x000001717fc79150
Event: 21095.939 Thread 0x000001710afac0c0 Thread exited: 0x000001710afac0c0
Event: 21141.575 Thread 0x0000017109b71d70 Thread exited: 0x0000017109b71d70
Event: 21320.517 Thread 0x0000017177cab9d0 Thread added: 0x000001710afa4410
Event: 21320.550 Thread 0x000001710afa4410 Thread added: 0x000001710afa85b0
Event: 21320.651 Thread 0x000001710afa4410 Thread added: 0x0000017109b751f0
Event: 21320.710 Thread 0x000001710afa4410 Thread added: 0x0000017109b75880
Event: 21320.797 Thread 0x0000017109b75880 Thread added: 0x0000017109b73120
Event: 21320.847 Thread 0x0000017109b75880 Thread added: 0x0000017109b772c0
Event: 21320.855 Thread 0x0000017109b75880 Thread added: 0x0000017109b75f10
Event: 21322.233 Thread 0x0000017109b75880 Thread added: 0x0000017109b765a0
Event: 21322.257 Thread 0x0000017109b75880 Thread added: 0x000001710afa3060
Event: 21323.191 Thread 0x0000017109b75880 Thread added: 0x000001717a7e00f0
Event: 21323.297 Thread 0x0000017109b75880 Thread added: 0x000001717a7de020
Event: 21323.329 Thread 0x0000017109b75880 Thread added: 0x000001717a7e0780


Dynamic libraries:
0x00007ff6a49b0000 - 0x00007ff6a49ba000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffb70e70000 - 0x00007ffb71087000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffb701f0000 - 0x00007ffb702b4000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffb6e120000 - 0x00007ffb6e4f0000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffb6ea40000 - 0x00007ffb6eb51000 	C:\Windows\System32\ucrtbase.dll
0x00007ffb6aac0000 - 0x00007ffb6aad8000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffb4ebe0000 - 0x00007ffb4ebfb000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffb6f0e0000 - 0x00007ffb6f28e000 	C:\Windows\System32\USER32.dll
0x00007ffb6e820000 - 0x00007ffb6e846000 	C:\Windows\System32\win32u.dll
0x00007ffb5b0c0000 - 0x00007ffb5b352000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffb6efd0000 - 0x00007ffb6eff9000 	C:\Windows\System32\GDI32.dll
0x00007ffb6f530000 - 0x00007ffb6f5d7000 	C:\Windows\System32\msvcrt.dll
0x00007ffb6e4f0000 - 0x00007ffb6e60b000 	C:\Windows\System32\gdi32full.dll
0x00007ffb6e6c0000 - 0x00007ffb6e75a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffb70de0000 - 0x00007ffb70e11000 	C:\Windows\System32\IMM32.DLL
0x00007ffb69d70000 - 0x00007ffb69d7c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffb6aa30000 - 0x00007ffb6aabd000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffb00140000 - 0x00007ffb00dca000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffb70b60000 - 0x00007ffb70c11000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffb70140000 - 0x00007ffb701e7000 	C:\Windows\System32\sechost.dll
0x00007ffb6e690000 - 0x00007ffb6e6b8000 	C:\Windows\System32\bcrypt.dll
0x00007ffb6f5e0000 - 0x00007ffb6f6f4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffb6f2a0000 - 0x00007ffb6f311000 	C:\Windows\System32\WS2_32.dll
0x00007ffb6dff0000 - 0x00007ffb6e03d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffb6a2e0000 - 0x00007ffb6a314000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffb6b280000 - 0x00007ffb6b28a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffb6dfd0000 - 0x00007ffb6dfe3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffb6d160000 - 0x00007ffb6d178000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffb54ac0000 - 0x00007ffb54aca000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffb5a320000 - 0x00007ffb5a552000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffb6ebe0000 - 0x00007ffb6ef70000 	C:\Windows\System32\combase.dll
0x00007ffb6f000000 - 0x00007ffb6f0d7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffb58840000 - 0x00007ffb58872000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffb6e9c0000 - 0x00007ffb6ea3b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffb6aa20000 - 0x00007ffb6aa2e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffb4ebc0000 - 0x00007ffb4ebe0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffb4eba0000 - 0x00007ffb4ebb8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffb6f700000 - 0x00007ffb6ff78000 	C:\Windows\System32\SHELL32.dll
0x00007ffb6c070000 - 0x00007ffb6c980000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffb6bf30000 - 0x00007ffb6c06f000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffb6f420000 - 0x00007ffb6f52a000 	C:\Windows\System32\SHCORE.dll
0x00007ffb6f3c0000 - 0x00007ffb6f41e000 	C:\Windows\System32\shlwapi.dll
0x00007ffb6e050000 - 0x00007ffb6e07b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffb4f7e0000 - 0x00007ffb4f7f0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffb6a100000 - 0x00007ffb6a22c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffb6d5e0000 - 0x00007ffb6d64a000 	C:\Windows\system32\mswsock.dll
0x00007ffb4eb80000 - 0x00007ffb4eb96000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffb3e7c0000 - 0x00007ffb3e7d0000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffb6aae0000 - 0x00007ffb6ab07000 	C:\GradleHome\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffb6a360000 - 0x00007ffb6a4a4000 	C:\GradleHome\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffb4c800000 - 0x00007ffb4c809000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffb4c7f0000 - 0x00007ffb4c7fb000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffb70e20000 - 0x00007ffb70e28000 	C:\Windows\System32\PSAPI.DLL
0x00007ffb6d840000 - 0x00007ffb6d85b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffb6d0c0000 - 0x00007ffb6d0f5000 	C:\Windows\system32\rsaenh.dll
0x00007ffb6d6d0000 - 0x00007ffb6d6f8000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffb6d830000 - 0x00007ffb6d83c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffb6cbe0000 - 0x00007ffb6cc0d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffb6f290000 - 0x00007ffb6f299000 	C:\Windows\System32\NSI.dll
0x00007ffb4eb20000 - 0x00007ffb4eb29000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffb5b770000 - 0x00007ffb5b778000 	C:\Windows\system32\wshunix.dll
0x00007ffb276a0000 - 0x00007ffb276b7000 	C:\Windows\system32\napinsp.dll
0x00007ffb276c0000 - 0x00007ffb276db000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffb6cc50000 - 0x00007ffb6cd52000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffb276e0000 - 0x00007ffb276f1000 	C:\Windows\System32\winrnr.dll
0x00007ffb5e5e0000 - 0x00007ffb5e5f5000 	C:\Windows\system32\wshbth.dll
0x00007ffb27700000 - 0x00007ffb27727000 	C:\Windows\system32\nlansp_c.dll
0x000000006fb60000 - 0x000000006fb86000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ffb60840000 - 0x00007ffb6084a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffb67290000 - 0x00007ffb67313000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffb6d270000 - 0x00007ffb6d2a4000 	C:\Windows\SYSTEM32\ntmarta.dll
0x00007ffb6ac60000 - 0x00007ffb6ac7e000 	C:\Users\<USER>\AppData\Local\Temp\native-platform16519463148551441964dir\native-platform.dll
0x00007ffb6ac50000 - 0x00007ffb6ac57000 	C:\Program Files\Android\Android Studio\jbr\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\GradleHome\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\GradleHome\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64;C:\Program Files\Bonjour;C:\Users\<USER>\AppData\Local\Temp\native-platform16519463148551441964dir

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\GradleHome\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2
java_class_path (initial): C:\GradleHome\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\gradle-daemon-main-8.10.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Amazon Corretto\jdk11.0.19_7
PATH=C:\Program Files (x86)\Intel\iCLS Client\;C:\Program Files\Intel\iCLS Client\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=Mac
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 61 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 29, weak refs: 8

JNI global refs memory usage: 835, weak refs: 1025

Process memory usage:
Resident Set Size: 141444K (1% of 8288700K total physical memory with 301416K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 73090K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 55351K
Loader bootstrap                                                                       : 35920K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 16515K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 8738K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 802K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 705K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 290K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 234K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 21472B
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class com.google.common.collect.Iterables                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 3 times (x 204B)
Class com.google.common.collect.ForwardingObject                                      : loaded 3 times (x 68B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 3 times (x 202B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 3 times (x 146B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 3 times (x 81B)
Class com.google.common.collect.SetMultimap                                           : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 3 times (x 147B)
Class com.google.common.collect.RangeSet                                              : loaded 3 times (x 66B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$1                                            : loaded 3 times (x 109B)
Class com.google.common.base.Joiner$2                                                 : loaded 3 times (x 75B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher                                              : loaded 3 times (x 107B)
Class com.google.common.collect.PeekingIterator                                       : loaded 3 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 3 times (x 76B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 3 times (x 176B)
Class com.google.common.base.Preconditions                                            : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 3 times (x 109B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 3 times (x 73B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 3 times (x 203B)
Class org.gradle.internal.IoActions                                                   : loaded 3 times (x 67B)
Class org.gradle.api.GradleException                                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 3 times (x 78B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 3 times (x 65B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 3 times (x 109B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 3 times (x 110B)
Class com.google.common.base.Predicate                                                : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap                                          : loaded 3 times (x 116B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 3 times (x 107B)
Class org.gradle.internal.Cast                                                        : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 3 times (x 93B)
Class org.gradle.api.Action                                                           : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 3 times (x 69B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableSet                                          : loaded 3 times (x 141B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 3 times (x 73B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 3 times (x 65B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 3 times (x 208B)
Class com.google.common.base.Function                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 3 times (x 81B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 3 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 3 times (x 121B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 3 times (x 143B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 3 times (x 81B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 3 times (x 295B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 3 times (x 295B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 3 times (x 144B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 3 times (x 205B)
Class com.google.common.collect.Iterators$9                                           : loaded 3 times (x 77B)
Class com.google.common.collect.Lists                                                 : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 3 times (x 143B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 3 times (x 91B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 3 times (x 121B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 3 times (x 108B)
Class com.google.common.collect.Iterators$5                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 3 times (x 139B)
Class com.google.common.collect.Iterators$4                                           : loaded 3 times (x 78B)
Class com.google.common.collect.SortedIterable                                        : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 3 times (x 121B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 3 times (x 142B)
Class com.google.common.collect.Iterators$1                                           : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 3 times (x 204B)
Class org.gradle.api.UncheckedIOException                                             : loaded 3 times (x 78B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 3 times (x 203B)
Class com.google.common.collect.NullnessCasts                                         : loaded 3 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 3 times (x 147B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 3 times (x 92B)
Class com.google.common.collect.ObjectArrays                                          : loaded 3 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 3 times (x 78B)
Class com.google.common.collect.BiMap                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 3 times (x 77B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 3 times (x 146B)
Class com.google.common.base.CharMatcher$And                                          : loaded 3 times (x 108B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 3 times (x 203B)
Class com.google.common.collect.Iterators                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Range                                                 : loaded 3 times (x 83B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 3 times (x 212B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 3 times (x 205B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 3 times (x 65B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 3 times (x 119B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 3 times (x 73B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 3 times (x 77B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 3 times (x 76B)
Class com.google.common.collect.Platform                                              : loaded 3 times (x 67B)
Class com.google.common.base.Joiner                                                   : loaded 3 times (x 75B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 3 times (x 119B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 3 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 3 times (x 93B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 3 times (x 295B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 3 times (x 111B)
Class com.android.builder.model.v2.models.AndroidDsl                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.gradle.DefaultProjectIdentifier                     : loaded 2 times (x 82B)
Class com.amazon.ion.impl._Private_IonContainer                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntHashMap                         : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.allopen.AllOpenModel              : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$SerializedForm                           : loaded 2 times (x 69B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 73B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class kotlin.jvm.internal.ArrayIteratorKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.amazon.ion.impl.SharedSymbolTable                                           : loaded 2 times (x 89B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter                                      : loaded 2 times (x 165B)
Class com.amazon.ion.SymbolToken                                                      : loaded 2 times (x 66B)
Class [Lcom.google.common.base.Function;                                              : loaded 2 times (x 65B)
Class com.google.common.io.CharSource$ConcatenatedCharSource                          : loaded 2 times (x 82B)
Class com.google.common.collect.MapMakerInternalMap$Strength$1                        : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.android.builder.model.v2.models.BasicAndroidProject                         : loaded 2 times (x 66B)
Class com.android.builder.model.AndroidProject                                        : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class kotlin.text.MatchResult                                                         : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceScope                                                  : loaded 2 times (x 69B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.ToolingModelContract                                         : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonBoolLite                                            : loaded 2 times (x 174B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash$NULL                          : loaded 2 times (x 67B)
Class com.google.common.reflect.Types$ClassOwnership$1                                : loaded 2 times (x 76B)
Class com.google.common.collect.MapMakerInternalMap$Strength$2                        : loaded 2 times (x 76B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.gradle.internal.impldep.gnu.trove.PrimeFinder                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.kapt.KaptGradleModel              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinDslScriptAdditionalTask           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ClasspathEntryModel                          : loaded 2 times (x 66B)
Class build_3c68g4cqxtim38gie18mfrqau$_run_closure1                                   : loaded 2 times (x 135B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$ClassOwnership$2                                : loaded 2 times (x 76B)
Class com.google.common.reflect.TypeResolver                                          : loaded 2 times (x 68B)
Class com.google.common.collect.MapMakerInternalMap$StrongValueEntry                  : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class com.android.ide.gradle.model.AdditionalClassifierArtifactsModelParameter        : loaded 2 times (x 66B)
Class com.amazon.ion.IonTimestamp                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator       : loaded 2 times (x 77B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.Collections2$FilteredCollection                       : loaded 2 times (x 115B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs64Bit                        : loaded 2 times (x 78B)
Class com.google.common.reflect.Types$ClassOwnership$3                                : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class settings_3bl2rfiofw99tk4pe4p48otnk$_run_closure1                                : loaded 2 times (x 135B)
Class org.gradle.api.JavaVersion                                                      : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Dependency                                             : loaded 2 times (x 66B)
Class com.amazon.ion.IonDatagram                                                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1   : loaded 2 times (x 74B)
Class com.amazon.ion.impl._Private_ByteTransferSink                                   : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState  : loaded 2 times (x 75B)
Class [Lcom.google.common.collect.Maps$EntryFunction;                                 : loaded 2 times (x 65B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.internal.Platform$Posix                             : loaded 2 times (x 77B)
Class com.google.common.collect.Interners$InternerImpl                                : loaded 2 times (x 71B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class com.android.builder.model.v2.ide.AbstractArtifact                               : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.ExternalLibraryDependency                    : loaded 2 times (x 66B)
Class com.google.common.reflect.Invokable                                             : loaded 2 times (x 99B)
Class com.google.common.collect.MapMaker$Dummy                                        : loaded 2 times (x 75B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 73B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.amazon.ion.IonValue                                                         : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap$1                       : loaded 2 times (x 134B)
Class com.google.common.reflect.Types$ParameterizedTypeImpl                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractIterator$1                                    : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class org.apache.commons.io.output.StringBuilderWriter                                : loaded 2 times (x 96B)
Class com.google.common.collect.CollectSpliterators$1WithCharacteristics              : loaded 2 times (x 86B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder$Mutable                       : loaded 2 times (x 97B)
Class org.apache.commons.io.output.ByteArrayOutputStream                              : loaded 2 times (x 88B)
Class com.google.common.reflect.TypeResolver$TypeVariableKey                          : loaded 2 times (x 68B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.amazon.ion.IonWriter                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonRawWriter                                   : loaded 2 times (x 66B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.IntelliJSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinSourceSetContainer                : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.TestInfo$Execution                             : loaded 2 times (x 75B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode                    : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonSystemLite                                          : loaded 2 times (x 271B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.GradlePropertiesModel                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystem                                             : loaded 2 times (x 167B)
Class com.amazon.ion.impl.bin.Symbols$2                                               : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin._Private_IonManagedWriter                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.android.synthetic.idea.AndroidExtensionsGradleModel        : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.util.GradleVersionUtil                     : loaded 2 times (x 67B)
Class org.apache.commons.io.FileExistsException                                       : loaded 2 times (x 78B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.model.UnsupportedMethodException                             : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContainerLite                                       : loaded 2 times (x 244B)
Class com.amazon.ion.IonBufferConfiguration$OversizedSymbolTableHandler               : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonWriterBuilder                                          : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ToolingStreamApiUtils        : loaded 2 times (x 67B)
Class com.amazon.ion.facet.Faceted                                                    : loaded 2 times (x 66B)
Class ijInit1_49ih7zuoejrdj2xelmhqc51i4                                               : loaded 2 times (x 175B)
Class com.google.common.reflect.TypeToken$SimpleTypeToken                             : loaded 2 times (x 69B)
Class com.google.common.io.CharSource$StringCharSource                                : loaded 2 times (x 82B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry            : loaded 2 times (x 78B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.utf8.Poolable                                           : loaded 2 times (x 75B)
Class com.amazon.ion.IonSymbol                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.THash                                     : loaded 2 times (x 77B)
Class [Lcom.android.ide.common.repository.AgpVersion$PreviewKind;                     : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.CodeShrinker                                   : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class net.rubygrapefruit.platform.internal.Platform                                   : loaded 2 times (x 75B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonStructLite                                          : loaded 2 times (x 294B)
Class com.amazon.ion.impl._Private_ReaderWriter                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractSymbolTable                                     : loaded 2 times (x 103B)
Class com.amazon.ion.Decimal                                                          : loaded 2 times (x 129B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$1  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService: loaded 2 times (x 73B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class net.rubygrapefruit.platform.NativeIntegrationUnavailableException               : loaded 2 times (x 78B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService: loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 108B)
Class kotlin.collections.AbstractIterator                                             : loaded 2 times (x 79B)
Class kotlin.text.CharsKt                                                             : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.CollectionMapper                            : loaded 2 times (x 69B)
Class com.amazon.ion.impl.lite.IonFloatLite                                           : loaded 2 times (x 182B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder                               : loaded 2 times (x 97B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$2  : loaded 2 times (x 76B)
Class org.gradle.internal.impldep.gnu.trove.Equality                                  : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Linux32Bit                        : loaded 2 times (x 79B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class [Lcom.android.builder.model.v2.ide.LibraryType;                                 : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.LibraryType                                    : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.projectModel.KotlinTaskProperties                     : loaded 2 times (x 66B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.ExternalDependencyId                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack                                     : loaded 2 times (x 68B)
Class com.amazon.ion.impl._Private_IonReaderBuilder                                   : loaded 2 times (x 91B)
Class com.amazon.ion.IonMutableCatalog                                                : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$WriteContext: loaded 2 times (x 68B)
Class net.rubygrapefruit.platform.Native                                              : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.NativeIntegration                                   : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 143B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 107B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter                      : loaded 2 times (x 76B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectCanonicalHashingStrategy           : loaded 2 times (x 76B)
Class [Lnet.rubygrapefruit.platform.internal.FunctionResult$Failure;                  : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag    : loaded 2 times (x 75B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 100B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class kotlin.collections.ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1           : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class org.gradle.internal.time.DefaultTimer                                           : loaded 2 times (x 76B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator$1     : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonStringLite                                          : loaded 2 times (x 208B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 72B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.text.MatcherMatchResult$groupValues$1                                    : loaded 2 times (x 194B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewKey              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.amazon.ion.impl.bin.Block                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.lombok.LombokModel                : loaded 2 times (x 66B)
Class com.google.common.reflect.Reflection                                            : loaded 2 times (x 67B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.android.ide.common.repository.AgpVersion                                    : loaded 2 times (x 71B)
Class com.android.builder.model.v2.CustomSourceDirectory                              : loaded 2 times (x 66B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker: loaded 2 times (x 72B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool$1                            : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLoaderLite                                          : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContext                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystemText                                         : loaded 2 times (x 186B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode;    : loaded 2 times (x 65B)
Class com.amazon.ion.ValueFactory                                                     : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalTask                                 : loaded 2 times (x 66B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.android.builder.model.v2.models.ProjectSyncIssues                           : loaded 2 times (x 66B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class org.gradle.internal.time.DefaultCountdownTimer                                  : loaded 2 times (x 84B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$TwoElementSequenceInputStream     : loaded 2 times (x 88B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolver                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_SymbolToken                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector                       : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.GradleTaskModel                              : loaded 2 times (x 66B)
Class org.apache.commons.io.output.NullOutputStream                                   : loaded 2 times (x 81B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.internal.gradle.DefaultBuildIdentifier                       : loaded 2 times (x 75B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.IExternalSystemSourceType     : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$Mutable                           : loaded 2 times (x 91B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIdentityHashingStrategy            : loaded 2 times (x 74B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ProjectDependencies: loaded 2 times (x 66B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class kotlin.sequences.DistinctIterator                                               : loaded 2 times (x 79B)
Class org.apache.commons.io.filefilter.IOFileFilter                                   : loaded 2 times (x 66B)
Class com.google.common.io.AppendableWriter                                           : loaded 2 times (x 96B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 72B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$2     : loaded 2 times (x 76B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifactsModel       : loaded 2 times (x 66B)
Class com.amazon.ion.IonBufferConfiguration                                           : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.Symbols                                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService: loaded 2 times (x 78B)
Class com.google.common.collect.Maps$EntryFunction$1                                  : loaded 2 times (x 87B)
Class net.rubygrapefruit.platform.internal.FunctionResult                             : loaded 2 times (x 73B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class com.android.builder.model.Variant                                               : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleProjectIdentity                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class [Lcom.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization;             : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.google.common.collect.Maps$EntryFunction$2                                  : loaded 2 times (x 87B)
Class net.rubygrapefruit.platform.NativeIntegrationLinkageException                   : loaded 2 times (x 78B)
Class com.google.common.reflect.Invokable$ConstructorInvokable                        : loaded 2 times (x 103B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.android.builder.model.v2.ide.JavaArtifact                                   : loaded 2 times (x 66B)
Class com.android.builder.model.ProjectSyncIssues                                     : loaded 2 times (x 66B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class kotlin.ranges.ClosedRange                                                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState                        : loaded 2 times (x 81B)
Class com.amazon.ion.impl._Private_IonWriter                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.InternalBuildIdentifier: loaded 2 times (x 71B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$WriteContext: loaded 2 times (x 69B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.apache.commons.io.IOUtils                                                   : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeToken                                             : loaded 2 times (x 69B)
Class com.google.common.collect.MapMakerInternalMap$Segment                           : loaded 2 times (x 138B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class kotlin.collections.IntIterator                                                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.SymbolToken;                                                   : loaded 2 times (x 65B)
Class com.google.common.cache.RemovalCause                                            : loaded 2 times (x 76B)
Class net.rubygrapefruit.platform.internal.jni.NativeLibraryFunctions                 : loaded 2 times (x 67B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.android.builder.model.v2.dsl.SigningConfig                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.AndroidProject                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.AndroidModel                                       : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalProjectDependency                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.KotlinDslScriptsModelSerializationService: loaded 2 times (x 77B)
Class [Lcom.google.common.reflect.Types$JavaVersion;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.Interners$InternerBuilder                             : loaded 2 times (x 72B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.collections.ArrayDeque                                                   : loaded 2 times (x 203B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchList$Node                       : loaded 2 times (x 68B)
Class com.amazon.ion.system.IonSystemBuilder$Mutable                                  : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolverBuilder            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradleModel                       : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Windows                           : loaded 2 times (x 75B)
Class com.google.common.io.CharStreams$NullWriter                                     : loaded 2 times (x 95B)
Class com.google.common.io.CharSource$CharSequenceCharSource                          : loaded 2 times (x 82B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class kotlin.jvm.internal.Lambda                                                      : loaded 2 times (x 71B)
Class com.android.ide.gradle.model.ArtifactIdentifier                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$LocalSymbolTableView             : loaded 2 times (x 103B)
Class com.android.builder.model.v2.ide.Variant                                        : loaded 2 times (x 66B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.MapMakerInternalMap$Strength                          : loaded 2 times (x 76B)
Class org.gradle.tooling.model.Model                                                  : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.composites.BuildMap                                : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchList                            : loaded 2 times (x 81B)
Class com.amazon.ion.UnknownSymbolException                                           : loaded 2 times (x 82B)
Class com.google.common.reflect.TypeToken$1                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.noarg.NoArgModel                  : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap$ArrayProducer                     : loaded 2 times (x 66B)
Class kotlin.text.MatchResult$Destructured                                            : loaded 2 times (x 68B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class org.jetbrains.plugins.gradle.model.IntelliJProjectSettings                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.annotation.AnnotationBasedPluginModel: loaded 2 times (x 66B)
Class com.google.common.reflect.TypeToken$2                                           : loaded 2 times (x 106B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap$AsMapEntries           : loaded 2 times (x 134B)
Class com.android.builder.model.v2.ide.Library                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ArtifactDependenciesAdjacencyList              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.Versions                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class kotlin.sequences.SequencesKt___SequencesKt$filterNotNull$1                      : loaded 2 times (x 74B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache: loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.model.ExternalDependency                           : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap                                   : loaded 2 times (x 68B)
Class [Lcom.google.common.base.Supplier;                                              : loaded 2 times (x 65B)
Class net.rubygrapefruit.platform.internal.LibraryDef                                 : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1     : loaded 2 times (x 76B)
Class com.android.builder.model.v2.models.ndk.NativeModule                            : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.amazon.ion.impl.lite.IonSymbolLite                                          : loaded 2 times (x 242B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$1                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct$Factory                            : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.IonType;                                                       : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.VersionCatalogsModel                         : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.taskIndex.GradleTaskRequest     : loaded 2 times (x 66B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind$Companion              : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.PrivacySandboxSdkInfo                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode;                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class org.gradle.tooling.internal.adapter.ObjectGraphAdapter                          : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginPropertiesModelParameters : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$2                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.model.ProjectImportModelProvider                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonNull                                                          : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators$6                                           : loaded 2 times (x 77B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class org.gradle.internal.operations.MultipleBuildOperationFailures                   : loaded 2 times (x 91B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 217B)
Class kotlin.text.RegexKt                                                             : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerInfo                        : loaded 2 times (x 72B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$3                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.IonBinaryWriter                                                  : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetArtifactIndex.GradleSourceSetArtifactBuildRequest: loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Window32Bit                       : loaded 2 times (x 75B)
Class com.android.builder.model.v2.models.ModelBuilderParameter                       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.MethodInvocation                            : loaded 2 times (x 81B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$TransformingRandomAccessList                    : loaded 2 times (x 195B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$4                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$1: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.ExternalProject                              : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 83B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 108B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.utf8.Pool                                               : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_SymtabExtendsCache                                 : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$2: loaded 2 times (x 79B)
Class build_3c68g4cqxtim38gie18mfrqau                                                 : loaded 2 times (x 176B)
Class [Lcom.android.builder.model.v2.ide.CodeShrinker;                                : loaded 2 times (x 65B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.android.utils.FileUtils                                                     : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.android.builder.model.v2.ide.AaptOptions$Namespacing                        : loaded 2 times (x 75B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class com.amazon.ion.impl._Private_IonSymbol                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonValueLite                                           : loaded 2 times (x 143B)
Class com.amazon.ion.IonContainer                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider                            : loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$3: loaded 2 times (x 79B)
Class com.google.common.reflect.TypeResolver$TypeTable                                : loaded 2 times (x 69B)
Class com.google.common.io.CharStreams                                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class org.gradle.util.internal.DefaultGradleVersion$Stage                             : loaded 2 times (x 71B)
Class kotlin.coroutines.jvm.internal.RestrictedContinuationImpl                       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider         : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$4: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$Companion: loaded 2 times (x 67B)
Class com.google.common.reflect.TypeResolver$TypeTable$1                              : loaded 2 times (x 69B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class [Lkotlin.Pair;                                                                  : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class kotlin.text.MatchNamedGroupCollection                                           : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.gradle.internal.time.MonotonicClock                                         : loaded 2 times (x 73B)
Class com.google.common.collect.TransformedListIterator                               : loaded 2 times (x 89B)
Class com.amazon.ion.BufferConfiguration$Builder                                      : loaded 2 times (x 74B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$5: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelWriteContext: loaded 2 times (x 68B)
Class com.google.common.reflect.TypeCapture                                           : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 107B)
Class [Lkotlin.sequences.Sequence;                                                    : loaded 2 times (x 65B)
Class kotlin.sequences.SequencesKt___SequencesKt$sortedWith$1                         : loaded 2 times (x 71B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IntList                                                 : loaded 2 times (x 73B)
Class com.amazon.ion.impl.lite._Private_LiteDomTrampoline                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonType                                                          : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$6: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap                          : loaded 2 times (x 69B)
Class net.rubygrapefruit.platform.internal.Platform$Linux                             : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class com.android.builder.model.v2.ide.LibraryInfo                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.cache.LocalCache$Values                                       : loaded 2 times (x 114B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class settings_3bl2rfiofw99tk4pe4p48otnk$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.ValueFactoryLite                                       : loaded 2 times (x 214B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$7: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.RepositoryModels                             : loaded 2 times (x 66B)
Class com.intellij.util.ThrowableConsumer                                             : loaded 2 times (x 66B)
Class com.google.common.collect.Streams                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$SubList                                         : loaded 2 times (x 194B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class com.amazon.ion.impl.bin.WriteBuffer                                             : loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter                                  : loaded 2 times (x 162B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$8: loaded 2 times (x 79B)
Class com.google.common.reflect.Types$TypeVariableInvocationHandler                   : loaded 2 times (x 71B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntry                     : loaded 2 times (x 66B)
Class org.objectweb.asm.commons.InstructionAdapter                                    : loaded 2 times (x 183B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class org.gradle.tooling.internal.adapter.TargetTypeProvider                          : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 107B)
Class [Lorg.gradle.api.JavaVersion;                                                   : loaded 2 times (x 65B)
Class kotlin.ranges.IntRange                                                          : loaded 2 times (x 89B)
Class org.gradle.tooling.model.idea.IdeaDependencyScope                               : loaded 2 times (x 66B)
Class org.gradle.internal.time.TimeSource                                             : loaded 2 times (x 66B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptsModel                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonBlobLite                                            : loaded 2 times (x 215B)
Class com.amazon.ion.impl.LocalSymbolTable                                            : loaded 2 times (x 117B)
Class org.jetbrains.plugins.gradle.tooling.serialization.SerializationService         : loaded 2 times (x 66B)
Class com.google.common.base.Predicates                                               : loaded 2 times (x 67B)
Class kotlin.ranges.IntProgression$Companion                                          : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestInfo                                       : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.AndroidArtifact                                : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.NativeException                                     : loaded 2 times (x 78B)
Class org.gradle.tooling.internal.gradle.GradleBuildIdentity                          : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.IonList                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.Supplier    : loaded 2 times (x 66B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.android.builder.model.v2.ide.JavaCompileOptions                             : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MixInMappingAction   : loaded 2 times (x 76B)
Class org.gradle.internal.exceptions.ResolutionProvider                               : loaded 2 times (x 66B)
Class com.amazon.ion.IonSystem                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolContext            : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$EntryFunction                                    : loaded 2 times (x 87B)
Class net.rubygrapefruit.platform.internal.Platform$Unsupported                       : loaded 2 times (x 75B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD32Bit                      : loaded 2 times (x 78B)
Class com.google.common.reflect.Types$ClassOwnership$1LocalClass                      : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 116B)
Class kotlin.text.MatchResult$DefaultImpls                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaCompilerOutput                                : loaded 2 times (x 66B)
Class org.gradle.tooling.model.ProjectIdentifier                                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService: loaded 2 times (x 73B)
Class net.rubygrapefruit.platform.internal.Platform$Unix                              : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.ComponentInfo                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ProjectType                                    : loaded 2 times (x 75B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonIntLite                                             : loaded 2 times (x 185B)
Class com.amazon.ion.util._Private_FastAppendable                                     : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode;                 : loaded 2 times (x 65B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class com.android.builder.model.v2.dsl.BaseConfig                                     : loaded 2 times (x 66B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 107B)
Class kotlin.sequences.DistinctSequence                                               : loaded 2 times (x 71B)
Class org.gradle.internal.time.TimeSource$1                                           : loaded 2 times (x 73B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonSexpLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.impl.lite.IonListLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.IonFloat                                                         : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$JavaVersion                                     : loaded 2 times (x 79B)
Class com.google.common.collect.MapMaker                                              : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingModel                    : loaded 2 times (x 66B)
Class com.amazon.ion.impl.LocalSymbolTable$Factory                                    : loaded 2 times (x 73B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode       : loaded 2 times (x 76B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService: loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class settings_3bl2rfiofw99tk4pe4p48otnk                                              : loaded 2 times (x 175B)
Class kotlin.jvm.internal.ArrayIterator                                               : loaded 2 times (x 75B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct                                    : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonTimestampLite                                       : loaded 2 times (x 188B)
Class org.jetbrains.plugins.gradle.model.FileCollectionDependency                     : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHashingStrategy                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.utilDummyModel.DummyModel       : loaded 2 times (x 66B)
Class com.google.common.reflect.Invokable$MethodInvokable                             : loaded 2 times (x 103B)
Class com.google.common.io.LineReader$1                                               : loaded 2 times (x 71B)
Class com.google.common.collect.CollectSpliterators                                   : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap                        : loaded 2 times (x 124B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry          : loaded 2 times (x 80B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.gradle.internal.time.Clock                                                  : loaded 2 times (x 66B)
Class com.amazon.ion.IonString                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.jar.JarTaskManifestConfiguration             : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetDependencyModel               : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class com.android.builder.model.v2.dsl.ProductFlavor                                  : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.TestInfo$Execution;                          : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class kotlin.io.FilePathComponents                                                    : loaded 2 times (x 68B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.RestrictedSuspendLambda                          : loaded 2 times (x 87B)
Class org.gradle.tooling.internal.adapter.TypeInspector                               : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$NoOpDecoration       : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Element                                                : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class com.android.ide.gradle.model.LegacyV1AgpVersionModel                            : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration                                              : loaded 2 times (x 69B)
Class com.amazon.ion.system.SimpleCatalog                                             : loaded 2 times (x 87B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService: loaded 2 times (x 72B)
Class sync_studio_tooling6444_et45etochyvzxeosmmi9lyslf                               : loaded 2 times (x 175B)
Class com.google.common.base.Predicates$CompositionPredicate                          : loaded 2 times (x 84B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.model.GradleExtensions                             : loaded 2 times (x 66B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 108B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1    : loaded 2 times (x 71B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$1                        : loaded 2 times (x 73B)
Class org.gradle.internal.impldep.com.google.common.base.Preconditions                : loaded 2 times (x 67B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.tooling.model.internal.ImmutableDomainObjectSet                      : loaded 2 times (x 151B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.UnsupportedIonVersionException                                   : loaded 2 times (x 81B)
Class org.apache.commons.io.FileUtils                                                 : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$Helper   : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags                : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag; : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.BasicVariant                                   : loaded 2 times (x 66B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 74B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState                      : loaded 2 times (x 76B)
Class com.amazon.ion.IonStruct                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleBuildScriptClasspathModel              : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$ClassOwnership                                  : loaded 2 times (x 76B)
Class com.android.builder.model.v2.ide.BundleInfo                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceProvider                                 : loaded 2 times (x 66B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class kotlin.text.CharsKt__CharJVMKt                                                  : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class org.gradle.util.internal.DefaultGradleVersion                                   : loaded 2 times (x 80B)
Class com.google.common.collect.Sets$FilteredSet                                      : loaded 2 times (x 133B)
Class com.google.common.io.Java8Compatibility                                         : loaded 2 times (x 67B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.amazon.ion.system.IonBinaryWriterBuilder                                    : loaded 2 times (x 93B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$4                      : loaded 2 times (x 81B)
Class org.jetbrains.plugins.gradle.model.UnresolvedExternalDependency                 : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class com.amazon.ion.impl.IonWriterSystemTextMarkup                                   : loaded 2 times (x 188B)
Class com.amazon.ion.impl.bin.BlockAllocator                                          : loaded 2 times (x 76B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs32Bit                        : loaded 2 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform$Linux64Bit                        : loaded 2 times (x 79B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 74B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class com.amazon.ion.system.IonWriterBuilderBase                                      : loaded 2 times (x 79B)
Class com.google.common.collect.Interners                                             : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class [Lcom.android.builder.model.v2.ide.ProjectType;                                 : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$DefaultViewBuilder   : loaded 2 times (x 76B)
Class [Lcom.android.builder.model.v2.ide.AaptOptions$Namespacing;                     : loaded 2 times (x 65B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails$1   : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$1                                    : loaded 2 times (x 73B)
Class [Lcom.amazon.ion.SymbolTable;                                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonBinaryWriterAdapter$Factory                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.BlockedBuffer$BufferedOutputStream                          : loaded 2 times (x 86B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$ReadContext: loaded 2 times (x 68B)
Class net.rubygrapefruit.platform.internal.FunctionResult$Failure                     : loaded 2 times (x 75B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLoader                        : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl: loaded 2 times (x 73B)
Class com.google.common.io.CharSource$AsByteSource                                    : loaded 2 times (x 81B)
Class com.android.ide.common.repository.AgpVersion$Companion                          : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class org.gradle.tooling.model.build.GradleEnvironment                                : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class org.gradle.tooling.model.idea.IdeaDependency                                    : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization                : loaded 2 times (x 75B)
Class com.google.common.collect.MapMakerInternalMap                                   : loaded 2 times (x 157B)
Class com.android.builder.model.v2.ide.AaptOptions                                    : loaded 2 times (x 66B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.BlockAllocatorProvider                                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService: loaded 2 times (x 73B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetModel                         : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause$1                                          : loaded 2 times (x 76B)
Class com.android.builder.model.v2.models.ndk.NativeModelBuilderParameter             : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.android.builder.model.v2.models.VariantDependenciesAdjacencyList            : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class kotlin.KotlinNothingValueException                                              : loaded 2 times (x 78B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.amazon.ion.impl.lite.IonSequenceLite                                        : loaded 2 times (x 428B)
Class com.amazon.ion.impl._Private_Utils$1                                            : loaded 2 times (x 85B)
Class com.amazon.ion.impl.SymbolTableAsStruct                                         : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause$2                                          : loaded 2 times (x 76B)
Class com.google.common.collect.MapMakerInternalMap$WeakValueReference                : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD                           : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.RemovalCause;                                         : loaded 2 times (x 65B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$AbsentValueProvider     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractIonWriter                                       : loaded 2 times (x 157B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector$Processor             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.samWithReceiver.SamWithReceiverModel: loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause$3                                          : loaded 2 times (x 76B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$ObjectFactory            : loaded 2 times (x 66B)
Class com.amazon.ion.IonException                                                     : loaded 2 times (x 80B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 108B)
Class kotlin.collections.State                                                        : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function4                                                  : loaded 2 times (x 66B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$3                      : loaded 2 times (x 81B)
Class org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder                   : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaModuleDependency                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ViewBuilder                                 : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonReaderBuilder                                          : loaded 2 times (x 89B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingConfig                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonReader                                                        : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause$4                                          : loaded 2 times (x 76B)
Class com.google.common.io.CharSource$EmptyCharSource                                 : loaded 2 times (x 82B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.android.builder.model.v2.ide.AndroidLibraryData                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ApiVersion                                     : loaded 2 times (x 66B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 108B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonClob                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.ModelBuilderService$Parameter              : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalCause$5                                          : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$AbstractFilteredMap                              : loaded 2 times (x 123B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Strength;                       : loaded 2 times (x 65B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.android.builder.model.v2.models.Versions$Version                            : loaded 2 times (x 66B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 107B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonLobLite                                             : loaded 2 times (x 180B)
Class com.amazon.ion.impl.lite.IonTextLite                                            : loaded 2 times (x 177B)
Class com.amazon.ion.IonNumber                                                        : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntProcedure                       : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLocator                       : loaded 2 times (x 69B)
Class net.rubygrapefruit.platform.Process                                             : loaded 2 times (x 66B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueSegment        : loaded 2 times (x 138B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ReflectionMethodInvoker: loaded 2 times (x 72B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder                                  : loaded 2 times (x 76B)
Class com.amazon.ion.IonBool                                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.assignment.AssignmentModel        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterables$5                                           : loaded 2 times (x 77B)
Class com.google.common.reflect.Types$NativeTypeVariableEquals                        : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeParameter                                         : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.MapMaker$Dummy;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class com.amazon.ion.IonLoader                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleProperty                               : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapKeySet                                    : loaded 2 times (x 146B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.VariantDependencies                         : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.amazon.ion.impl._Private_Utils                                              : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.android.builder.model.v2.ide.TestedTargetVariant                            : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class kotlin.ranges.IntRange$Companion                                                : loaded 2 times (x 67B)
Class kotlin.text.MatcherMatchResult$groups$1                                         : loaded 2 times (x 147B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.MultiCauseException                              : loaded 2 times (x 66B)
Class org.gradle.internal.time.CountdownTimer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$ReadContext: loaded 2 times (x 69B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyWriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.parcelize.ParcelizeGradleModel    : loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class net.rubygrapefruit.platform.ProcessLauncher                                     : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 101B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$1                             : loaded 2 times (x 74B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class org.gradle.tooling.model.ProjectModel                                           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleConfiguration                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool                              : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonSystem                                          : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$OversizedValueHandler                        : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class kotlin.ranges.OpenEndRange                                                      : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceBuilderIterator                                        : loaded 2 times (x 81B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class com.amazon.ion.IonDecimal                                                       : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestsModel                     : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$JavaVersion$1                                   : loaded 2 times (x 79B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.android.ide.common.repository.AgpVersion$Companion$WhenMappings             : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$3                             : loaded 2 times (x 74B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaLanguageLevel                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker    : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$WeakKey                 : loaded 2 times (x 75B)
Class org.gradle.internal.exceptions.DefaultMultiCauseException                       : loaded 2 times (x 91B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder$Mutable                     : loaded 2 times (x 105B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService: loaded 2 times (x 73B)
Class com.google.common.reflect.Types$JavaVersion$2                                   : loaded 2 times (x 79B)
Class com.google.common.reflect.Types                                                 : loaded 2 times (x 67B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ProjectInfo                                    : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class kotlin.text.CharsKt__CharKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArrayDeque$Companion                                         : loaded 2 times (x 67B)
Class org.gradle.tooling.model.HierarchicalElement                                    : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchPoint                           : loaded 2 times (x 67B)
Class com.amazon.ion.IonBlob                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.IonLob                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.IonSequence                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService: loaded 2 times (x 73B)
Class com.google.common.reflect.Types$JavaVersion$3                                   : loaded 2 times (x 79B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.android.builder.model.v2.dsl.DependenciesInfo                               : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Segment;                        : loaded 2 times (x 65B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$ValueIterator                                : loaded 2 times (x 82B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD64Bit                      : loaded 2 times (x 78B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class org.gradle.internal.time.Time                                                   : loaded 2 times (x 67B)
Class com.amazon.ion.system.IonTextWriterBuilder                                      : loaded 2 times (x 93B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1$1 : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_LocalSymbolTableFactory                            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService: loaded 2 times (x 73B)
Class com.google.common.reflect.Types$JavaVersion$4                                   : loaded 2 times (x 79B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntryHelper               : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.Table                                                 : loaded 2 times (x 66B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonValue                                           : loaded 2 times (x 66B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 100B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$2                      : loaded 2 times (x 81B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonNullLite                                            : loaded 2 times (x 171B)
Class [Lcom.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState;: loaded 2 times (x 65B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash                               : loaded 2 times (x 90B)
Class org.jetbrains.plugins.gradle.model.DependencyAccessorsModel                     : loaded 2 times (x 66B)
Class com.google.common.reflect.Types$JavaVersion$5                                   : loaded 2 times (x 67B)
Class [Lcom.google.common.reflect.Types$ClassOwnership;                               : loaded 2 times (x 65B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonClobLite                                            : loaded 2 times (x 216B)
Class com.amazon.ion.impl._Private_ValueFactory                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$1                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode; : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinMPPGradleModel                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyDownloadPolicyModel.GradleDependencyDownloadPolicy: loaded 2 times (x 66B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.gradle.tooling.model.BuildIdentifier                                        : loaded 2 times (x 66B)
Class com.amazon.ion.IonInt                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$2                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.PrepareKotlinIdeImportTaskModel         : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs                             : loaded 2 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform$Window64Bit                       : loaded 2 times (x 75B)
Class com.google.common.collect.CollectSpliterators$1                                 : loaded 2 times (x 86B)
Class kotlin.text.MatcherMatchResult                                                  : loaded 2 times (x 76B)
Class com.android.builder.model.v2.ide.VectorDrawablesOptions                         : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class kotlin.text.MatchGroupCollection                                                : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class com.android.ide.gradle.model.GradlePluginModel                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$3                  : loaded 2 times (x 78B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.android.builder.model.v2.ide.Edge                                           : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class org.gradle.tooling.model.build.JavaEnvironment                                  : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleExtension                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonDecimalLite                                         : loaded 2 times (x 184B)
Class com.amazon.ion.IonBufferConfiguration$Builder                                   : loaded 2 times (x 74B)
Class com.android.builder.model.v2.ide.SourceSetContainer                             : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.gradle.internal.time.Timer                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchList$1                          : loaded 2 times (x 79B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType;                     : loaded 2 times (x 65B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 79B)
Class com.android.builder.model.v2.ide.LintOptions                                    : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails     : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaProject                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.model.DomainObjectSet                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder$Result                           : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Pool$Allocator                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonText                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.SymbolTokenImpl                                             : loaded 2 times (x 77B)
Class com.amazon.ion.SubstituteSymbolTableException                                   : loaded 2 times (x 80B)
Class com.google.common.cache.LocalCache$SoftValueReference                           : loaded 2 times (x 93B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 123B)
Class com.android.builder.model.v2.ide.BasicArtifact                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class org.gradle.util.GradleVersion                                                   : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportDescriptor                 : loaded 2 times (x 72B)
Class com.amazon.ion.SymbolTable                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService$Companion: loaded 2 times (x 67B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.android.builder.model.v2.ide.ViewBindingOptions                             : loaded 2 times (x 66B)
Class com.google.common.io.LineReader                                                 : loaded 2 times (x 68B)
Class [Lkotlin.collections.State;                                                     : loaded 2 times (x 65B)
Class kotlin.jvm.internal.Ref$BooleanRef                                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.StringCompanionObject                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache$MethodInvocationKey: loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonWriterBase                                      : loaded 2 times (x 158B)
Class com.amazon.ion.system.IonSystemBuilder                                          : loaded 2 times (x 71B)
Class com.amazon.ion.IonCatalog                                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.utilTurnOffDefaultTasksModel.TurnOffDefaultTasks: loaded 2 times (x 66B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.android.utils.StringHelper                                                  : loaded 2 times (x 67B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelWriteContext: loaded 2 times (x 68B)
Class com.google.common.reflect.Types$TypeVariableImpl                                : loaded 2 times (x 68B)
Class com.google.common.io.LineBuffer                                                 : loaded 2 times (x 71B)
Class com.google.common.collect.MapMakerInternalMap$1                                 : loaded 2 times (x 79B)
Class com.android.builder.model.v2.dsl.BuildType                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class kotlin.collections.AbstractIterator$WhenMappings                                : loaded 2 times (x 67B)
Class kotlin.ranges.IntProgressionIterator                                            : loaded 2 times (x 78B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap                         : loaded 2 times (x 72B)
Class org.gradle.tooling.GradleConnectionException                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$1                    : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewDecoration       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.amazon.ion.impl.lite.ContainerlessContext                                   : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$1                      : loaded 2 times (x 81B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.google.common.collect.ForwardingCollection                                  : loaded 2 times (x 126B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.adapter.MethodInvoker                               : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginProperties                : loaded 2 times (x 66B)
Class com.amazon.ion.UnexpectedEofException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.IonSexp                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_LocalSymbolTable                                   : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$DataHandler                                  : loaded 2 times (x 66B)
Class com.amazon.ion.ReadOnlyValueException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder                             : loaded 2 times (x 105B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)
Class kotlin.ranges.IntProgression                                                    : loaded 2 times (x 77B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.model.gradle.GradleScript                                    : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4830)
OS uptime: 0 days 7:23 hours

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 61 stepping 4 microcode 0x2f, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 2701, Current Mhz: 2701, Mhz Limit: 2701

Memory: 4k page, system-wide physical 8094M (298M free)
TotalPageFile size 20106M (AvailPageFile size 437M)
current process WorkingSet (physical memory assigned to process): 237M, peak: 2338M
current process commit charge ("private bytes"): 2055M, peak: 2712M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
