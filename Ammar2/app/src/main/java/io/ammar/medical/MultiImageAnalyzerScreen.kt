package io.ammar.medical

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageDecoder
import android.graphics.Matrix
import android.graphics.pdf.PdfRenderer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.os.ParcelFileDescriptor
import android.provider.MediaStore
import android.util.Log
import android.webkit.MimeTypeMap
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Archive
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.PhotoCamera
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.SyncAlt
import androidx.compose.material.icons.outlined.Archive
import androidx.compose.material.icons.outlined.Folder
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material.icons.outlined.InsertDriveFile
import androidx.compose.material.icons.outlined.PhotoLibrary
import androidx.compose.material.icons.outlined.Send
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.snapshots.SnapshotStateMap
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.FileProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.rememberAsyncImagePainter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import java.util.zip.ZipEntry
import java.util.zip.ZipFile
import androidx.core.content.ContextCompat
import android.content.pm.PackageManager
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import io.ammar.medical.viewmodel.MedicalAnalysisViewModel
import io.ammar.medical.viewmodel.SettingsViewModel
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.util.concurrent.TimeUnit
import androidx.compose.runtime.derivedStateOf
import io.ammar.medical.viewmodel.AnalysisState
import androidx.compose.foundation.layout.heightIn
import java.util.Calendar
import java.util.regex.Pattern
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.PictureAsPdf
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.outlined.Folder
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.ui.graphics.asImageBitmap
import coil.compose.AsyncImage
import io.ammar.medical.ui.components.SavedDoctorsList
import java.util.zip.ZipInputStream
import android.Manifest
import androidx.compose.ui.graphics.vector.ImageVector

@Parcelize
data class AnalyzedFile(
    val id: String = UUID.randomUUID().toString(),
    val uri: Uri,
    val isPdf: Boolean = false,
    val isFromZip: Boolean = false,
    val zipSource: String? = null,
    val fileName: String = "",
    val folderPath: String = "",
    val dateTimestamp: Long = 0,
    val extractedText: String? = null,
    val isProcessing: Boolean = false,
    val error: String? = null,
    val isSelected: Boolean = true
    // Bitmap is not Parcelable, so we'll handle it separately
    // val thumbnailBitmap: Bitmap? = null
) : Parcelable

// Custom saver for AnalyzedFile list that handles the Bitmap separately
val AnalyzedFilesListSaver = listSaver<List<AnalyzedFile>, Any>(
    save = { files ->
        files.flatMap { file ->
            listOf(
                file.id,
                file.uri.toString(),
                file.isPdf,
                file.isFromZip,
                file.zipSource ?: "",
                file.fileName,
                file.folderPath,
                file.dateTimestamp,
                file.extractedText ?: "",
                file.isProcessing,
                file.error ?: "",
                file.isSelected
            )
        }
    },
    restore = { values ->
        val files = mutableListOf<AnalyzedFile>()
        for (i in values.indices step 12) {
            val id = values[i] as String
            val uriString = values[i + 1] as String
            val isPdf = values[i + 2] as Boolean
            val isFromZip = values[i + 3] as Boolean
            val zipSource = (values[i + 4] as String).takeIf { it.isNotEmpty() }
            val fileName = values[i + 5] as String
            val folderPath = values[i + 6] as String
            val dateTimestamp = values[i + 7] as Long
            val extractedText = (values[i + 8] as String).takeIf { it.isNotEmpty() }
            val isProcessing = values[i + 9] as Boolean
            val error = (values[i + 10] as String).takeIf { it.isNotEmpty() }
            val isSelected = values[i + 11] as Boolean
            
            files.add(
                AnalyzedFile(
                    id = id,
                    uri = Uri.parse(uriString),
                    isPdf = isPdf,
                    isFromZip = isFromZip,
                    zipSource = zipSource,
                    fileName = fileName,
                    folderPath = folderPath,
                    dateTimestamp = dateTimestamp,
                    extractedText = extractedText,
                    isProcessing = isProcessing,
                    error = error,
                    isSelected = isSelected
                )
            )
        }
        files
    }
)

// Helper function to create a temporary image URI for camera capture
private fun createTempImageUri(context: Context): Uri? {
    return try {
        val tempFile = File.createTempFile(
            "camera_capture_",
            ".jpg",
            context.cacheDir
        ).apply {
            deleteOnExit()
        }
        
        FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            tempFile
        )
    } catch (e: IOException) {
        Log.e("MultiImageAnalyzer", "Error creating temp file", e)
        null
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MultiImageAnalyzerScreen(
    onNavigateBack: () -> Unit,
    sharedZipUri: Uri? = null,
    viewModel: MedicalAnalysisViewModel = viewModel(),
    settingsViewModel: SettingsViewModel = viewModel()
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // Create a temporary file for camera capture
    val tempImageUri = remember {
        createTempImageUri(context)
    }
    
    // Define selectedFiles here, before it's used in cameraLauncher
    var selectedFiles by rememberSaveable(stateSaver = AnalyzedFilesListSaver) { mutableStateOf<List<AnalyzedFile>>(emptyList()) }
    
    // Camera launcher
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            // Get bitmap from Uri
            tempImageUri?.let { uri ->
                // Process the image in background
                scope.launch(Dispatchers.IO) {
                    val bitmap = try {
                        val imageStream = context.contentResolver.openInputStream(uri)
                        BitmapFactory.decodeStream(imageStream)
                    } catch (e: Exception) {
                        Log.e("MultiImageAnalyzer", "Failed to decode camera image: ${e.message}")
                        null
                    }
                    
                    if (bitmap == null) {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                context,
                                "Failed to process camera image",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                        return@launch
                    }
                    
                    // Add the bitmap to selectedFiles
                    processImageBitmap(bitmap, "Camera Capture", selectedFiles, { updatedFiles ->
                        selectedFiles = updatedFiles
                    }, context)
                }
            }
        }
    }
    
    // Permission launcher for camera
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // Permission granted, launch camera
            tempImageUri?.let {
                cameraLauncher.launch(it)
            }
        } else {
            // Permission denied, show toast
            Toast.makeText(
                context,
                "Camera permission is required to take photos",
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    // PC connection state
    var pcServerUrl by rememberSaveable { mutableStateOf<String?>(null) }
    var isPcConnected by rememberSaveable { mutableStateOf(false) }
    var isSendingToPC by rememberSaveable { mutableStateOf(false) }
    var pcConnectionStatus by rememberSaveable { mutableStateOf<String?>(null) }
    
    // QR Code scanner launcher using ZXing barcode scanner
    val qrCodeScanLauncher = rememberLauncherForActivityResult(ScanContract()) { result ->
        if (result.contents != null) {
            Log.d("MultiImageAnalyzerScreen", "QR Code scanned: ${result.contents}")
            
            val qrContent = result.contents
            
            // Extract server URL from QR code
            try {
                if (qrContent.startsWith("PC_")) {
                    // This is a PC connection code from the sync script
                    val sessionId = qrContent.substringBefore(":")
                    pcServerUrl = "http://${context.resources.getString(R.string.server_ip)}:5000/send_text/default"
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC Transfer Service", Toast.LENGTH_SHORT).show()
                    Log.d("MultiImageAnalyzerScreen", "Connected to PC with session ID: $sessionId")
                } else if (qrContent.startsWith("http://") || qrContent.startsWith("https://")) {
                    // This is a regular URL
                    pcServerUrl = qrContent
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC: $qrContent", Toast.LENGTH_SHORT).show()
                } else {
                    // Try to interpret as a direct IP address
                    pcServerUrl = "http://${qrContent}:5000/send_text/default"
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC at $qrContent", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("MultiImageAnalyzerScreen", "Error processing QR code", e)
                Toast.makeText(context, "Error connecting to PC: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // Function to send text to PC and return result
    suspend fun sendTextToPC(text: String, serverUrl: String): Result<String> = withContext(Dispatchers.IO) {
        try {
            // Code to send text to PC
            val client = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build()
            
            val json = JSONObject().apply {
                put("text", text)
            }
            
            val body = json.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
            
            val request = Request.Builder()
                .url(serverUrl)
                .post(body)
                .build()
            
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                Result.success("Successfully sent to PC")
            } else {
                Result.failure(Exception("Failed to send: ${response.code} ${response.message}"))
            }
        } catch (e: Exception) {
            Log.e("MultiImageAnalyzer", "Error sending to PC: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // Check if premium features are activated
    val isPremiumActivated by settingsViewModel.isActivated.collectAsState()
    
    // Dialog for premium feature requirement
    var showPremiumDialog by remember { mutableStateOf(false) }
    
    // State with rememberSaveable to preserve across configuration changes
    var holisticAnalysis by rememberSaveable { mutableStateOf<String?>(null) }
    var isAnalyzing by rememberSaveable { mutableStateOf(false) }
    var analysisError by rememberSaveable { mutableStateOf<String?>(null) }
    var isProcessingBatch by rememberSaveable { mutableStateOf(false) }
    var doctorEmail by rememberSaveable { mutableStateOf("") }
    var showDoctorDialog by rememberSaveable { mutableStateOf(false) }
    var isSendingToDoctor by rememberSaveable { mutableStateOf(false) }
    var sendSuccess by rememberSaveable { mutableStateOf<Boolean?>(null) }
    var currentFileIndex by remember { mutableIntStateOf(0) }
    
    // State for showing ZIP extraction dialog
    var showZipExtractionDialog by remember { mutableStateOf(false) }
    var zipExtractionProgress by remember { mutableStateOf(0f) }
    var zipContents by remember { mutableStateOf<List<AnalyzedFile>>(emptyList()) }
    var isExtractingZip by remember { mutableStateOf(false) }
    var currentZipUri by remember { mutableStateOf<Uri?>(null) }
    var currentZipFileName by remember { mutableStateOf("") }
    
    // State for ZIP file explorer UI
    var currentFolder by remember { mutableStateOf("") }
    var showDateView by remember { mutableStateOf(false) }
    var folderStructure by remember { mutableStateOf<MutableMap<String, List<AnalyzedFile>>>(mutableMapOf()) }
    var allZipFiles by remember { mutableStateOf<List<AnalyzedFile>>(emptyList()) }
    var dateSortedFiles by remember { mutableStateOf<MutableMap<String, List<AnalyzedFile>>>(mutableMapOf()) }
    
    // Add a key to force recomposition when needed
    var zipContentUpdateKey by remember { mutableStateOf(0) }
    
    // Optimize with derived state for current visible files
    val currentVisibleFiles = remember(currentFolder, showDateView, zipContentUpdateKey) {
        derivedStateOf {
            if (showDateView) {
                dateSortedFiles.values.flatten()
            } else {
                folderStructure[currentFolder]?.toList() ?: emptyList()
            }
        }
    }.value
    
    // State for WhatsApp chat handling
    var showContactFilterMenu by remember { mutableStateOf(false) }
    var isWhatsAppExport by remember { mutableStateOf(false) }
    
    // Add a state variable to store extracted senders when needed
    var extractedSenders by remember { mutableStateOf<List<String>>(emptyList()) }
    
    // Load settings from SettingsViewModel
    LaunchedEffect(Unit) {
        settingsViewModel.loadSettings(context)
        
        // Process shared ZIP file if present
        sharedZipUri?.let { uri ->
            try {
                // Only prepare the ZIP dialog, don't extract yet
                currentZipUri = uri
                isExtractingZip = false
                zipExtractionProgress = 0f
                zipContents = emptyList()
                
                // Get file name from URI
                var fileName = "archive.zip"
                context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                    if (cursor.moveToFirst()) {
                        val nameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
                        if (nameIndex >= 0) {
                            fileName = cursor.getString(nameIndex)
                        }
                    }
                }
                currentZipFileName = fileName
                
                // Show ZIP dialog immediately without extracting
                showZipExtractionDialog = true
                
                // Reset folder view to empty when new ZIP is shared
                currentFolder = ""
                showDateView = false
                
                // Check if this is a WhatsApp export by examining file structure
                scope.launch {
                    try {
                        val extractedSenders = extractSendersFromChat(context, uri)
                        if (extractedSenders.isNotEmpty()) {
                            // Only update these state variables on the main thread
                            withContext(Dispatchers.Main) {
                                isWhatsAppExport = true
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("MultiImageAnalyzer", "Error loading chat senders: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("MultiImageAnalyzer", "Failed to process shared ZIP: ${e.message}")
                Toast.makeText(context, "Failed to process shared ZIP file: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // Load senders when a WhatsApp ZIP is detected
    LaunchedEffect(currentZipUri) {
        currentZipUri?.let { uri ->
            try {
                // Check if this is a WhatsApp export by examining file structure
                val senders = extractSendersFromChat(context, uri)
                if (senders.isNotEmpty()) {
                    isWhatsAppExport = true
                }
            } catch (e: Exception) {
                Log.e("MultiImageAnalyzer", "Error loading chat senders: ${e.message}")
            }
        }
    }
    
    // Collect state from ViewModels
    val analysisState by viewModel.analysisState.collectAsState()
    val patientId by viewModel.patientId.collectAsState()
    
    // Store thumbnails separately since Bitmap is not Parcelable
    val thumbnailsMap = remember { mutableStateMapOf<String, Bitmap>() }
    
    // Gemini model - get from SettingsViewModel
    val geminiModel = remember { settingsViewModel.getGeminiModel() }
    
    // Function to extract text from an image
    val extractTextFromImage = { uri: Uri, fileId: String ->
        scope.launch(Dispatchers.IO) {
            try {
                // Update file status to processing
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(isProcessing = true, error = null)
                        else it
                    }
                }
                
                // Convert URI to Bitmap
                val bitmap = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    val source = ImageDecoder.createSource(context.contentResolver, uri)
                    ImageDecoder.decodeBitmap(source) { decoder, _, _ ->
                        decoder.allocator = ImageDecoder.ALLOCATOR_SOFTWARE
                        decoder.setTargetSampleSize(2) // Downsample to reduce memory usage
                    }
                } else {
                    @Suppress("DEPRECATION")
                    MediaStore.Images.Media.getBitmap(context.contentResolver, uri)
                }
                
                // Create Gemini content with image and prompt
                val prompt = "Extract all text visible in this image. Return ONLY the extracted text, nothing else."
                
                val content = content {
                    image(bitmap)
                    text(prompt)
                }
                
                // Generate content using the model from SettingsViewModel
                val response = geminiModel.generateContent(content)
                val result = response.text
                
                // Update file with extracted text
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(extractedText = result, isProcessing = false)
                        else it
                    }
                }
            } catch (e: Exception) {
                Log.e("ImageAnalyzer", "Error extracting text from image", e)
                // Update file with error
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(error = "Error: ${e.message}", isProcessing = false)
                        else it
                    }
                }
            }
        }
    }
    
    // Function to extract text from a PDF
    val extractTextFromPdf = { uri: Uri, fileId: String ->
        scope.launch(Dispatchers.IO) {
            try {
                // Update file status to processing
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(isProcessing = true, error = null)
                        else it
                    }
                }
                
                // Create a temporary file to store the PDF
                val inputStream = context.contentResolver.openInputStream(uri)
                val tempFile = File.createTempFile("temp_pdf", ".pdf", context.cacheDir)
                val outputStream = FileOutputStream(tempFile)
                
                inputStream?.use { input ->
                    outputStream.use { output ->
                        input.copyTo(output)
                    }
                }
                
                // Create a PDF renderer
                val fileDescriptor = ParcelFileDescriptor.open(tempFile, ParcelFileDescriptor.MODE_READ_ONLY)
                val pdfRenderer = PdfRenderer(fileDescriptor)
                
                // Get the first page as a thumbnail
                val thumbnailBitmap = if (pdfRenderer.pageCount > 0) {
                    val page = pdfRenderer.openPage(0)
                    val bitmap = Bitmap.createBitmap(page.width / 2, page.height / 2, Bitmap.Config.ARGB_8888)
                    page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                    page.close()
                    bitmap
                } else {
                    null
                }
                
                // Store thumbnail in the map
                if (thumbnailBitmap != null) {
                    withContext(Dispatchers.Main) {
                        thumbnailsMap[fileId] = thumbnailBitmap
                    }
                }
                
                // Extract text from all pages using Gemini
                val allPagesText = StringBuilder()
                
                for (i in 0 until minOf(pdfRenderer.pageCount, 5)) { // Limit to first 5 pages to avoid token limits
                    val page = pdfRenderer.openPage(i)
                    val pageBitmap = Bitmap.createBitmap(page.width / 2, page.height / 2, Bitmap.Config.ARGB_8888)
                    page.render(pageBitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                    
                    // Create Gemini content with page image and prompt
                    val prompt = "Extract all text visible in this PDF page. Return ONLY the extracted text, nothing else."
                    
                    val content = content {
                        image(pageBitmap)
                        text(prompt)
                    }
                    
                    // Generate content using the model from SettingsViewModel
                    val response = geminiModel.generateContent(content)
                    val result = response.text
                    
                    if (!result.isNullOrBlank()) {
                        allPagesText.append("--- Page ${i + 1} ---\n")
                        allPagesText.append(result)
                        allPagesText.append("\n\n")
                    }
                    
                    page.close()
                }
                
                // Close resources
                pdfRenderer.close()
                fileDescriptor.close()
                tempFile.delete()
                
                // Update file with extracted text and thumbnail
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(
                            extractedText = allPagesText.toString(),
                            isProcessing = false
                        )
                        else it
                    }
                }
            } catch (e: Exception) {
                Log.e("ImageAnalyzer", "Error extracting text from PDF", e)
                // Update file with error
                withContext(Dispatchers.Main) {
                    selectedFiles = selectedFiles.map { 
                        if (it.id == fileId) it.copy(error = "Error: ${e.message}", isProcessing = false)
                        else it
                    }
                }
            }
        }
    }
    
    // Function to process files in batches with limited concurrency
    val processFilesInBatches = { newFiles: List<AnalyzedFile> ->
        scope.launch {
            isProcessingBatch = true
            
            // Process files in batches of 3 at a time
            val batchSize = 3
            val totalBatches = (newFiles.size + batchSize - 1) / batchSize
            
            for (batchIndex in 0 until totalBatches) {
                val startIndex = batchIndex * batchSize
                val endIndex = minOf(startIndex + batchSize, newFiles.size)
                val batch = newFiles.subList(startIndex, endIndex)
                
                // Process this batch concurrently
                val deferreds = batch.map { file ->
                    async(Dispatchers.IO) {
                        if (file.isPdf) {
                            extractTextFromPdf(file.uri, file.id)
                        } else {
                            extractTextFromImage(file.uri, file.id)
                        }
                    }
                }
                
                // Wait for all files in this batch to complete before moving to next batch
                deferreds.awaitAll()
            }
            
            isProcessingBatch = false
        }
    }
    
    // Image picker launcher
    val imagePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickMultipleVisualMedia(maxItems = 10)
    ) { uris ->
        if (uris.isNotEmpty()) {
            // Add all selected images to the list
            val newFiles = uris.map { uri ->
                // Take persistable URI permissions for all images
                try {
                    val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                    context.contentResolver.takePersistableUriPermission(uri, takeFlags)
                } catch (e: Exception) {
                    Log.e("MultiImageAnalyzer", "Failed to take persistable permission: ${e.message}")
                }
                
                AnalyzedFile(uri = uri)
            }
            selectedFiles = selectedFiles + newFiles
            
            // Process files in batches with limited concurrency
            processFilesInBatches(newFiles)
        }
    }
    
    // PDF picker launcher
    val pdfPicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenMultipleDocuments()
    ) { uris ->
        if (uris.isNotEmpty()) {
            // Take persistable URI permissions for all PDFs
            uris.forEach { uri ->
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)
            }
            
            // Add all selected PDFs to the list
            val newFiles = uris.map { uri ->
                AnalyzedFile(uri = uri, isPdf = true)
            }
            selectedFiles = selectedFiles + newFiles
            
            // Process files in batches with limited concurrency
            processFilesInBatches(newFiles)
        }
    }

    // ZIP file picker launcher
    val zipPicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        if (uri != null) {
            try {
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)
                
                // Prepare the dialog without starting extraction
                currentZipUri = uri
                isExtractingZip = false
                zipExtractionProgress = 0f
                zipContents = emptyList()
                
                // Get file name from URI
                var fileName = "archive.zip"
                context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                    if (cursor.moveToFirst()) {
                        val nameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
                        if (nameIndex >= 0) {
                            fileName = cursor.getString(nameIndex)
                        }
                    }
                }
                currentZipFileName = fileName
                
                // Show dialog immediately without extracting
                showZipExtractionDialog = true
            } catch (e: Exception) {
                Log.e("MultiImageAnalyzer", "Failed to take persistable permission: ${e.message}")
                Toast.makeText(context, "Failed to access ZIP file: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // Function to handle configuration changes and restore state
    DisposableEffect(Unit) {
        onDispose {
            // Clean up any resources if needed when the composable is disposed
        }
    }

    // Save URI permissions when the component is first created
    LaunchedEffect(Unit) {
        // Restore any thumbnails that might have been lost during configuration change
        selectedFiles.filter { it.isPdf && !thumbnailsMap.containsKey(it.id) }.forEach { file ->
            scope.launch(Dispatchers.IO) {
                try {
                    val inputStream = context.contentResolver.openInputStream(file.uri)
                    val tempFile = File.createTempFile("temp_pdf", ".pdf", context.cacheDir)
                    val outputStream = FileOutputStream(tempFile)
                    
                    inputStream?.use { input ->
                        outputStream.use { output ->
                            input.copyTo(output)
                        }
                    }
                    
                    val fileDescriptor = ParcelFileDescriptor.open(tempFile, ParcelFileDescriptor.MODE_READ_ONLY)
                    val pdfRenderer = PdfRenderer(fileDescriptor)
                    
                    if (pdfRenderer.pageCount > 0) {
                        val page = pdfRenderer.openPage(0)
                        val bitmap = Bitmap.createBitmap(page.width / 2, page.height / 2, Bitmap.Config.ARGB_8888)
                        page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                        page.close()
                        
                        withContext(Dispatchers.Main) {
                            thumbnailsMap[file.id] = bitmap
                        }
                    }
                    
                    pdfRenderer.close()
                    fileDescriptor.close()
                    tempFile.delete()
                } catch (e: Exception) {
                    Log.e("MultiImageAnalyzer", "Error restoring PDF thumbnail: ${e.message}")
                }
            }
        }
    }

    // Update UI based on analysis state
    LaunchedEffect(analysisState) {
        when (val state = analysisState) {
            is AnalysisState.Initial -> {
                // Initial state, nothing to do
            }
            is AnalysisState.Analyzing -> {
                isAnalyzing = true
                analysisError = null
                holisticAnalysis = null
            }
            is AnalysisState.Success -> {
                isAnalyzing = false
                holisticAnalysis = state.analysisText
                analysisError = null
            }
            is AnalysisState.Error -> {
                isAnalyzing = false
                analysisError = state.message
            }
            is AnalysisState.Sending -> {
                isSendingToDoctor = true
                sendSuccess = null
            }
            is AnalysisState.SendSuccess -> {
                isSendingToDoctor = false
                sendSuccess = true
            }
            is AnalysisState.SendError -> {
                isSendingToDoctor = false
                sendSuccess = false
            }
        }
    }

    // ZIP File Selection Dialog
    if (showZipExtractionDialog) {
        AlertDialog(
            onDismissRequest = {
                // Only allow dismissal if not currently extracting
                if (!isExtractingZip) {
                    showZipExtractionDialog = false
                }
            },
            title = { 
                Text(
                    text = if (isExtractingZip) 
                        stringResource(R.string.extracting_zip)
                    else if (zipContents.isEmpty())
                        stringResource(R.string.process_archive, currentZipFileName.ifEmpty { "ZIP Archive" })
                    else
                        if (showDateView) stringResource(R.string.files_by_date) else stringResource(R.string.files_by_folder)
                ) 
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 450.dp)
                ) {
                    if (isExtractingZip) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(stringResource(R.string.extracting_zip))
                            Spacer(modifier = Modifier.height(8.dp))
                            LinearProgressIndicator(
                                progress = { zipExtractionProgress },
                                modifier = Modifier.fillMaxWidth()
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text("${(zipExtractionProgress * 100).toInt()}%")
                        }
                    } else if (zipContents.isEmpty()) {
                        // Show message before extraction begins
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.Archive,
                                contentDescription = null,
                                modifier = Modifier.size(48.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = stringResource(R.string.prepare_extract),
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Click 'Start Extraction' to process the archive and select files to analyze.",
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center
                            )
                        }
                    } else {
                        // Toggle view button
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            Row(
                                modifier = Modifier
                                    .border(
                                        width = 1.dp,
                                        color = MaterialTheme.colorScheme.outline,
                                        shape = MaterialTheme.shapes.small
                                    )
                            ) {
                                TextButton(
                                    onClick = { showDateView = false },
                                    colors = ButtonDefaults.textButtonColors(
                                        containerColor = if (!showDateView) 
                                            MaterialTheme.colorScheme.primaryContainer 
                                        else 
                                            Color.Transparent
                                    )
                                ) {
                                    Text(stringResource(R.string.folders))
                                }
                                
                                TextButton(
                                    onClick = { showDateView = true },
                                    colors = ButtonDefaults.textButtonColors(
                                        containerColor = if (showDateView) 
                                            MaterialTheme.colorScheme.primaryContainer 
                                        else
                                            Color.Transparent
                                    )
                                ) {
                                    Text(stringResource(R.string.by_date))
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // Add filter options row
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // File count info
                            Text(
                                text = "${zipContents.size} files found • ${zipContents.count { it.isSelected }} selected",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(start = 4.dp)
                            )
                            
                            // Filter dropdown menu
                            var showFilterMenu by remember { mutableStateOf(false) }
                            
                            Box {
                                FilledTonalButton(
                                    onClick = { showFilterMenu = true },
                                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
                                    modifier = Modifier.heightIn(min = 32.dp)
                                ) {
                                    Text("Filter", style = MaterialTheme.typography.bodyMedium)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Icon(
                                        imageVector = Icons.Default.FilterList,
                                        contentDescription = "Filter Options",
                                        modifier = Modifier.size(18.dp)
                                    )
                                }
                                
                                DropdownMenu(
                                    expanded = showFilterMenu,
                                    onDismissRequest = { showFilterMenu = false }
                                ) {
                                    // Quick selection options
                                    DropdownMenuItem(
                                        text = { Text("Select All") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Check, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { it.copy(isSelected = true) }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            zipContentUpdateKey++ // Force recomposition
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    DropdownMenuItem(
                                        text = { Text("Deselect All") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Clear, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { it.copy(isSelected = false) }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            zipContentUpdateKey++ // Force recomposition
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    Divider()
                                    
                                    // Date-based filters
                                    val calendar = Calendar.getInstance()
                                    
                                    // Last 24 hours filter
                                    calendar.add(Calendar.HOUR_OF_DAY, -24)
                                    val oneDayAgo = calendar.timeInMillis
                                    DropdownMenuItem(
                                        text = { Text("Last 24h") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Image, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            // Filter to images from last 24h
                                            val calendar = Calendar.getInstance()
                                            val currentTime = calendar.timeInMillis
                                            val oneDayAgo = currentTime - (24 * 60 * 60 * 1000)
                                            
                                            zipContents = zipContents.map { file ->
                                                // Select files created in the last 24 hours
                                                if (file.dateTimestamp > oneDayAgo) {
                                                    file.copy(isSelected = true)
                                                } else {
                                                    file.copy(isSelected = false)
                                                }
                                            }
                                            
                                            // Update both views
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            zipContentUpdateKey++ // Force recomposition
                                        }
                                    )
                                    
                                    // Last 3 days filter
                                    calendar.timeInMillis = System.currentTimeMillis()
                                    calendar.add(Calendar.DAY_OF_YEAR, -3)
                                    val threeDaysAgo = calendar.timeInMillis
                                    DropdownMenuItem(
                                        text = { Text("Last 3 Days") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Image, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { file ->
                                                file.copy(isSelected = file.dateTimestamp > threeDaysAgo)
                                            }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    // Last week filter
                                    calendar.timeInMillis = System.currentTimeMillis()
                                    calendar.add(Calendar.DAY_OF_YEAR, -7)
                                    val weekAgo = calendar.timeInMillis
                                    DropdownMenuItem(
                                        text = { Text("Last Week") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Image, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { file ->
                                                file.copy(isSelected = file.dateTimestamp > weekAgo)
                                            }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    Divider()
                                    
                                    // File type filters
                                    DropdownMenuItem(
                                        text = { Text("Images Only") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.Image, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { file ->
                                                file.copy(isSelected = !file.isPdf)
                                            }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    DropdownMenuItem(
                                        text = { Text("PDFs Only") },
                                        leadingIcon = { 
                                            Icon(
                                                imageVector = Icons.Default.PictureAsPdf, 
                                                contentDescription = null
                                            ) 
                                        },
                                        onClick = {
                                            zipContents = zipContents.map { file ->
                                                file.copy(isSelected = file.isPdf)
                                            }
                                            updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                            showFilterMenu = false
                                        }
                                    )
                                    
                                    // Current folder only (when in folder view)
                                    if (!showDateView && currentFolder.isNotEmpty()) {
                                        DropdownMenuItem(
                                            text = { Text("Current Folder Only") },
                                            leadingIcon = { 
                                                Icon(
                                                    imageVector = Icons.Default.Folder, 
                                                    contentDescription = null
                                                ) 
                                            },
                                            onClick = {
                                                zipContents = zipContents.map { file ->
                                                    file.copy(isSelected = file.folderPath == currentFolder)
                                                }
                                                updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                                showFilterMenu = false
                                            }
                                        )
                                    }
                                    
                                    // Special WhatsApp filters
                                    if (isWhatsAppExport) {
                                        Divider()
                                        
                                        Text(
                                            "WhatsApp Filters",
                                            style = MaterialTheme.typography.labelMedium,
                                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                        
                                        // Filter by sender
                                        DropdownMenuItem(
                                            text = { Text("Latest Message Files") },
                                            leadingIcon = { 
                                                Icon(
                                                    imageVector = Icons.Default.Image, 
                                                    contentDescription = null
                                                ) 
                                            },
                                            onClick = {
                                                val calendar = Calendar.getInstance()
                                                calendar.add(Calendar.HOUR, -2) // Consider very recent
                                                val recentTimestamp = calendar.timeInMillis
                                                
                                                zipContents = zipContents.map { file ->
                                                    // Select files from last chat message (usually has same timestamps)
                                                    val maxTimestamp = zipContents.maxOfOrNull { it.dateTimestamp } ?: 0L
                                                    val isLatest = file.dateTimestamp >= maxTimestamp - (1000 * 60 * 5) // 5 minute window
                                                    
                                                    file.copy(isSelected = isLatest)
                                                }
                                                updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                                showFilterMenu = false
                                            }
                                        )
                                        
                                        // Select medical reports based on naming patterns
                                        DropdownMenuItem(
                                            text = { Text("Medical Reports") },
                                            leadingIcon = { 
                                                Icon(
                                                    imageVector = Icons.Default.PictureAsPdf, 
                                                    contentDescription = null
                                                ) 
                                            },
                                            onClick = {
                                                // Common patterns in medical report filenames
                                                val medicalTerms = listOf(
                                                    "report", "lab", "test", "scan", "results", 
                                                    "xray", "x-ray", "mri", "ct", "ultrasound", 
                                                    "blood", "medical", "doctor", "hospital",
                                                    "prescription", "diagnosis"
                                                )
                                                
                                                zipContents = zipContents.map { file ->
                                                    val lowerFileName = file.fileName.lowercase()
                                                    val isMedical = medicalTerms.any { term -> 
                                                        lowerFileName.contains(term) 
                                                    }
                                                    file.copy(isSelected = isMedical)
                                                }
                                                updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                                showFilterMenu = false
                                            }
                                        )
                                        
                                        // Select by contacts
                                        if (isWhatsAppExport) {
                                            DropdownMenuItem(
                                                text = { Text("By Contact") },
                                                leadingIcon = { 
                                                    Icon(
                                                        imageVector = Icons.Default.Person, 
                                                        contentDescription = null
                                                    ) 
                                                },
                                                trailingIcon = {
                                                    Icon(
                                                        imageVector = Icons.Default.ChevronRight,
                                                        contentDescription = null
                                                    )
                                                },
                                                onClick = {
                                                    // Show sub-menu for contacts
                                                    showFilterMenu = false
                                                    showContactFilterMenu = true
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Breadcrumb navigation when in folder view
                        if (!showDateView && currentFolder.isNotEmpty()) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                TextButton(
                                    onClick = { currentFolder = "" },
                                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Text("Root")
                                }
                                
                                currentFolder.split("/").filter { it.isNotEmpty() }.forEachIndexed { index, folder ->
                                    Text(" > ")
                                    TextButton(
                                        onClick = {
                                            currentFolder = currentFolder.split("/")
                                                .take(index + 1)
                                                .joinToString("/")
                                        },
                                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                                    ) {
                                        Text(folder)
                                    }
                                }
                            }
                        }
                        
                        if (showDateView) {
                            // Date view - show files organized by date
                            // Add effect outside of the LazyColumn content lambda
                            LaunchedEffect(zipContentUpdateKey) {
                                // Empty effect just to trigger recomposition when key changes
                            }
                            
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(8.dp)
                            ) {
                                // Group by date and sort by most recent
                                val dateGroups = dateSortedFiles.toSortedMap(compareByDescending { it })
                                
                                dateGroups.forEach { (dateString, files) ->
                                    item {
                                        Text(
                                            text = dateString,
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.Bold,
                                            modifier = Modifier.padding(vertical = 8.dp)
                                        )
                                        
                                        Divider()
                                    }
                                    
                                    items(files) { file ->
                                        FileListItem(
                                            file, 
                                            onToggleSelection = { selectedFile ->
                                                toggleFileSelection(
                                                    selectedFile,
                                                    zipContents,
                                                    { zipContents = it },
                                                    { zipContentUpdateKey++ }
                                                )
                                            }
                                        )
                                    }
                                }
                            }
                        } else {
                            // Folder view - show files and folders in current path
                            // Add effect outside of the LazyColumn content lambda
                            LaunchedEffect(zipContentUpdateKey) {
                                // Empty effect just to trigger recomposition when key changes
                            }
                            
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(8.dp)
                            ) {
                                if (currentFolder.isEmpty()) {
                                    // Root level - show all top-level folders
                                    val rootFolders = folderStructure.keys
                                        .filter { it.isNotEmpty() && !it.contains("/") }
                                        .sorted()
                                    
                                    items(rootFolders) { folder ->
                                        FolderListItem(
                                            folderName = folder,
                                            fileCount = folderStructure[folder]?.size ?: 0,
                                            onClick = { currentFolder = folder }
                                        )
                                    }
                                    
                                    // Show root files (if any)
                                    folderStructure["root"]?.let { rootFiles ->
                                        items(rootFiles) { file ->
                                            FileListItem(
                                                file,
                                                onToggleSelection = { selectedFile ->
                                                    toggleFileSelection(
                                                        selectedFile,
                                                        zipContents,
                                                        { zipContents = it },
                                                        { zipContentUpdateKey++ }
                                                    )
                                                }
                                            )
                                        }
                                    }
                                } else {
                                    // In a subfolder - show navigation, subfolders and files
                                    item {
                                        // Back navigation
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clickable {
                                                    // Go up one level
                                                    currentFolder = if (currentFolder.contains("/")) {
                                                        currentFolder.substringBeforeLast("/")
                                                    } else {
                                                        ""
                                                    }
                                                }
                                                .padding(8.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.ArrowBack,
                                                contentDescription = "Back",
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                            Spacer(modifier = Modifier.width(8.dp))
                                            Text("../ (Up one level)")
                                        }
                                        Divider()
                                    }
                                    
                                    // Show subfolders
                                    val subfolders = folderStructure.keys
                                        .filter {
                                            it.startsWith("$currentFolder/") && 
                                            it.count { c -> c == '/' } == currentFolder.count { c -> c == '/' } + 1
                                        }
                                        .sorted()
                                    
                                    items(subfolders) { fullPath ->
                                        val folderName = fullPath.substringAfterLast("/")
                                        FolderListItem(
                                            folderName = folderName,
                                            fileCount = folderStructure[fullPath]?.size ?: 0,
                                            onClick = { currentFolder = fullPath }
                                        )
                                    }
                                    
                                    // Show files in current folder
                                    folderStructure[currentFolder]?.let { folderFiles ->
                                        items(folderFiles) { file ->
                                            FileListItem(
                                                file,
                                                onToggleSelection = { selectedFile ->
                                                    toggleFileSelection(
                                                        selectedFile,
                                                        zipContents,
                                                        { zipContents = it },
                                                        { zipContentUpdateKey++ }
                                                    )
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Selection stats
                        val selectedCount = zipContents.count { it.isSelected }
                        Text(
                            text = "$selectedCount of ${zipContents.size} files selected",
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            },
            confirmButton = {
                if (zipContents.isEmpty() && !isExtractingZip) {
                    // Before extraction, show start extraction button
                    Button(
                        onClick = {
                            // Begin extraction process
                            scope.launch {
                                extractZipContents(currentZipUri!!, context, { progress ->
                                    zipExtractionProgress = progress
                                }, { files ->
                                    zipContents = files
                                    allZipFiles = files
                                    
                                    // Organize files into folder structure
                                    val structure = files.groupBy { it.folderPath }
                                    folderStructure = structure.toMutableMap()
                                    
                                    // Organize files by date
                                    val dateFormat = java.text.SimpleDateFormat("MMMM dd, yyyy", java.util.Locale.getDefault())
                                    dateSortedFiles = files.groupBy { file ->
                                        dateFormat.format(java.util.Date(file.dateTimestamp))
                                    }.toSortedMap(compareByDescending { it }).toMutableMap()
                                    
                                }, { error ->
                                    analysisError = error
                                }, {
                                    isExtractingZip = true
                                }, {
                                    isExtractingZip = false
                                }, {
                                    isWhatsAppExport = true
                                })
                            }
                        }
                    ) {
                        Text(stringResource(R.string.start_extraction))
                    }
                } else if (!isExtractingZip) {
                    // After extraction, show add selected files button
                    Button(
                        onClick = {
                            // Add selected files to main list
                            val zipSelectedFiles = zipContents.filter { it.isSelected }
                            if (zipSelectedFiles.isNotEmpty()) {
                                // Add the files and process them
                                selectedFiles = selectedFiles + zipSelectedFiles
                                processFilesInBatches(zipSelectedFiles)
                            }
                            showZipExtractionDialog = false
                        },
                        enabled = zipContents.any { it.isSelected }
                    ) {
                        Text(stringResource(R.string.add_selected_files))
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showZipExtractionDialog = false
                    },
                    enabled = !isExtractingZip
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text("SmartMed")
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    // Add QR code scan button to connect to PC
                    IconButton(
                        onClick = {
                            // Launch QR code scanner
                            val scanOptions = ScanOptions().apply {
                                setPrompt("Align QR code within the frame.")
                                setBeepEnabled(true)
                                setOrientationLocked(false)
                                setDesiredBarcodeFormats(ScanOptions.QR_CODE) // Focus only on QR codes
                                setBarcodeImageEnabled(true)
                                setTimeout(30000) // 30 second timeout
                                setCameraId(0) // Use back camera
                            }
                            qrCodeScanLauncher.launch(scanOptions)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = "Connect to PC",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Remove API Key status Card and replace with introduction text
            Text(
                text = stringResource(R.string.upload_intro),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // File selection buttons
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Image selection button
                Button(
                    onClick = {
                        if (selectedFiles.size >= 10) {
                            // Show toast or snackbar about limit
                            Toast.makeText(
                                context,
                                "Maximum of 10 files allowed. Please remove some files first.",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            imagePicker.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = selectedFiles.size < 10
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(stringResource(R.string.images))
                }
                
                // Camera capture button
                Button(
                    onClick = {
                        if (selectedFiles.size >= 10) {
                            Toast.makeText(
                                context,
                                "Maximum of 10 files allowed. Please remove some files first.",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else if (tempImageUri == null) {
                            Toast.makeText(
                                context,
                                "Unable to initialize camera. Please try again.",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            // Check if we have the permission
                            if (ContextCompat.checkSelfPermission(
                                    context,
                                    Manifest.permission.CAMERA
                                ) == PackageManager.PERMISSION_GRANTED
                            ) {
                                // We have the permission, launch camera
                                cameraLauncher.launch(tempImageUri)
                            } else {
                                // We don't have permission, request it
                                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                            }
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = selectedFiles.size < 10,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Icon(Icons.Default.PhotoCamera, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(stringResource(R.string.camera))
                }
                
                // PDF selection button
                Button(
                    onClick = {
                        if (selectedFiles.size >= 10) {
                            // Show toast or snackbar about limit
                            Toast.makeText(
                                context,
                                "Maximum of 10 files allowed. Please remove some files first.",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            pdfPicker.launch(arrayOf("application/pdf"))
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = selectedFiles.size < 10
                ) {
                    Icon(Icons.Default.PictureAsPdf, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(stringResource(R.string.pdfs))
                }
                
                // ZIP selection button
                Button(
                    onClick = {
                        if (selectedFiles.size >= 10) {
                            // Show toast or snackbar about limit
                            Toast.makeText(
                                context,
                                "Maximum of 10 files allowed. Please remove some files first.",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            zipPicker.launch("application/zip")
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = selectedFiles.size < 10
                ) {
                    Icon(Icons.Default.Folder, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(stringResource(R.string.zip))
                }
            }
            
            // File count indicator
            if (selectedFiles.isNotEmpty()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.files_count, selectedFiles.size),
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    if (selectedFiles.size >= 8) {
                        Text(
                            text = stringResource(R.string.approaching_limit),
                            style = MaterialTheme.typography.bodySmall,
                            color = if (selectedFiles.size >= 10) 
                                MaterialTheme.colorScheme.error 
                            else 
                                MaterialTheme.colorScheme.tertiary
                        )
                    }
                }
            }
            
            // Selected files preview
            if (selectedFiles.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.selected_files, selectedFiles.size),
                    style = MaterialTheme.typography.titleMedium
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(selectedFiles) { file ->
                        Card(
                            modifier = Modifier
                                .width(160.dp)
                                .height(200.dp),
                            border = BorderStroke(
                                width = 2.dp,
                                color = if (file.isSelected) 
                                    MaterialTheme.colorScheme.primary 
                                else 
                                    Color.Transparent
                            )
                        ) {
                            Box(modifier = Modifier.fillMaxSize()) {
                                if (file.isPdf && thumbnailsMap[file.id] != null) {
                                    // Show PDF thumbnail
                                    Image(
                                        bitmap = thumbnailsMap[file.id]!!.asImageBitmap(),
                                        contentDescription = "PDF Thumbnail",
                                        modifier = Modifier.fillMaxSize()
                                    )
                                } else if (file.isPdf) {
                                    // Show PDF icon if no thumbnail
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(MaterialTheme.colorScheme.surfaceVariant),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.PictureAsPdf,
                                            contentDescription = "PDF File",
                                            modifier = Modifier.size(64.dp),
                                            tint = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                } else {
                                    // Show image
                                    AsyncImage(
                                        model = file.uri,
                                        contentDescription = "Selected Image",
                                        modifier = Modifier.fillMaxSize()
                                    )
                                }
                                
                                // Processing indicator
                                if (file.isProcessing) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.Black.copy(alpha = 0.5f)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        CircularProgressIndicator(
                                            color = MaterialTheme.colorScheme.onPrimary
                                        )
                                    }
                                }
                                
                                // Error indicator
                                if (file.error != null) {
                                    Box(
                                        modifier = Modifier
                                            .align(Alignment.TopCenter)
                                            .padding(4.dp)
                                            .size(24.dp)
                                            .background(MaterialTheme.colorScheme.error, CircleShape),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Clear,
                                            contentDescription = "Error",
                                            tint = MaterialTheme.colorScheme.onError,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                    
                                    // Error tooltip (shown on hover in a real app)
                                    if (file.error.length > 10) {
                                        Box(
                                            modifier = Modifier
                                                .align(Alignment.Center)
                                                .fillMaxWidth()
                                                .padding(8.dp)
                                                .background(
                                                    MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.9f),
                                                    MaterialTheme.shapes.small
                                                )
                                                .padding(8.dp)
                                        ) {
                                            Text(
                                                text = "Error processing file",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onErrorContainer
                                            )
                                        }
                                    }
                                }
                                
                                // File name display at bottom
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.BottomCenter)
                                        .fillMaxWidth()
                                        .background(Color.Black.copy(alpha = 0.7f))
                                        .padding(4.dp)
                                ) {
                                    Text(
                                        text = when {
                                            file.fileName.isNotEmpty() -> file.fileName
                                            file.isPdf -> "PDF Document"
                                            else -> "Image"
                                        },
                                        color = Color.White,
                                        style = MaterialTheme.typography.bodySmall,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis
                                    )
                                }
                                
                                // Selection toggle
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.TopStart)
                                        .padding(4.dp)
                                        .size(32.dp)
                                        .background(
                                            color = if (file.isSelected) 
                                                MaterialTheme.colorScheme.primary 
                                            else 
                                                MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
                                            shape = CircleShape
                                        )
                                        .clickable {
                                            // Toggle selection
                                            selectedFiles = selectedFiles.map {
                                                if (it.id == file.id) it.copy(isSelected = !it.isSelected)
                                                else it
                                            }
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (file.isSelected) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = "Selected",
                                            tint = MaterialTheme.colorScheme.onPrimary,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                                
                                // Remove button
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(4.dp)
                                        .size(32.dp)
                                        .background(
                                            color = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.8f),
                                            shape = CircleShape
                                        )
                                        .clickable {
                                            // Remove file
                                            selectedFiles = selectedFiles.filter { it.id != file.id }
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "Remove File",
                                        tint = MaterialTheme.colorScheme.onErrorContainer,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }
                }
                
                // Extracted text from each file
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = stringResource(R.string.extracted_text),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(top = 16.dp)
                    )
                    
                    selectedFiles.forEach { file ->
                        Card(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    if (file.isPdf) {
                                        Icon(
                                            imageVector = Icons.Default.PictureAsPdf,
                                            contentDescription = "PDF",
                                            tint = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.size(24.dp)
                                        )
                                    } else {
                                        Icon(
                                            imageVector = Icons.Default.Add,
                                            contentDescription = "Image",
                                            tint = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.size(24.dp)
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Text(
                                        text = if (file.isPdf) stringResource(R.string.pdf_document) else stringResource(R.string.image),
                                        style = MaterialTheme.typography.titleSmall,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                when {
                                    file.isProcessing -> {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(16.dp),
                                                strokeWidth = 2.dp
                                            )
                                            Text(stringResource(R.string.extracting_text))
                                        }
                                    }
                                    file.error != null -> {
                                        Text(
                                            text = file.error,
                                            color = MaterialTheme.colorScheme.error
                                        )
                                    }
                                    file.extractedText.isNullOrBlank() -> {
                                        Text(stringResource(R.string.no_text_detected))
                                    }
                                    else -> {
                                        Text(file.extractedText)
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Analyze button for holistic analysis
                if (selectedFiles.isNotEmpty() && selectedFiles.all { !it.isProcessing } && !isProcessingBatch) {
                    val selectedCount = selectedFiles.count { it.isSelected }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Show selection count
                        Text(
                            text = stringResource(R.string.files_selected_for_analysis, selectedCount, selectedFiles.size),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        // Add select all / unselect all button
                        TextButton(
                            onClick = {
                                val allSelected = selectedFiles.all { it.isSelected }
                                selectedFiles = selectedFiles.map { 
                                    it.copy(isSelected = !allSelected) 
                                }
                            }
                        ) {
                            Text(
                                if (selectedFiles.all { it.isSelected }) stringResource(R.string.unselect_all) else stringResource(R.string.select_all)
                            )
                        }
                    }
                    
                    Button(
                        onClick = {
                            // Check if premium is activated
                            if (!isPremiumActivated) {
                                showPremiumDialog = true
                                return@Button
                            }
                            
                            // Get only selected files' extracted text and file types
                            val selectedOnlyFiles = selectedFiles.filter { it.isSelected }
                            
                            if (selectedOnlyFiles.isEmpty()) {
                                Toast.makeText(
                                    context,
                                    "Please select at least one file to analyze",
                                    Toast.LENGTH_SHORT
                                ).show()
                                return@Button
                            }
                            
                            val extractedTexts = selectedOnlyFiles.map { it.extractedText ?: "No text detected" }
                            val fileTypes = selectedOnlyFiles.map { 
                                when {
                                    it.isPdf -> "PDF" 
                                    it.isFromZip -> "ZIP/${it.fileName}"
                                    else -> "Image"
                                }
                            }
                            
                            // Use ViewModel to analyze
                            viewModel.analyzeMedicalReports(extractedTexts, fileTypes)
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isAnalyzing && !isProcessingBatch && 
                                selectedFiles.isNotEmpty() && 
                                selectedFiles.all { !it.isProcessing } &&
                                selectedFiles.any { it.isSelected }
                    ) {
                        Text(stringResource(R.string.analyze_selected_files))
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Arabic explanation button
                    Button(
                        onClick = {
                            // Check if premium is activated
                            if (!isPremiumActivated) {
                                showPremiumDialog = true
                                return@Button
                            }
                            
                            // Get only selected files' extracted text and file types
                            val selectedOnlyFiles = selectedFiles.filter { it.isSelected }
                            
                            if (selectedOnlyFiles.isEmpty()) {
                                Toast.makeText(
                                    context,
                                    "Please select at least one file to analyze",
                                    Toast.LENGTH_SHORT
                                ).show()
                                return@Button
                            }
                            
                            val extractedTexts = selectedOnlyFiles.map { it.extractedText ?: "No text detected" }
                            val fileTypes = selectedOnlyFiles.map { 
                                when {
                                    it.isPdf -> "PDF" 
                                    it.isFromZip -> "ZIP/${it.fileName}"
                                    else -> "Image"
                                }
                            }
                            
                            // Use ViewModel to analyze in Arabic
                            viewModel.analyzeMedicalReportsInArabic(extractedTexts, fileTypes)
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isAnalyzing && !isProcessingBatch && 
                                selectedFiles.isNotEmpty() && 
                                selectedFiles.all { !it.isProcessing } &&
                                selectedFiles.any { it.isSelected },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Text("شرح الكلام بالعربي")
                    }
                }
                
                // Loading indicator for holistic analysis
                if (isAnalyzing) {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            CircularProgressIndicator()
                            Text(
                                text = stringResource(R.string.generating_analysis),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
                
                // Error message for holistic analysis
                if (analysisError != null) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = analysisError!!,
                            modifier = Modifier.padding(16.dp),
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
                
                // Display holistic analysis if available
                if (holisticAnalysis != null && holisticAnalysis!!.isNotEmpty()) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "SmartMed Analysis",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = holisticAnalysis!!,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // Bottom actions row
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // Share button
                                Button(
                                    onClick = {
                                        val shareText = """
                                            SmartMed Analysis Results
                                            
                                            $holisticAnalysis
                                            
                                            Analyzed ${selectedFiles.size} files:
                                            ${selectedFiles.mapIndexed { index, file -> 
                                                "${index + 1}. ${if (file.isPdf) "PDF" else "Image"}" 
                                            }.joinToString("\n")}
                                        """.trimIndent()
                                        
                                        TextSharing.shareText(shareText, context, "SmartMed Analysis Results")
                                    },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(Icons.Default.Share, contentDescription = null)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(stringResource(R.string.share_results))
                                }
                                
                                // Send to PC button (if connected)
                                if (isPcConnected) {
                                    Button(
                                        onClick = {
                                            pcServerUrl?.let { serverUrl ->
                                                val shareText = """
                                                    SmartMed Analysis Results
                                                    
                                                    $holisticAnalysis
                                                    
                                                    Analyzed ${selectedFiles.size} files:
                                                    ${selectedFiles.mapIndexed { index, file -> 
                                                        "${index + 1}. ${if (file.isPdf) "PDF" else "Image"}" 
                                                    }.joinToString("\n")}
                                                """.trimIndent()
                                                
                                                isSendingToPC = true
                                                pcConnectionStatus = "Sending to PC..."
                                                
                                                scope.launch {
                                                    val result = sendTextToPC(shareText, serverUrl)
                                                    isSendingToPC = false
                                                    pcConnectionStatus = result.fold(
                                                        onSuccess = { it },
                                                        onFailure = { "Error: ${it.message}" }
                                                    )
                                                }
                                            }
                                        },
                                        enabled = !isSendingToPC,
                                        modifier = Modifier.fillMaxWidth(),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = MaterialTheme.colorScheme.tertiary
                                        )
                                    ) {
                                        if (isSendingToPC) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(24.dp),
                                                strokeWidth = 2.dp,
                                                color = MaterialTheme.colorScheme.onTertiary
                                            )
                                        } else {
                                            Icon(Icons.Default.Send, contentDescription = null)
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text("Send to PC")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Show PC connection card if connected
                if (isPcConnected) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.tertiaryContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "Connected to PC",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onTertiaryContainer
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "Server: $pcServerUrl",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onTertiaryContainer
                            )
                            
                            // Show connection status if available
                            pcConnectionStatus?.let { status ->
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = status,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (status.contains("Error") || status.contains("Failed"))
                                            MaterialTheme.colorScheme.error
                                        else
                                            MaterialTheme.colorScheme.onTertiaryContainer
                                )
                            }
                        }
                    }
                }
            }
            
            // Bottom buttons row
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Share button
                Button(
                    onClick = {
                        val shareText = """
                            SmartMed Analysis Results
                            
                            $holisticAnalysis
                            
                            Analyzed ${selectedFiles.size} files:
                            ${selectedFiles.mapIndexed { index, file -> 
                                "${index + 1}. ${if (file.isPdf) "PDF" else "Image"}" 
                            }.joinToString("\n")}
                        """.trimIndent()
                        
                        TextSharing.shareText(shareText, context, "SmartMed Analysis Results")
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Share, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(stringResource(R.string.share_results))
                }
                
                // Send to PC button (if connected)
                if (isPcConnected) {
                    Button(
                        onClick = {
                            pcServerUrl?.let { serverUrl ->
                                val shareText = """
                                    SmartMed Analysis Results
                                    
                                    $holisticAnalysis
                                    
                                    Analyzed ${selectedFiles.size} files:
                                    ${selectedFiles.mapIndexed { index, file -> 
                                        "${index + 1}. ${if (file.isPdf) "PDF" else "Image"}" 
                                    }.joinToString("\n")}
                                """.trimIndent()
                                
                                isSendingToPC = true
                                pcConnectionStatus = "Sending to PC..."
                                
                                scope.launch {
                                    val result = sendTextToPC(shareText, serverUrl)
                                    isSendingToPC = false
                                    pcConnectionStatus = result.fold(
                                        onSuccess = { it },
                                        onFailure = { "Error: ${it.message}" }
                                    )
                                }
                            }
                        },
                        enabled = !isSendingToPC,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.tertiary
                        )
                    ) {
                        if (isSendingToPC) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onTertiary
                            )
                        } else {
                            Icon(Icons.Default.Send, contentDescription = null)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Send to PC")
                        }
                    }
                }
            }
        }
    }
    
    // Doctor Email Dialog
    if (showDoctorDialog) {
        // Move selectedTab declaration outside the dialog content
        var selectedTab by remember { mutableStateOf(0) }
        
        AlertDialog(
            onDismissRequest = { showDoctorDialog = false },
            title = { Text(stringResource(R.string.share_medical_analysis)) },
            text = {
                Column {
                    // Tabs for different sharing options
                    val tabs = listOf(
                        stringResource(R.string.email),
                        stringResource(R.string.my_doctors),
                        stringResource(R.string.other_apps)
                    )
                    
                    TabRow(selectedTabIndex = selectedTab) {
                        tabs.forEachIndexed { index, title ->
                            Tab(
                                selected = selectedTab == index,
                                onClick = { selectedTab = index },
                                text = { Text(title) },
                                icon = {
                                    when (index) {
                                        0 -> Icon(Icons.Default.Email, contentDescription = null)
                                        1 -> Icon(Icons.Default.Person, contentDescription = null)
                                        2 -> Icon(Icons.Default.Send, contentDescription = null)
                                    }
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    when (selectedTab) {
                        0 -> {
                            // Email tab
                            OutlinedTextField(
                                value = doctorEmail,
                                onValueChange = { doctorEmail = it },
                                label = { Text(stringResource(R.string.doctors_email)) },
                                modifier = Modifier.fillMaxWidth(),
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)
                            )
                            
                            if (sendSuccess == true) {
                                Text(
                                    stringResource(R.string.analysis_sent_success),
                                    color = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.padding(top = 8.dp)
                                )
                            } else if (sendSuccess == false) {
                                Text(
                                    stringResource(R.string.analysis_send_failed),
                                    color = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.padding(top = 8.dp)
                                )
                            }
                        }
                        1 -> {
                            // My Doctors tab
                            var showDoctorsList by remember { mutableStateOf(true) }
                            
                            if (showDoctorsList) {
                                Box(
                                    modifier = Modifier
                                        .height(300.dp)
                                        .fillMaxWidth()
                                ) {
                                    SavedDoctorsList(
                                        viewModel = viewModel,
                                        selectionMode = true,
                                        onSelectionDone = {
                                            showDoctorsList = false
                                        }
                                    )
                                }
                            } else {
                                // Show selected doctors
                                val selectedDoctors by viewModel.selectedDoctors.collectAsState()
                                
                                Column {
                                    Text(
                                        stringResource(R.string.selected_doctors, selectedDoctors.size),
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    selectedDoctors.forEach { doctor ->
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 4.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                Icons.Default.Person,
                                                contentDescription = null,
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                            Spacer(modifier = Modifier.width(8.dp))
                                            Text("Dr. ${doctor.doctorName}")
                                        }
                                    }
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    TextButton(
                                        onClick = {
                                            showDoctorsList = true
                                        }
                                    ) {
                                        Text(stringResource(R.string.change_selection))
                                    }
                                }
                            }
                        }
                        2 -> {
                            // Other Apps tab
                            Text(
                                stringResource(R.string.share_via_other_apps),
                                style = MaterialTheme.typography.bodyMedium
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                stringResource(R.string.share_menu_description),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // WhatsApp button
                                Button(
                                    onClick = {
                                        viewModel.shareViaWhatsApp(context)
                                        showDoctorDialog = false
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFF25D366) // WhatsApp green
                                    )
                                ) {
                                    Icon(Icons.Default.Send, contentDescription = null)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(stringResource(R.string.whatsapp))
                                }
                                
                                // Other apps button
                                Button(
                                    onClick = {
                                        viewModel.shareAnalysisViaExternalApp(context, null)
                                        showDoctorDialog = false
                                    },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(Icons.Default.Share, contentDescription = null)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(stringResource(R.string.other_apps))
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                // Collect state outside of the when expression
                val selectedDoctors by viewModel.selectedDoctors.collectAsState()
                
                Button(
                    onClick = {
                        when (selectedTab) {
                            0 -> {
                                // Send via email
                                if (doctorEmail.isNotEmpty()) {
                                    viewModel.sendAnalysisToDoctor(doctorEmail)
                                }
                            }
                            1 -> {
                                // Send to selected doctors
                                viewModel.sendAnalysisToSelectedDoctors()
                            }
                            2 -> {
                                // No action needed here as we have dedicated buttons
                                showDoctorDialog = false
                            }
                        }
                    },
                    enabled = when (selectedTab) {
                        0 -> !isSendingToDoctor && doctorEmail.isNotEmpty()
                        1 -> !isSendingToDoctor && selectedDoctors.isNotEmpty()
                        2 -> false // Hide the button for the third tab as we have dedicated buttons
                        else -> true
                    },
                    modifier = if (selectedTab != 2) Modifier else Modifier.size(0.dp)
                ) {
                    if (isSendingToDoctor) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            when (selectedTab) {
                                0 -> stringResource(R.string.send)
                                1 -> stringResource(R.string.send_to_doctors, selectedDoctors.size)
                                else -> ""
                            }
                        )
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showDoctorDialog = false
                        viewModel.clearDoctorSelection()
                    },
                    modifier = if (selectedTab != 2) Modifier else Modifier.size(0.dp)
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    // Contact filter dialog for WhatsApp exports
    if (showContactFilterMenu && isWhatsAppExport) {
        // Extract senders when dialog is shown
        LaunchedEffect(showContactFilterMenu) {
            if (showContactFilterMenu && currentZipUri != null) {
                try {
                    val senders = extractSendersFromChat(context, currentZipUri!!)
                    extractedSenders = senders
                } catch (e: Exception) {
                    Log.e("MultiImageAnalyzer", "Error extracting senders: ${e.message}")
                }
            }
        }
        
        AlertDialog(
            onDismissRequest = { showContactFilterMenu = false },
            title = { Text(stringResource(R.string.select_contact)) },
            text = {
                if (extractedSenders.isEmpty()) {
                    // Show loading indicator if senders are not yet extracted
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(modifier = Modifier.size(36.dp))
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(stringResource(R.string.loading_contacts))
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 300.dp)
                    ) {
                        items(extractedSenders) { sender ->
                            // Capture string resource outside of clickable lambda
                            val errorMessage = stringResource(R.string.failed_filter_contact)
                            
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        // Apply filter for this sender
                                        // Create a pattern to match messages from this sender
                                        val senderPattern = "$sender:.*?(?:\\(file attached\\))".toRegex(RegexOption.DOT_MATCHES_ALL)
                                        
                                        // First pass: find files from this sender
                                        val filesFromSender = mutableSetOf<String>()
                                        scope.launch {
                                            try {
                                                val inputStream = context.contentResolver.openInputStream(currentZipUri!!)
                                                val zipInputStream = ZipInputStream(inputStream!!.buffered())
                                                
                                                var entry = zipInputStream.nextEntry
                                                while (entry != null) {
                                                    if (!entry.isDirectory) {
                                                        val name = entry.name
                                                        if (name.endsWith("_chat.txt", ignoreCase = true) || 
                                                            name.contains("WhatsApp Chat", ignoreCase = true) ||
                                                            name.contains("chat", ignoreCase = true)) {
                                                            val chatText = zipInputStream.bufferedReader().readText()
                                                            val lines = chatText.split("\n")
                                                            
                                                            // Process lines to find files sent by this sender
                                                            for (i in lines.indices) {
                                                                val line = lines[i]
                                                                if (line.contains("$sender:", ignoreCase = true) && 
                                                                    (i+1 < lines.size && lines[i+1].contains("(file attached)", ignoreCase = true))) {
                                                                    
                                                                    // Extract filename using regex or simple string matching
                                                                    val attachmentPattern = Pattern.compile("([A-Za-z0-9_-]+\\.(jpg|jpeg|png|pdf))\\s*\\(file attached\\)")
                                                                    val matcher = attachmentPattern.matcher(lines[i+1])
                                                                    if (matcher.find()) {
                                                                        val fileName = matcher.group(1).trim()
                                                                        filesFromSender.add(fileName)
                                                                    }
                                                                }
                                                            }
                                                            break
                                                        }
                                                    }
                                                    zipInputStream.closeEntry()
                                                    entry = zipInputStream.nextEntry
                                                }
                                                
                                                zipInputStream.close()
                                                inputStream.close()
                                                
                                                // Second pass: Update UI on the main thread
                                                withContext(Dispatchers.Main) {
                                                    zipContents = zipContents.map { file ->
                                                        file.copy(isSelected = filesFromSender.contains(file.fileName))
                                                    }
                                                    updateZipContentViews(zipContents, folderStructure, dateSortedFiles)
                                                    showContactFilterMenu = false
                                                }
                                            } catch (e: Exception) {
                                                Log.e("ContactFilter", "Error filtering by contact: ${e.message}", e)
                                                
                                                withContext(Dispatchers.Main) {
                                                    Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT).show()
                                                    showContactFilterMenu = false
                                                }
                                            }
                                        }
                                    }
                                    .padding(16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Person,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(24.dp)
                                )
                                
                                Spacer(modifier = Modifier.width(16.dp))
                                
                                Text(sender, style = MaterialTheme.typography.bodyLarge)
                            }
                        }
                    }
                }
            },
            confirmButton = {},
            dismissButton = {
                TextButton(onClick = { showContactFilterMenu = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    // Premium feature dialog
    if (showPremiumDialog) {
        AlertDialog(
            onDismissRequest = { showPremiumDialog = false },
            title = { Text(stringResource(R.string.premium_features)) },
            text = { 
                Column {
                    Text(stringResource(R.string.premium_features_description))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        stringResource(R.string.text_extraction_free),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showPremiumDialog = false
                        // Navigate to settings screen
                        onNavigateBack()
                        // Send user to settings screen
                        val intent = Intent(context, MainActivity::class.java).apply {
                            putExtra("NAVIGATE_TO_SETTINGS", true)
                        }
                        context.startActivity(intent)
                    }
                ) {
                    Text(stringResource(R.string.go_to_settings))
                }
            },
            dismissButton = {
                TextButton(onClick = { showPremiumDialog = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

/**
 * Extract contents of a ZIP file 
 * @param uri The URI of the ZIP file
 * @param context Android context for accessing content resolver
 */
private suspend fun extractZipContents(
    uri: Uri, 
    context: Context,
    // Pass the state values as parameters
    onProgress: (Float) -> Unit,
    onComplete: (List<AnalyzedFile>) -> Unit,
    onError: (String) -> Unit,
    onStartExtraction: () -> Unit,
    onFinishExtraction: () -> Unit,
    onWhatsAppExportDetected: () -> Unit
) {
    onStartExtraction()
    
    withContext(Dispatchers.IO) {
        try {
            // Open ZIP input stream
            val inputStream = context.contentResolver.openInputStream(uri)
            if (inputStream == null) {
                withContext(Dispatchers.Main) {
                    onError("Could not open ZIP file")
                    onFinishExtraction()
                }
                return@withContext
            }
            
            val zipInputStream = ZipInputStream(inputStream.buffered())
            val extractedFiles = mutableListOf<AnalyzedFile>()
            val tempDir = File(context.cacheDir, "zip_extracts_${System.currentTimeMillis()}")
            tempDir.mkdirs()
            
            // First scan the ZIP for text files that might contain date information
            var datePatternMap = mutableMapOf<String, Long>()
            val zipEntries = mutableListOf<ZipEntry>()
            var isWhatsAppChat = false
            var chatSendersFound = mutableSetOf<String>()
            
            // First pass: Scan for metadata files
            var entry = zipInputStream.nextEntry
            while (entry != null) {
                if (!entry.isDirectory) {
                    val name = entry.name
                    
                    // Check if this is a WhatsApp export by looking for chat text files
                    if (name.endsWith("_chat.txt", ignoreCase = true) || 
                        name.contains("WhatsApp Chat", ignoreCase = true) ||
                        name.contains("chat", ignoreCase = true)) {
                        
                        isWhatsAppChat = true
                        Log.d("WhatsAppParser", "Found WhatsApp chat file: $name")
                        
                        // Read the chat file to extract dates for files
                        val chatText = zipInputStream.bufferedReader().readText()
                        parseWhatsAppChatDates(chatText, datePatternMap)
                        
                        // Extract sender names for filtering
                        val senderPattern = Pattern.compile("\\d{1,2}/\\d{1,2}/\\d{2,4},\\s+\\d{1,2}:\\d{2}(?::\\d{2})?(?:\\s*[AP]M)?\\s*-\\s*([^:]+):")
                        
                        val matcher = senderPattern.matcher(chatText)
                        while (matcher.find()) {
                            val sender = matcher.group(1).trim()
                            if (sender.isNotEmpty()) {
                                chatSendersFound.add(sender)
                            }
                        }
                    }
                    
                    // Add supported files to our list for extraction
                    if (name.endsWith(".pdf", ignoreCase = true) || 
                        name.endsWith(".jpg", ignoreCase = true) ||
                        name.endsWith(".jpeg", ignoreCase = true) ||
                        name.endsWith(".png", ignoreCase = true)) {
                        zipEntries.add(entry)
                    }
                }
                zipInputStream.closeEntry()
                entry = zipInputStream.nextEntry
            }
            
            // Close and reopen ZIP for actual extraction
            zipInputStream.close()
            inputStream.close()
            
            if (zipEntries.isEmpty()) {
                withContext(Dispatchers.Main) {
                    onError("No supported files found in ZIP")
                    onFinishExtraction()
                }
                return@withContext
            }
            
            // Update the isWhatsAppExport flag
            if (isWhatsAppChat) {
                withContext(Dispatchers.Main) {
                    onWhatsAppExportDetected()
                }
            }
            
            // Start extraction
            val newInputStream = context.contentResolver.openInputStream(uri)
            val newZipInputStream = ZipInputStream(newInputStream!!.buffered())
            
            // Organize files by folder structure
            val folderStructure = mutableMapOf<String, MutableList<AnalyzedFile>>()
            
            var processedEntries = 0
            entry = newZipInputStream.nextEntry
            while (entry != null) {
                val currentEntry = entry
                val entryName = currentEntry.name
                
                if (!currentEntry.isDirectory) {
                    if (entryName.endsWith(".pdf", ignoreCase = true) || 
                        entryName.endsWith(".jpg", ignoreCase = true) ||
                        entryName.endsWith(".jpeg", ignoreCase = true) ||
                        entryName.endsWith(".png", ignoreCase = true)) {
                        
                        val isPdf = entryName.endsWith(".pdf", ignoreCase = true)
                        
                        // Extract file to temp directory
                        val fileName = entryName.substring(entryName.lastIndexOf("/") + 1)
                        val tempFile = File(tempDir, fileName)
                        tempFile.outputStream().use { output ->
                            newZipInputStream.copyTo(output)
                        }
                        
                        // Create a content URI for the file 
                        val fileUri = Uri.fromFile(tempFile)
                        
                        // Determine folder path and date
                        val folderPath = if (entryName.contains("/")) {
                            entryName.substring(0, entryName.lastIndexOf("/"))
                        } else {
                            "root"
                        }
                        
                        // Look for date information from the chat text or use file timestamp
                        // Check for timestamp in the most detailed ways possible
                        var dateTimestamp = currentEntry.time
                        var foundInChatLog = false
                        
                        // Try to find exact filename match
                        if (datePatternMap.containsKey(fileName)) {
                            dateTimestamp = datePatternMap[fileName] ?: dateTimestamp
                            foundInChatLog = true
                            Log.d("WhatsAppParser", "Found exact match for $fileName: ${Date(dateTimestamp)}")
                        } 
                        // Try to find by partial match (some filenames may be truncated in chat)
                        else {
                            for ((chatFileName, timestamp) in datePatternMap) {
                                // Check if the ZIP filename contains the chat filename or vice versa
                                if (fileName.contains(chatFileName) || chatFileName.contains(fileName)) {
                                    dateTimestamp = timestamp
                                    foundInChatLog = true
                                    Log.d("WhatsAppParser", "Found partial match for $fileName via $chatFileName: ${Date(dateTimestamp)}")
                                    break
                                }
                            }
                        }
                        
                        // For WhatsApp export IMG-YYYYMMDD format, extract date from filename
                        if (!foundInChatLog && fileName.matches(Regex("IMG-\\d{8}-.*\\.(jpg|jpeg|png)"))) {
                            try {
                                val dateStr = fileName.substring(4, 12)  // Extract YYYYMMDD
                                val year = dateStr.substring(0, 4).toInt()
                                val month = dateStr.substring(4, 6).toInt() - 1  // Calendar month is 0-based
                                val day = dateStr.substring(6, 8).toInt()
                                
                                val calendar = Calendar.getInstance()
                                calendar.set(year, month, day, 12, 0, 0)  // Default to noon
                                calendar.set(Calendar.MILLISECOND, 0)
                                
                                dateTimestamp = calendar.timeInMillis
                                Log.d("WhatsAppParser", "Extracted date from filename $fileName: ${Date(dateTimestamp)}")
                            } catch (e: Exception) {
                                Log.e("WhatsAppParser", "Failed to parse date from filename: $fileName", e)
                            }
                        }
                        
                        // Create analyzed file object with the timestamp
                        val analyzedFile = AnalyzedFile(
                            uri = fileUri,
                            isPdf = isPdf,
                            isFromZip = true,
                            zipSource = uri.toString(),
                            fileName = fileName,
                            folderPath = folderPath,
                            dateTimestamp = dateTimestamp
                        )
                        
                        // Add to organized folder structure
                        if (!folderStructure.containsKey(folderPath)) {
                            folderStructure[folderPath] = mutableListOf()
                        }
                        folderStructure[folderPath]?.add(analyzedFile)
                        
                        extractedFiles.add(analyzedFile)
                        
                        // Update progress
                        processedEntries++
                        val progress = processedEntries.toFloat() / zipEntries.size.toFloat()
                        withContext(Dispatchers.Main) {
                            onProgress(progress)
                        }
                    }
                }
                
                newZipInputStream.closeEntry()
                entry = newZipInputStream.nextEntry
            }
            
            // Close resources
            newZipInputStream.close()
            newInputStream.close()
            
            // Sort files by date if available
            val sortedFiles = extractedFiles.sortedByDescending { it.dateTimestamp }
            
            // Update UI with extracted files
            withContext(Dispatchers.Main) {
                onComplete(sortedFiles)
                onFinishExtraction()
            }
        } catch (e: Exception) {
            Log.e("MultiImageAnalyzer", "ZIP extraction error: ${e.message}", e)
            withContext(Dispatchers.Main) {
                onError("Error extracting ZIP: ${e.message}")
                onFinishExtraction()
            }
        }
    }
}

/**
 * Parse dates from WhatsApp chat export text file
 */
private fun parseWhatsAppChatDates(chatText: String, datePatternMap: MutableMap<String, Long>) {
    try {
        // WhatsApp chat format typically has dates in format like [MM/DD/YY, HH:MM:SS] or MM/DD/YY, HH:MM AM/PM - Name: message
        val lines = chatText.split("\n")
        var currentTimestamp = System.currentTimeMillis()
        
        // Match both formats: [MM/DD/YY, HH:MM:SS] and MM/DD/YY, HH:MM AM/PM - Name: message
        val datePattern = Pattern.compile("(?:\\[)?(\\d{1,2}/\\d{1,2}/\\d{2,4}),\\s+(\\d{1,2}:\\d{2}(?::\\d{2})?(?:\\s*[AP]M)?)(?:\\])?\\s*-")
        
        // Match file attachments: filename.ext (file attached) or <attached: filename.ext>
        val attachmentPatterns = listOf(
            Pattern.compile("([A-Za-z0-9_-]+\\.(jpg|jpeg|png|pdf))\\s*\\(file attached\\)"),
            Pattern.compile("<attached:\\s+([^>]+)>"),
            Pattern.compile("([A-Za-z0-9_-]+\\.(jpg|jpeg|png|pdf))")
        )
        
        Log.d("DateParser", "Parsing WhatsApp chat with ${lines.size} lines")
        
        lines.forEach { line ->
            // Extract date if present
            val dateMatch = datePattern.matcher(line)
            if (dateMatch.find()) {
                // Parse date and time
                try {
                    val dateParts = dateMatch.group(1).split("/")
                    val timeStr = dateMatch.group(2)
                    
                    // Parse the time considering both 12-hour and 24-hour formats
                    val isPm = timeStr.contains("PM", ignoreCase = true)
                    val isAm = timeStr.contains("AM", ignoreCase = true)
                    
                    // Remove AM/PM for parsing
                    val cleanTimeStr = timeStr.replace(Regex("\\s*[AP]M"), "")
                    val timeParts = cleanTimeStr.split(":")
                    
                    // Create a Calendar for date manipulation
                    val calendar = Calendar.getInstance()
                    
                    // Parse month, day, year
                    val month = dateParts[0].toInt() - 1 // Calendar months are 0-based
                    val day = dateParts[1].toInt()
                    val year = if (dateParts[2].length == 2) 
                        // 2-digit year (e.g., 24 -> 2024)
                        if (dateParts[2].toInt() > 50) 1900 + dateParts[2].toInt() else 2000 + dateParts[2].toInt()
                    else 
                        // 4-digit year
                        dateParts[2].toInt()
                    
                    // Parse hour, minute
                    var hour = timeParts[0].toInt()
                    val minute = timeParts[1].toInt()
                    val second = if (timeParts.size > 2) timeParts[2].toInt() else 0
                    
                    // Adjust for AM/PM if in 12-hour format
                    if (isPm && hour < 12) {
                        hour += 12
                    } else if (isAm && hour == 12) {
                        hour = 0
                    }
                    
                    calendar.set(year, month, day, hour, minute, second)
                    currentTimestamp = calendar.timeInMillis
                    
                    Log.d("DateParser", "Found date: ${calendar.time} for line: $line")
                    
                    // Look for attachments in this line
                    for (pattern in attachmentPatterns) {
                        val matcher = pattern.matcher(line)
                        if (matcher.find()) {
                            val fileName = matcher.group(1).trim()
                            // Save the timestamp for this file
                            datePatternMap[fileName] = currentTimestamp
                            Log.d("DateParser", "Mapped file $fileName to timestamp $currentTimestamp (${calendar.time})")
                            break
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e("DateParser", "Failed to parse date: ${e.message}", e)
                }
            } else {
                // If no date in this line, check if it's a continuation with a file attachment
                for (pattern in attachmentPatterns) {
                    val matcher = pattern.matcher(line)
                    if (matcher.find()) {
                        val fileName = matcher.group(1).trim()
                        // Use the last found timestamp
                        datePatternMap[fileName] = currentTimestamp
                        Log.d("DateParser", "Mapped file $fileName to timestamp $currentTimestamp from continuation line")
                        break
                    }
                }
            }
        }
        
        Log.d("DateParser", "Completed parsing. Mapped ${datePatternMap.size} files to dates")
    } catch (e: Exception) {
        Log.e("DateParser", "Error parsing chat text: ${e.message}", e)
    }
}

/**
 * Updates both folder structure and date views with latest selection states
 */
private fun updateZipContentViews(
    updatedContents: List<AnalyzedFile>,
    folderStructureMap: MutableMap<String, List<AnalyzedFile>>,
    dateViewMap: MutableMap<String, List<AnalyzedFile>>
) {
    // Create a lookup map for quick reference
    val updatedFilesById = updatedContents.associateBy { it.id }
    
    // Update the folder structure map with new selection states
    folderStructureMap.replaceAll { _, filesInFolder ->
        filesInFolder.map { file ->
            // Find the updated version of this file
            updatedFilesById[file.id] ?: file
        }
    }
    
    // Update the date view map with new selection states
    dateViewMap.keys.forEach { date ->
        val filesOnDate = dateViewMap[date] ?: emptyList()
        dateViewMap[date] = filesOnDate.map { file ->
            // Find the updated version of this file
            updatedContents.find { it.id == file.id } ?: file
        }
    }
}

/**
 * Composable for displaying a folder in the ZIP explorer
 */
@Composable
private fun FolderListItem(
    folderName: String,
    fileCount: Int,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 8.dp)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.outline,
                shape = MaterialTheme.shapes.small
            )
            .clickable(onClick = onClick)
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Folder,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        
        Column(
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .weight(1f)
        ) {
            Text(
                text = folderName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = if (fileCount == 1) 
                    "$fileCount file" 
                else 
                    "$fileCount files",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = stringResource(R.string.open_folder),
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * Composable for displaying a file in the ZIP explorer
 */
@Composable
private fun FileListItem(
    file: AnalyzedFile,
    onToggleSelection: (AnalyzedFile) -> Unit,
    modifier: Modifier = Modifier
) {
    val isSelected = file.isSelected
    val isPdf = file.fileName.endsWith(".pdf", ignoreCase = true)
    val isImage = file.fileName.endsWith(".jpg", ignoreCase = true) || 
                 file.fileName.endsWith(".jpeg", ignoreCase = true) || 
                 file.fileName.endsWith(".png", ignoreCase = true)
    
    // File item card
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.secondaryContainer
            else 
                MaterialTheme.colorScheme.surface
        ),
        border = BorderStroke(
            width = 1.dp,
            color = if (isSelected) 
                MaterialTheme.colorScheme.primary
            else 
                MaterialTheme.colorScheme.outline
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Selection checkbox
            Checkbox(
                checked = isSelected,
                onCheckedChange = { onToggleSelection(file) },
                colors = CheckboxDefaults.colors(
                    checkedColor = MaterialTheme.colorScheme.primary,
                    checkmarkColor = MaterialTheme.colorScheme.onPrimary
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // File icon
            Icon(
                imageVector = if (isPdf) 
                    Icons.Default.PictureAsPdf 
                else 
                    Icons.Default.Image,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // File details
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = file.fileName,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (file.dateTimestamp > 0) {
                    Text(
                        text = java.text.SimpleDateFormat("MM/dd/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date(file.dateTimestamp)),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * Extract sender names from WhatsApp chat
 */
private suspend fun extractSendersFromChat(context: Context, zipUri: Uri?): List<String> {
    if (zipUri == null) return emptyList()
    
    return withContext(Dispatchers.IO) {
        try {
            val senders = mutableSetOf<String>()
            val inputStream = context.contentResolver.openInputStream(zipUri) ?: return@withContext emptyList<String>()
            val zipInputStream = ZipInputStream(inputStream.buffered())
            
            var entry = zipInputStream.nextEntry
            while (entry != null) {
                if (!entry.isDirectory) {
                    val name = entry.name
                    // Look for chat text file - with expanded patterns to match different WhatsApp export formats
                    if (name.endsWith("_chat.txt", ignoreCase = true) || 
                        name.contains("WhatsApp Chat", ignoreCase = true) ||
                        name.contains("chat", ignoreCase = true)) {
                        
                        val chatText = zipInputStream.bufferedReader().readText()
                        // Match common WhatsApp date format + sender pattern
                        val senderPattern = Pattern.compile("\\d{1,2}/\\d{1,2}/\\d{2,4},\\s+\\d{1,2}:\\d{2}(?::\\d{2})?(?:\\s*[AP]M)?\\s*-\\s*([^:]+):")
                        
                        val matcher = senderPattern.matcher(chatText)
                        while (matcher.find()) {
                            val sender = matcher.group(1).trim()
                            if (sender.isNotEmpty()) {
                                senders.add(sender)
                            }
                        }
                        
                        // Break early since we've found the chat file
                        break
                    }
                }
                zipInputStream.closeEntry()
                entry = zipInputStream.nextEntry
            }
            
            zipInputStream.close()
            inputStream.close()
            
            Log.d("WhatsAppParser", "Found ${senders.size} senders: ${senders.joinToString(", ")}")
            return@withContext senders.toList()
        } catch (e: Exception) {
            Log.e("WhatsAppParser", "Error extracting senders: ${e.message}", e)
            return@withContext emptyList<String>()
        }
    }
}

// Replace the toggleFileSelection function with a more efficient version
private fun toggleFileSelection(
    file: AnalyzedFile,
    zipContents: List<AnalyzedFile>,
    updateZipContents: (List<AnalyzedFile>) -> Unit,
    updateKey: () -> Unit
) {
    // Create a copy with just the single file's selection state changed
    val updatedContents = zipContents.map { existingFile ->
        if (existingFile.id == file.id) {
            existingFile.copy(isSelected = !existingFile.isSelected)
        } else {
            existingFile
        }
    }
    
    // Update the zipContents state
    updateZipContents(updatedContents)
    
    // Force recomposition of visible items only
    updateKey()
}

// Add this function to the file, outside of any composable function
private fun processImageBitmap(
    bitmap: Bitmap,
    fileName: String,
    currentFiles: List<AnalyzedFile>,
    onFilesUpdated: (List<AnalyzedFile>) -> Unit,
    context: Context
) {
    // Convert bitmap to byte array
    val outputStream = ByteArrayOutputStream()
    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
    val imageBytes = outputStream.toByteArray()
    
    // Create a temporary file to store the bitmap
    val tempFile = File.createTempFile("camera_image_", ".jpg", context.cacheDir).apply {
        deleteOnExit() // Delete when the app exits
        outputStream().use { fileOut ->
            fileOut.write(imageBytes)
        }
    }
    
    // Get URI for the file
    val imageUri = FileProvider.getUriForFile(
        context,
        "${context.packageName}.fileprovider",
        tempFile
    )
    
    // Create a new AnalyzedFile
    val newFile = AnalyzedFile(
        uri = imageUri,
        isPdf = false,
        isFromZip = false,
        fileName = fileName,
        extractedText = null,
        isProcessing = true,
        error = null,
        isSelected = true,
        dateTimestamp = System.currentTimeMillis()
    )
    
    // Add to current files
    val updatedFiles = currentFiles.toMutableList().apply {
        add(newFile)
    }
    
    // Update UI
    onFilesUpdated(updatedFiles)
    
    // Process the image using Gemini AI in a background thread
    CoroutineScope(Dispatchers.IO).launch {
        try {
            // Initialize Gemini model
            val geminiModel = GenerativeModel(
                modelName = "gemini-2.0-flash",
                apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk"
            )
            
            // Create content with image and prompt for text extraction
            val prompt = "Extract all text visible in this image. Return ONLY the extracted text, nothing else."
            
            val content = content {
                image(bitmap)
                text(prompt)
            }
            
            // Generate content
            val response = geminiModel.generateContent(content)
            val extractedText = response.text ?: "No text could be extracted from the image."
            
            // Update with extracted text
            val processedFile = newFile.copy(
                isProcessing = false,
                extractedText = extractedText
            )
            
            // Find and replace the processing file
            val finalList = updatedFiles.map {
                if (it.id == newFile.id) {
                    processedFile
                } else {
                    it
                }
            }
            
            // Update UI on main thread
            withContext(Dispatchers.Main) {
                onFilesUpdated(finalList)
            }
        } catch (e: Exception) {
            // Handle errors
            Log.e("MultiImageAnalyzer", "Error extracting text from camera image: ${e.message}", e)
            withContext(Dispatchers.Main) {
                onFilesUpdated(updatedFiles.map {
                    if (it.id == newFile.id) {
                        it.copy(
                            isProcessing = false,
                            error = "Error processing image: ${e.message}"
                        )
                    } else {
                        it
                    }
                })
            }
        }
    }
}