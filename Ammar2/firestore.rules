rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own documents
    match /users/{userId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && 
                    (request.auth.uid == userId || 
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'lastLogin']));
      allow delete: if request.auth != null && request.auth.uid == userId;
      
      // Allow users to manage their contacts (including anonymous users)
      match /contacts/{contactId} {
        allow read: if request.auth != null && request.auth.uid == userId;
        allow create, update: if request.auth != null && request.auth.uid == userId;
        allow delete: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Allow PC session management for QR code authentication
    match /pc_sessions/{sessionId} {
      // Allow creation by anyone (for PC app to create sessions)
      allow create: if true;
      
      // Allow reading by anyone (for mobile app to check sessions)
      allow read: if true;
      
      // Allow updates if:
      // 1. User is authenticated and updating their own session
      // 2. Or updating only the status field (for expiration)
      allow update: if (request.auth != null && 
                       (resource.data.userId == null || resource.data.userId == request.auth.uid)) ||
                      request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status']);
      
      // Allow deletion by anyone (cleanup)
      allow delete: if true;
    }
    
    // Allow users to send and receive messages
    match /messages/{messageId} {
      allow create: if request.auth != null && 
                     request.resource.data.senderUid == request.auth.uid;
      
      allow read: if request.auth != null && 
                   (resource.data.senderUid == request.auth.uid || 
                    resource.data.recipientUid == request.auth.uid);
      
      allow update: if request.auth != null && 
                     resource.data.recipientUid == request.auth.uid &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
      
      allow delete: if request.auth != null && 
                     (resource.data.senderUid == request.auth.uid || 
                      resource.data.recipientUid == request.auth.uid);
    }
    
    // Allow users to manage their extracted texts with TTL (Time To Live)
    match /extracted_texts/{documentId} {
      // Allow creation if user is authenticated, userId matches, and expirationTime is set
      allow create: if request.auth != null && 
                     request.resource.data.userId == request.auth.uid &&
                     request.resource.data.expirationTime is number &&
                     request.resource.data.expirationTime > request.time.toMillis() &&
                     // Ensure expiration is not more than 24 hours in the future (prevent abuse)
                     request.resource.data.expirationTime <= (request.time.toMillis() + 24 * 60 * 60 * 1000);
      
      // Allow reading if user is authenticated and they own the document
      allow read: if request.auth != null && 
                   resource.data.userId == request.auth.uid;
      
      // Allow updates if user is authenticated, they own the document, and not extending expiration
      allow update: if request.auth != null && 
                     resource.data.userId == request.auth.uid &&
                     (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['expirationTime']) ||
                      request.resource.data.expirationTime <= resource.data.expirationTime);
      
      // Allow deletion if user is authenticated and they own the document
      // Also allow system admin functions to delete expired documents
      allow delete: if request.auth != null && 
                     (resource.data.userId == request.auth.uid ||
                      // Allow deletion of expired documents (for cleanup functions)
                      resource.data.expirationTime < request.time.toMillis());
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
} 