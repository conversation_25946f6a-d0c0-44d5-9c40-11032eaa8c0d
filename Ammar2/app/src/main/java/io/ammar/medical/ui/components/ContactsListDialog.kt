package io.ammar.medical.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import io.ammar.medical.data.Contact

/**
 * Dialog to display a list of contacts
 */
@Composable
fun ContactsListDialog(
    onDismiss: () -> Unit,
    onSelectContact: (Contact) -> Unit,
    onAddContact: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Your Contacts") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    Button(
                        onClick = onAddContact
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Add Contact",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Add Contact")
                    }
                }
                
                // Display the contacts list
                ContactsList(
                    onSelectContact = onSelectContact,
                    modifier = Modifier.weight(1f)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("Close")
            }
        }
    )
} 