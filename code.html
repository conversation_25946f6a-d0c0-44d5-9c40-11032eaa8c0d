<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Details - Murmur3 System</title>
    <!-- Tailwind CSS - Production optimized -->
    <script>
        // Suppress Tailwind CDN warning in production
        if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
            const originalConsole = console.warn;
            console.warn = function(...args) {
                if (args[0] && args[0].includes && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
                    return; // Suppress this specific warning
                }
                originalConsole.apply(console, args);
            };
        }
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Layout System */
        .sidebar {
            width: 280px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border-right: 1px solid rgba(226, 232, 240, 0.8);
        }
        .main-content {
            margin-left: 280px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
        }

        /* Enhanced Card System */
        .modern-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 4px 16px rgba(0, 0, 0, 0.04);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899, #f59e0b);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .modern-card:hover::before {
            opacity: 1;
        }
        .modern-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(0, 0, 0, 0.08);
            border-color: rgba(99, 102, 241, 0.4);
        }

        /* Tab System */
        .tab-button {
            position: relative;
            padding: 14px 28px;
            border-radius: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            border: 2px solid transparent;
            backdrop-filter: blur(10px);
        }
        .tab-button:not(.active) {
            color: #64748b;
            background: rgba(255, 255, 255, 0.7);
            border-color: rgba(226, 232, 240, 0.5);
        }
        .tab-button:not(.active):hover {
            background: rgba(99, 102, 241, 0.15);
            color: #4f46e5;
            border-color: rgba(99, 102, 241, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
        }
        .tab-button.active {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            border-color: rgba(255, 255, 255, 0.2);
        }
        .tab-button.active::before {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 18px;
            padding: 2px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0.6;
        }

        /* Patient Avatar & Header */
        .patient-avatar {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            font-weight: 700;
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
            position: relative;
            overflow: hidden;
        }
        .patient-avatar::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 24px;
            padding: 2px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0.8;
        }
        .patient-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.5);
        }

        /* Enhanced Stats Cards */
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899, #f59e0b);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .stat-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        .stat-card:hover::before {
            opacity: 1;
        }
        .stat-card:hover::after {
            opacity: 1;
        }
        .stat-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08);
            border-color: rgba(99, 102, 241, 0.3);
        }

        /* Priority Indicators */
        .priority-critical { 
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
        }
        .priority-urgent { 
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            color: white;
        }
        .priority-standard { 
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
        }
        .priority-routine { 
            background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
            color: white;
        }

        /* Modern Audio Interface */
        .audio-recorder {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 3px dashed rgba(99, 102, 241, 0.4);
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        .audio-recorder::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .audio-recorder:hover::before {
            opacity: 1;
        }
        .audio-recorder.recording {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            animation: pulse-recording 2s infinite;
            transform: scale(1.02);
        }
        @keyframes pulse-recording {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
                border-color: #ef4444;
            }
            50% {
                box-shadow: 0 0 0 15px rgba(239, 68, 68, 0);
                border-color: #dc2626;
            }
        }

        /* Allergy & Medication Tags */
        .allergy-tag {
            background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .allergy-tag:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .medication-tag {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 1px solid #3b82f6;
            color: #1e40af;
            padding: 8px 14px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .medication-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        /* Consultation Cards */
        .consultation-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 5px solid transparent;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }
        .consultation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .consultation-card:hover::before {
            opacity: 0.6;
        }
        .consultation-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            border-color: rgba(99, 102, 241, 0.3);
        }
        .consultation-card.ctas-1 {
            border-left-color: #dc2626;
            color: #dc2626;
        }
        .consultation-card.ctas-2 {
            border-left-color: #ea580c;
            color: #ea580c;
        }
        .consultation-card.ctas-3 {
            border-left-color: #d97706;
            color: #d97706;
        }
        .consultation-card.ctas-4 {
            border-left-color: #2563eb;
            color: #2563eb;
        }
        .consultation-card.ctas-5 {
            border-left-color: #16a34a;
            color: #16a34a;
        }
        
        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 32px;
            right: 32px;
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            border: 3px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        .fab::before {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 50%;
            padding: 3px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .fab:hover::before {
            opacity: 1;
        }
        .fab:hover {
            transform: scale(1.15) rotate(90deg);
            box-shadow: 0 16px 48px rgba(99, 102, 241, 0.6);
        }
        .fab:active {
            transform: scale(1.05) rotate(90deg);
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                width: 100%;
                max-width: 320px;
            }
            .sidebar.active {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                padding: 16px;
            }
            .mobile-menu-button {
                display: block;
            }
            
            /* Enhanced Mobile Styles */
            .patient-avatar {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .modern-card {
                padding: 16px;
                margin-bottom: 16px;
            }
            
            .tab-button {
                padding: 8px 16px;
                font-size: 14px;
            }
            
            .tab-button span {
                display: none;
            }
            
            .fab {
                bottom: 80px;
                right: 16px;
            }
            
            #quickActionsMenu {
                right: 16px;
                bottom: 140px;
                left: 16px;
                width: auto;
            }
            
            /* Mobile Tab Navigation */
            .mobile-view .tab-content {
                padding-top: 60px;
            }
            
            .mobile-view .flex.flex-wrap.gap-2 {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #e5e7eb;
                padding: 12px;
                z-index: 40;
                justify-content: space-around;
            }
            
            .mobile-view .tab-button {
                flex: 1;
                justify-content: center;
                max-width: none;
                margin: 0;
            }
        }
        .mobile-menu-button {
            display: none;
        }
        
        /* Tab Content Styles */
        .tab-pane {
            transition: all 0.3s ease;
        }
        
        .tab-pane.hidden {
            opacity: 0;
            transform: translateY(10px);
        }
        
        .tab-pane.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Consultation styling */
        .consultation-content {
            line-height: 1.6;
        }
        .consultation-content h3 {
            color: #4338ca;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 0.5rem;
            margin-top: 1.5rem;
        }
        .consultation-content h4 {
            color: #4f46e5;
            margin-top: 1rem;
        }
        .consultation-content li {
            margin-bottom: 0.5rem;
        }
        .consultation-content .font-semibold {
            color: #1e40af;
        }

        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Triage Results Modal Animations */
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .animate-modalSlideIn {
            animation: modalSlideIn 0.3s ease-out forwards;
        }

        .animate-modalSlideOut {
            animation: modalSlideOut 0.3s ease-in forwards;
        }

        /* Custom scrollbar for modal content */
        .overflow-y-auto::-webkit-scrollbar {
            width: 6px;
        }

        .overflow-y-auto::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Collapsible sections styles */
        .section-header {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .section-header:hover {
            background-color: #f8fafc;
        }
        
        .section-content {
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .section-content.collapsed {
            max-height: 0;
            padding-top: 0;
            padding-bottom: 0;
            opacity: 0;
        }
        
        .section-content.expanded {
            max-height: 2000px;
            opacity: 1;
        }
        
        .chevron-icon {
            transition: transform 0.2s ease;
        }
        
        .chevron-icon.rotated {
            transform: rotate(180deg);
        }
        
        .compact-section {
            margin-bottom: 0.75rem;
        }
        
        .compact-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .compact-content {
            padding: 1rem;
        }

        /* Enhanced Loading Animations */
        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
        }

        /* Smooth Page Transitions */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Button Interactions */
        .interactive-button {
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
        }

        .interactive-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .interactive-button:active::before {
            width: 300px;
            height: 300px;
        }

        /* Micro-interactions for Icons */
        .icon-bounce {
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .icon-bounce:hover {
            transform: scale(1.2) rotate(5deg);
        }

        /* Progress Indicators */
        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }

        /* Enhanced Tooltips */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            bottom: 115%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .tooltip:hover::after,
        .tooltip:hover::before {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Menu Button -->
    <button id="mobileMenuBtn" class="mobile-menu-button fixed top-4 left-4 z-50 p-2 rounded-md bg-white shadow-lg">
        <i class="fas fa-bars text-gray-600"></i>
    </button>

    <!-- Mobile Back Button -->
    <button id="mobileBackBtn" class="mobile-menu-button fixed top-4 right-4 z-50 p-2 rounded-md bg-white shadow-lg">
        <i class="fas fa-arrow-left text-gray-600"></i>
    </button>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar fixed h-full bg-white shadow-lg">
        <div class="p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-bold text-gray-800">Murmur3</h1>
                    <p class="text-sm text-gray-600">Doctor Dashboard</p>
                </div>
                <button id="closeSidebarBtn" class="md:hidden p-2 rounded-md hover:bg-gray-100">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>
        </div>
        <nav class="mt-8">
            <a href="/dashboard.html" class="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-home w-6"></i>
                <span>Dashboard</span>
            </a>
            <a href="/dashboard.html" class="flex items-center px-4 py-2 text-gray-700 bg-gray-100">
                <i class="fas fa-users w-6"></i>
                <span>Patients</span>
            </a>
            <a href="/dashboard.html#patients" class="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-stethoscope w-6"></i>
                <span>New Consultation</span>
            </a>
            <a href="/analytics.html" class="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-chart-bar w-6"></i>
                <span>Analytics</span>
            </a>
            <a href="/dashboard.html#settings" class="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>Settings</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content p-6">
        <!-- Enhanced Header with Breadcrumbs -->
        <header class="mb-8">
            <!-- Breadcrumb Navigation -->
            <nav class="mb-4">
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <a href="dashboard.html" class="hover:text-blue-600 transition-colors">Dashboard</a>
                    <i class="fas fa-chevron-right text-xs"></i>
                    <a href="dashboard.html" class="hover:text-blue-600 transition-colors">Patients</a>
                    <i class="fas fa-chevron-right text-xs"></i>
                    <span class="text-gray-900 font-medium" id="breadcrumbPatientName">Patient Details</span>
                </div>
            </nav>

            <!-- Enhanced Patient Header Card -->
            <div class="modern-card p-6 mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <!-- Patient Info with Avatar -->
                    <div class="flex items-center gap-6">
                        <div class="patient-avatar" id="patientAvatar">
                            <i class="fas fa-user"></i>
                        </div>
                <div>
                            <h1 class="text-2xl font-bold text-gray-900 mb-1" id="patientHeaderName">Loading...</h1>
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-id-badge text-blue-500"></i>
                                    ID: <span id="patientHeaderId">-</span>
                                </span>
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-birthday-cake text-pink-500"></i>
                                    Age: <span id="patientHeaderAge">-</span>
                                </span>
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-venus-mars text-purple-500"></i>
                                    <span id="patientHeaderGender">-</span>
                                </span>
                </div>
                            <div class="mt-2">
                                <span class="priority-standard px-3 py-1 rounded-full text-sm font-medium" id="patientPriorityBadge">
                                    <i class="fas fa-flag mr-1"></i>Standard Priority
                                </span>
            </div>
                        </div>
                    </div>

                    <!-- Quick Actions & Credits -->
                    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                <!-- Credits Display -->
                        <div class="flex items-center gap-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-xl border border-blue-200">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-coins text-white"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-blue-800">Available Credits</div>
                                <div class="text-lg font-bold text-blue-900" id="creditsCount">--</div>
                            </div>
                            <button onclick="showCreditsModal()" class="text-blue-600 hover:text-blue-800 transition-colors">
                                <i class="fas fa-plus text-lg"></i>
                    </button>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex gap-2">
                            <button onclick="window.history.back()" class="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                                <i class="fas fa-arrow-left"></i>
                                <span class="hidden sm:inline">Back</span>
                            </button>
                        </div>
                    </div>
                </div>
                </div>
                
            <!-- Enhanced Stats Dashboard -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-stethoscope text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold text-gray-900" id="totalConsultations">0</span>
                    </div>
                    <h3 class="font-semibold text-gray-700">Consultations</h3>
                    <p class="text-sm text-gray-500">Total medical visits</p>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user-md text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold text-gray-900" id="totalTriages">0</span>
                    </div>
                    <h3 class="font-semibold text-gray-700">Triage Assessments</h3>
                    <p class="text-sm text-gray-500">Emergency evaluations</p>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-file-medical text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold text-gray-900" id="totalDocuments">0</span>
                    </div>
                    <h3 class="font-semibold text-gray-700">Documents</h3>
                    <p class="text-sm text-gray-500">Medical records</p>
                </div>

                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-pills text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold text-gray-900" id="totalMedications">0</span>
                    </div>
                    <h3 class="font-semibold text-gray-700">Medications</h3>
                    <p class="text-sm text-gray-500">Active prescriptions</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="flex flex-wrap gap-2 mb-6">
                <button class="tab-button active" onclick="switchTab('overview')" id="overviewTab">
                    <i class="fas fa-user-circle"></i>
                    <span>Overview</span>
                </button>
                <button class="tab-button" onclick="switchTab('clinical')" id="clinicalTab">
                    <i class="fas fa-stethoscope"></i>
                    <span>Clinical</span>
                </button>
                <button class="tab-button" onclick="switchTab('documents')" id="documentsTab">
                    <i class="fas fa-file-medical"></i>
                    <span>Documents</span>
                </button>
                <button class="tab-button" onclick="switchTab('reports')" id="reportsTab">
                    <i class="fas fa-chart-line"></i>
                    <span>Reports</span>
                </button>
            </div>
        </header>

        <!-- Tab Content Areas -->
        <div class="tab-content">
            <!-- Overview Tab -->
            <div id="overviewContent" class="tab-pane active">
                <!-- Enhanced Audio Triage Interface -->
                <div class="modern-card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-microphone text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Audio Triage</h3>
                                <p class="text-sm text-gray-600">AI-powered voice analysis and vital signs recording</p>
                            </div>
                        </div>
                        <button id="startTriageBtn" class="interactive-button flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all transform hover:scale-105 hover:shadow-lg tooltip" data-tooltip="Start AI-powered voice triage assessment">
                            <i class="fas fa-microphone icon-bounce"></i>
                            <span class="font-semibold">Start Triage</span>
                        </button>
                    </div>

                    <!-- Modern Audio Triage Interface -->
                    <div id="triageInterface" class="hidden space-y-6">
            <!-- Compact Header -->
            <div class="bg-green-50 border-b border-green-200 px-3 py-2 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-semibold text-green-700">
                        <i class="fas fa-microphone mr-1"></i>Audio Triage
                    </h3>
                    <button id="closeTriageBtn" class="text-green-600 hover:text-green-800 text-sm">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <!-- Ultra-Compact Content -->
            <div class="p-3">
                <!-- Vital Signs Section - Inline -->
                <div id="vitalSignsSection" class="mb-3">
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-3">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-xs font-semibold text-yellow-800">
                                <i class="fas fa-heartbeat mr-1"></i>Vital Signs
                            </h4>
                            <button id="skipVitalSignsBtn" class="text-xs text-yellow-600 hover:text-yellow-800">Skip</button>
                        </div>
                        
                        <!-- Ultra-Compact V/S Form -->
                        <form id="vitalSignsForm" class="space-y-2">
                            <div class="grid grid-cols-4 gap-1">
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">BP</label>
                                    <input type="number" id="systolicBp" min="50" max="300" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="120">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">/</label>
                                    <input type="number" id="diastolicBp" min="30" max="200" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="80">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">HR</label>
                                    <input type="number" id="heartRate" min="30" max="250" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="80">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">RR</label>
                                    <input type="number" id="respiratoryRate" min="5" max="50" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="16">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">Temp</label>
                                    <input type="number" id="temperature" min="30" max="45" step="0.1" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="37">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">O2</label>
                                    <input type="number" id="oxygenSaturation" min="70" max="100" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="98">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">RBS</label>
                                    <input type="number" id="randomBloodSugar" min="20" max="1000" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="120">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-0.5">Pain</label>
                                    <input type="number" id="painScale" min="1" max="10" class="w-full px-1 py-0.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-yellow-500" placeholder="3">
                                </div>
                            </div>
                            
                            <!-- Compact Voice Input Row -->
                            <div class="flex items-center space-x-2">
                                <button type="button" id="vitalSignsVoiceBtn" class="bg-yellow-600 text-white px-2 py-0.5 rounded text-xs hover:bg-yellow-700">
                                    <i class="fas fa-microphone mr-1"></i>Voice
                                </button>
                                <span id="vitalSignsVoiceStatus" class="text-xs text-gray-500">Click to record</span>
                                <button type="submit" class="ml-auto bg-yellow-600 text-white px-2 py-0.5 rounded text-xs hover:bg-yellow-700">
                                    Save
                                </button>
                            </div>
                            
                            <!-- Compact Voice Result -->
                            <div id="vitalSignsVoiceResult" class="hidden bg-white border border-yellow-200 rounded p-1">
                                <p class="text-xs font-medium text-gray-800 mb-0.5">Extracted:</p>
                                <div id="extractedVitalSigns" class="text-xs text-gray-600"></div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Compact Audio Recording Section -->
                <div class="bg-blue-50 border border-blue-200 rounded p-3">
                    <h4 class="text-xs font-semibold text-blue-800 mb-2">
                        <i class="fas fa-microphone mr-1"></i>Patient Recording
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <!-- Compact Recording Controls -->
                        <div class="text-center">
                            <div id="recordingControls">
                                <div id="microphoneButton" class="mx-auto w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-300 transform hover:scale-105">
                                    <i class="fas fa-microphone text-white text-lg"></i>
                                </div>
                                <p class="mt-1 text-xs text-gray-600">Click to record</p>
                                <div id="recordingStatus" class="hidden">
                                    <div class="flex items-center justify-center mt-1">
                                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-1"></div>
                                        <span class="text-red-600 text-xs font-medium">Recording...</span>
                                    </div>
                                    <div id="recordingTimer" class="text-gray-500 text-xs">00:00</div>
                                </div>
                            </div>
                            <div id="recordingComplete" class="hidden">
                                <i class="fas fa-check-circle text-green-500 text-xl"></i>
                                <p class="mt-1 text-green-600 text-xs font-medium">Complete</p>
                                <div class="mt-1 space-x-1">
                                    <button id="playRecordingBtn" class="px-2 py-0.5 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                                        <i class="fas fa-play mr-1"></i>Play
                                    </button>
                                    <button id="recordAgainBtn" class="px-2 py-0.5 bg-gray-600 text-white rounded text-xs hover:bg-gray-700">
                                        <i class="fas fa-redo mr-1"></i>Again
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Compact File Upload -->
                        <div class="text-center">
                            <div class="border-2 border-dashed border-blue-300 rounded p-2">
                                <input type="file" id="audioFileInput" accept="audio/*" class="hidden">
                                <label for="audioFileInput" class="cursor-pointer">
                                    <i class="fas fa-cloud-upload-alt text-xl text-blue-400 mb-1"></i>
                                    <p class="text-xs text-gray-600">Upload audio</p>
                                    <p class="text-xs text-gray-500">MP3, WAV, M4A</p>
                                </label>
                                <div id="fileSelected" class="hidden mt-1">
                                    <i class="fas fa-file-audio text-green-500 text-sm"></i>
                                    <p id="fileName" class="text-green-600 text-xs font-medium"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compact Analysis Button -->
                    <div class="mt-3 text-center">
                        <button id="analyzeAudioBtn" class="disabled:opacity-50 disabled:cursor-not-allowed px-4 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 font-medium text-xs" disabled>
                            <i class="fas fa-brain mr-1"></i>Analyze & Generate Triage
                        </button>
                        <p class="text-xs text-gray-500 mt-1">AI will analyze audio and generate triage notes</p>
                    </div>

                    <!-- Compact Analysis Progress -->
                    <div id="analysisProgress" class="hidden mt-2">
                        <div class="bg-blue-100 border border-blue-300 rounded p-2">
                            <div class="flex items-center">
                                <i class="fas fa-spinner fa-spin text-blue-600 mr-2"></i>
                                <span class="text-blue-800 text-xs font-medium">Analyzing audio...</span>
                            </div>
                            <div class="mt-1 text-xs text-blue-600">
                                Please don't close this page.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Enhanced Patient Medical Overview -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Allergies Card -->
                    <div class="modern-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                    </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Allergies</h3>
                                    <p class="text-sm text-gray-600">Known allergic reactions</p>
                </div>
            </div>
                    </div>
                        <div id="allergiesList" class="flex flex-wrap gap-2">
                            <!-- Allergies will be dynamically added here -->
                            <span class="text-sm text-gray-500 italic">No known allergies</span>
                    </div>
                    </div>

                    <!-- Current Medications Card -->
                    <div class="modern-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-pills text-white"></i>
                    </div>
                    <div>
                                    <h3 class="font-semibold text-gray-900">Current Medications</h3>
                                    <p class="text-sm text-gray-600">Active prescriptions</p>
                    </div>
                    </div>
                            <button onclick="showAddMedicationModal()" class="text-blue-600 hover:text-blue-800 transition-colors">
                                <i class="fas fa-plus text-lg"></i>
                            </button>
                </div>
                        <div id="medicationsList" class="space-y-2">
                            <!-- Medications will be dynamically added here -->
                            <span class="text-sm text-gray-500 italic">No medications recorded</span>
                        </div>
                    </div>

                    <!-- Chronic Conditions Card -->
                    <div class="modern-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-heartbeat text-white"></i>
                        </div>
                        <div>
                                    <h3 class="font-semibold text-gray-900">Chronic Conditions</h3>
                                    <p class="text-sm text-gray-600">Long-term health conditions</p>
                        </div>
                        </div>
                    </div>
                        <div id="patientConditions" class="text-sm text-gray-700">
                            <span class="text-gray-500 italic">None reported</span>
                </div>
            </div>
        </div>

        <!-- Global Time Filter Section - Compact & Collapsible -->
        <div class="bg-white rounded-lg shadow compact-section">
            <div class="section-header compact-header" onclick="toggleSection('timeFilter')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-filter text-blue-600 mr-2"></i>
                        <h3 class="text-base font-bold text-gray-800">Time Filter</h3>
                        <span class="ml-3 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full" id="filterSummary">All Time</span>
                    </div>
                    <i class="fas fa-chevron-down chevron-icon text-gray-400" id="timeFilterChevron"></i>
                </div>
            </div>
            
            <div class="section-content collapsed compact-content" id="timeFilterContent">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0">
                    <div>
                        <p class="text-sm text-gray-600">Filter all consultations, triages, and documents by date range</p>
                    </div>
                    
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3 w-full md:w-auto">
                        <select id="globalTimeFilter" class="w-full md:w-auto px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-sm">
                            <option value="all">All Time</option>
                            <option value="7days">Last 7 Days</option>
                            <option value="1month">Last Month</option>
                            <option value="3months">Last 3 Months</option>
                            <option value="6months">Last 6 Months</option>
                            <option value="1year">Last Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                        
                        <div id="customDateRangeFilter" class="hidden flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
                            <input type="date" id="filterStartDate" class="w-full md:w-auto px-2 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-sm">
                            <input type="date" id="filterEndDate" class="w-full md:w-auto px-2 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-sm">
                        </div>
                        
                        <button id="applyFilterBtn" class="w-full md:w-auto px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                            <i class="fas fa-search mr-1"></i>Apply
                        </button>
                        
                        <button id="clearFilterBtn" class="w-full md:w-auto px-3 py-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors hidden text-sm">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                    </div>
                </div>
                
                <!-- Filter Status Display -->
                <div id="filterStatus" class="hidden mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-600 mr-2 text-sm"></i>
                            <span id="filterStatusText" class="text-blue-800 font-medium text-sm"></span>
                        </div>
                        <div class="flex items-center space-x-3 text-xs text-blue-700">
                            <span>Consultations: <span id="filteredConsultationsCount" class="font-bold">0</span></span>
                            <span>Triages: <span id="filteredTriagesCount" class="font-bold">0</span></span>
                            <span>Documents: <span id="filteredDocumentsCount" class="font-bold">0</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patient Documents Section - Compact & Collapsible -->
        <div class="bg-white rounded-lg shadow compact-section">
            <div class="section-header compact-header" onclick="toggleSection('documents')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-file-medical-alt text-purple-600 mr-2"></i>
                        <h3 class="text-base font-bold text-gray-800">Patient Documents</h3>
                        <span class="ml-3 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full" id="documentsSummary">0 documents</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="uploadDocumentBtn" class="px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm transition-colors">
                            <i class="fas fa-upload mr-1"></i>Upload
                        </button>
                        <i class="fas fa-chevron-down chevron-icon text-gray-400" id="documentsChevron"></i>
                    </div>
                </div>
            </div>
            
            <div class="section-content collapsed compact-content" id="documentsContent">
                
                <div class="text-sm text-gray-600 mb-4 bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
                    <i class="fas fa-info-circle text-purple-600 mr-2"></i>
                    <strong>Document System:</strong> Upload and analyze medical documents (PDF, Images, DOCX, TXT). AI extracts vital signs, lab values, medications, and diagnoses automatically. Documents expire after 2 hours for privacy.
                </div>

                <!-- Upload Form (Initially Hidden) -->
                <div id="documentUploadForm" class="hidden border-2 border-dashed border-purple-300 rounded-lg p-6 mb-6 bg-purple-50">
                    <div class="text-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-purple-400 mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">Upload Medical Documents</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Supported formats: PDF, JPG, PNG, DOCX, TXT (Max 10MB per file, up to 5 files)
                        </p>
                        
                        <input type="file" 
                               id="documentFiles" 
                               multiple 
                               accept=".pdf,.jpg,.jpeg,.png,.docx,.txt"
                               class="hidden">
                        
                        <label for="documentFiles" 
                               class="inline-block px-6 py-3 bg-purple-600 text-white rounded-md cursor-pointer hover:bg-purple-700 transition-colors duration-200">
                            <i class="fas fa-folder-open mr-2"></i>
                            Choose Files
                        </label>
                        
                        <div id="selectedFiles" class="mt-4 hidden">
                            <h5 class="font-semibold text-gray-700 mb-2">Selected Files:</h5>
                            <div id="filesList" class="space-y-2"></div>
                            <div class="mt-4 space-x-2">
                                <button id="uploadFiles" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200">
                                    <i class="fas fa-upload mr-2"></i>
                                    Upload & Analyze
                                </button>
                                <button id="cancelUpload" class="px-4 py-2 bg-gray-400 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="uploadProgress" class="hidden mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-spinner fa-spin text-blue-600 mr-3"></i>
                            <span class="text-blue-800 font-medium">Processing documents...</span>
                        </div>
                        <div class="mt-2 text-sm text-blue-600">
                            Extracting text and analyzing medical data. This may take a few moments.
                        </div>
                        <div class="mt-3">
                            <div class="bg-blue-200 rounded-full h-2 w-full">
                                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Documents List -->
                <div class="space-y-4" id="documentsList">
                    <!-- Documents will be dynamically added here -->
                    <div id="noDocuments" class="text-center py-8 text-gray-500">
                        <i class="fas fa-file-medical-alt text-3xl mb-3 text-purple-300"></i>
                        <p>No documents uploaded yet</p>
                        <p class="text-sm mt-2">Upload medical documents to extract structured data automatically</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consultation History -->
        <div class="space-y-6">
            <!-- Triage Assessments Section - Compact & Collapsible -->
            <div class="bg-white rounded-lg shadow compact-section">
                <div class="section-header compact-header" onclick="toggleSection('triageAssessments')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-clipboard-check text-blue-600 mr-2"></i>
                            <h3 class="text-base font-bold text-gray-800">Triage Assessments</h3>
                            <span class="ml-3 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full" id="triageSummary">0 assessments</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <select id="triageSort" class="px-2 py-1 border rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <option value="recent">Recent</option>
                                <option value="priority">Priority</option>
                            </select>
                            <i class="fas fa-chevron-down chevron-icon text-gray-400" id="triageAssessmentsChevron"></i>
                        </div>
                    </div>
                </div>
                
                <div class="section-content collapsed compact-content" id="triageAssessmentsContent">
                    <div class="text-xs text-gray-600 mb-3 bg-blue-50 p-2 rounded border-l-2 border-blue-400">
                        <i class="fas fa-info-circle text-blue-600 mr-1"></i>
                        <strong>Triage Assessments:</strong> Initial patient evaluations including AI voice analysis and manual CTAS assessments.
                    </div>
                    <div class="space-y-3" id="triageAssessmentsList">
                        <!-- Triage assessments will be dynamically added here -->
                        <div class="text-center py-6 text-gray-500">
                            <i class="fas fa-clipboard-check text-2xl mb-2 text-blue-300"></i>
                            <p class="text-sm">No triage assessments found</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical Consultations Section - Compact & Collapsible -->
            <div class="bg-white rounded-lg shadow compact-section">
                <div class="section-header compact-header" onclick="toggleSection('medicalConsultations')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-stethoscope text-blue-600 mr-2"></i>
                            <h3 class="text-base font-bold text-gray-800">Medical Consultations</h3>
                            <span class="ml-3 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full" id="consultationsSummary">0 consultations</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <select id="consultationSort" class="px-2 py-1 border rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <option value="recent">Recent</option>
                                <option value="status">Status</option>
                            </select>
                            <i class="fas fa-chevron-down chevron-icon text-gray-400" id="medicalConsultationsChevron"></i>
                        </div>
                    </div>
                </div>
                
                <div class="section-content collapsed compact-content" id="medicalConsultationsContent">
                    <div class="text-xs text-gray-600 mb-3 bg-green-50 p-2 rounded border-l-2 border-green-400">
                        <i class="fas fa-info-circle text-green-600 mr-1"></i>
                        <strong>Medical Consultations:</strong> Detailed doctor consultations including diagnosis, treatment plans, and follow-up notes.
                    </div>
                    <div class="space-y-3" id="medicalConsultationsList">
                        <!-- Medical consultations will be dynamically added here -->
                        <div class="text-center py-6 text-gray-500">
                            <i class="fas fa-stethoscope text-2xl mb-2 text-green-300"></i>
                            <p class="text-sm">No medical consultations found</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Patient Medications Section - Compact & Collapsible -->
            <div class="bg-white rounded-lg shadow compact-section">
                <div class="section-header compact-header" onclick="toggleSection('medications')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-pills text-green-600 mr-2"></i>
                            <h3 class="text-base font-bold text-gray-800">Current Medications</h3>
                            <span class="ml-3 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full" id="medicationsSummary">0 medications</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button id="analyzeDrugInteractionsBtn" class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">
                                <i class="fas fa-exclamation-triangle mr-1"></i>Check
                            </button>
                            <button id="addMedicationBtn" class="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors">
                                <i class="fas fa-plus mr-1"></i>Add
                            </button>
                            <i class="fas fa-chevron-down chevron-icon text-gray-400" id="medicationsChevron"></i>
                        </div>
                    </div>
                </div>
                
                <div class="section-content collapsed compact-content" id="medicationsContent">
                    <div class="text-xs text-gray-600 mb-3 bg-orange-50 p-2 rounded border-l-2 border-orange-400">
                        <i class="fas fa-info-circle text-orange-600 mr-1"></i>
                        <strong>Medication Management:</strong> Track current medications, dosages, and medical conditions. Includes allergy checking.
                    </div>
                    <div class="space-y-3" id="patientMedicationsList">
                        <!-- Patient medications will be dynamically added here -->
                        <div class="text-center py-6 text-gray-500">
                            <i class="fas fa-pills text-2xl mb-2 text-orange-300"></i>
                            <p class="text-sm">No medications recorded</p>
                            <p class="text-xs mt-1">Add medications to track patient's treatment plan</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Holistic Patient Report Section - Compact & Collapsible -->
            <div class="bg-white rounded-lg shadow compact-section">
                <div class="section-header compact-header" onclick="toggleSection('holisticReport')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-medical-alt text-blue-600 mr-2"></i>
                            <h3 class="text-base font-bold text-gray-800">Generate Holistic Report</h3>
                            <span class="ml-3 text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">Comprehensive Analysis</span>
                        </div>
                        <i class="fas fa-chevron-down chevron-icon text-gray-400" id="holisticReportChevron"></i>
                    </div>
                </div>
                
                <div class="section-content collapsed compact-content" id="holisticReportContent">
                    <div class="text-xs text-gray-600 mb-2 bg-indigo-50 p-2 rounded border-l-2 border-indigo-400">
                        <i class="fas fa-info-circle text-indigo-600 mr-1"></i>
                        <strong>Holistic Report:</strong> Generate comprehensive report including consultations, triages, and medical reports for selected time period.
                    </div>
                
                <form id="reportGenerationForm" class="space-y-3">
                    <!-- Compact Time Range & Options in Single Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-3 items-end">
                        <!-- Time Range -->
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Time Range</label>
                            <select id="timeRange" class="w-full px-3 py-1.5 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <option value="1month">Last Month</option>
                                <option value="custom">Custom Range</option>
                                <option value="3months">Last 3 Months</option>
                                <option value="6months">Last 6 Months</option>
                                <option value="1year">Last Year</option>
                            </select>
                        </div>
                        
                        <!-- Custom Date Range (Initially Hidden) -->
                        <div id="customDateRange" class="hidden lg:col-span-2">
                            <label class="block text-xs font-medium text-gray-700 mb-1">Custom Date Range</label>
                            <div class="grid grid-cols-2 gap-2">
                                <input type="date" id="startDate" class="px-3 py-1.5 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <input type="date" id="endDate" class="px-3 py-1.5 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <!-- Generate Button (aligned to bottom) -->
                        <div class="lg:justify-self-end">
                            <button type="submit" class="w-full lg:w-auto px-4 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                <i class="fas fa-file-medical-alt mr-1"></i>Generate Report
                            </button>
                        </div>
                    </div>

                    <!-- Compact Content Selection in 2x2 Grid -->
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Include in Report</label>
                        <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" id="includeConsultations" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="text-xs text-gray-700">Medical Consultations</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" id="includeTriages" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="text-xs text-gray-700">Triage Assessments</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" id="includeMedicalReports" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="text-xs text-gray-700">Medical Reports</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" id="includeVitalSigns" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="text-xs text-gray-700">Vital Signs</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" id="includeMedications" class="form-checkbox h-4 w-4 text-blue-600" checked>
                                <span class="text-xs text-gray-700">Medications</span>
                            </label>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Allergy Confirmation Modal -->
    <div id="allergyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">New Allergies Detected</h3>
                <div class="mt-2 px-4 md:px-7 py-3">
                    <p class="text-sm text-gray-500">The following allergies were mentioned in the consultation. Would you like to add them to the patient's permanent record?</p>
                    <div id="newAllergiesList" class="mt-4 text-left">
                        <!-- Allergies will be listed here -->
                    </div>
                </div>
                <div class="items-center px-4 py-3 space-y-2 md:space-y-0 md:space-x-2">
                    <button id="confirmAllergies" class="w-full md:w-auto px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                        Confirm Allergies
                    </button>
                    <button id="cancelAllergies" class="w-full md:w-auto px-4 py-2 bg-gray-100 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Preview Modal -->
    <div id="reportPreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-6 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-file-medical-alt mr-2 text-blue-600"></i>
                    Holistic Patient Report
                </h3>
                <button onclick="closeReportPreviewModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="reportContent" class="prose max-w-none bg-gray-50 p-4 rounded-lg max-h-[70vh] overflow-y-auto">
                <!-- Report content will be dynamically added here -->
            </div>
            <div class="mt-4 flex justify-end space-x-2">
                <button onclick="printReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-print mr-2"></i>Print
                </button>
                <button onclick="closeReportPreviewModal()" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Medication Modal -->
    <div id="medicationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900" id="medicationModalTitle">Add Medication</h3>
                <button onclick="closeMedicationModal()" 
                        class="flex items-center justify-center w-16 h-16 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 cursor-pointer"
                        title="Close modal">
                    <i class="fas fa-times text-3xl"></i>
                </button>
            </div>
            
            <form id="medicationForm" class="space-y-4">
                <!-- Medication Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search mr-1"></i>
                        Search Medication
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="medicationSearch" 
                               placeholder="Search by scientific or trade name..."
                               class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <div id="medicationSearchResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-48 overflow-y-auto">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                </div>

                <!-- Selected Medication Display -->
                <div id="selectedMedicationDisplay" class="hidden bg-green-50 p-3 rounded-lg border border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-green-800" id="selectedMedicationName"></h4>
                            <p class="text-sm text-green-600" id="selectedMedicationDetails"></p>
                        </div>
                        <button type="button" onclick="clearSelectedMedication()" class="text-green-600 hover:text-green-800">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Dosage Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-prescription-bottle mr-1"></i>
                            Dosage
                        </label>
                        <input type="text" 
                               id="medicationDosage" 
                               placeholder="e.g., 500mg, 1 tablet"
                               class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                               required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            Frequency
                        </label>
                        <select id="medicationFrequency" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                            <option value="">Select frequency</option>
                            <option value="Once daily">Once daily</option>
                            <option value="Twice daily">Twice daily</option>
                            <option value="Three times daily">Three times daily</option>
                            <option value="Four times daily">Four times daily</option>
                            <option value="Every 4 hours">Every 4 hours</option>
                            <option value="Every 6 hours">Every 6 hours</option>
                            <option value="Every 8 hours">Every 8 hours</option>
                            <option value="Every 12 hours">Every 12 hours</option>
                            <option value="As needed">As needed (PRN)</option>
                            <option value="Weekly">Weekly</option>
                            <option value="Monthly">Monthly</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>

                <!-- Custom Frequency Input -->
                <div id="customFrequencyContainer" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Custom Frequency</label>
                    <input type="text" 
                           id="customFrequency" 
                           placeholder="Specify custom frequency"
                           class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>

                <!-- Route of Administration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-route mr-1"></i>
                        Route
                    </label>
                    <select id="medicationRoute" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="">Select route</option>
                        <option value="Oral">Oral</option>
                        <option value="IV">Intravenous (IV)</option>
                        <option value="IM">Intramuscular (IM)</option>
                        <option value="SC">Subcutaneous (SC)</option>
                        <option value="Topical">Topical</option>
                        <option value="Inhalation">Inhalation</option>
                        <option value="Rectal">Rectal</option>
                        <option value="Nasal">Nasal</option>
                        <option value="Sublingual">Sublingual</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <!-- Medical Condition -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-stethoscope mr-1"></i>
                        Medical Condition
                    </label>
                    <div class="space-y-2">
                        <select id="commonConditions" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="">Select common condition</option>
                            <!-- Will be populated dynamically -->
                        </select>
                        <div class="text-center text-gray-500 text-sm">OR</div>
                        <input type="text" 
                               id="customCondition" 
                               placeholder="Enter custom medical condition"
                               class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                </div>

                <!-- Duration Section -->
                <div class="border-t pt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-clock mr-1"></i>
                        Medication Duration
                    </label>
                    
                    <div class="space-y-3">
                        <!-- Duration Type Selection -->
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-2">Duration Type</label>
                            <select id="medicationDurationType" 
                                    class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                    onchange="handleMedicationDurationTypeChange()"
                                    required>
                                <option value="until_discontinued">Until Discontinued (Default)</option>
                                <option value="fixed">Fixed Duration</option>
                                <option value="stat">STAT (Single Dose)</option>
                                <option value="prn">PRN (As Needed)</option>
                            </select>
                        </div>

                        <!-- Fixed Duration Options (Hidden by default) -->
                        <div id="medicationFixedDurationOptions" class="hidden space-y-3">
                            <!-- Preset Durations -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-2">Common Durations</label>
                                <select id="medicationDurationPreset" 
                                        class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                        onchange="applyMedicationDurationPreset()">
                                    <option value="">Select a preset or enter custom...</option>
                                    <!-- Presets will be loaded dynamically -->
                                </select>
                            </div>

                            <!-- Custom Duration -->
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">Duration</label>
                                    <input type="number" 
                                           id="medicationDurationValue" 
                                           min="1" max="365"
                                           placeholder="Enter number"
                                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">Unit</label>
                                    <select id="medicationDurationUnit" 
                                            class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="days">Days</option>
                                        <option value="weeks">Weeks</option>
                                        <option value="months">Months</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Calculated End Date Display -->
                            <div id="medicationCalculatedEndDate" class="hidden bg-blue-50 p-3 rounded-lg border border-blue-200">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt text-blue-600 mr-2"></i>
                                    <span class="text-sm text-blue-800">
                                        <strong>End Date:</strong> <span id="medicationEndDateDisplay"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Duration Type Descriptions -->
                        <div id="medicationDurationDescription" class="text-xs text-gray-600 bg-gray-50 p-3 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-gray-500 mr-2 mt-0.5"></i>
                                <span id="medicationDurationDescriptionText">
                                    Medication will continue until manually discontinued by a healthcare provider.
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Notes -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-notes-medical mr-1"></i>
                        Additional Notes (Optional)
                    </label>
                    <textarea id="medicationNotes" 
                              rows="3" 
                              placeholder="Any additional notes about this medication..."
                              class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-4">
                    <button type="button" 
                            onclick="closeMedicationModal()" 
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Save Medication
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Drug Interaction Analysis Modal -->
    <div id="drugInteractionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-red-700">
                    <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                    Drug-Drug Interaction Analysis
                </h3>
                <button onclick="closeDrugInteractionModal()" 
                        class="flex items-center justify-center w-16 h-16 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 cursor-pointer"
                        title="Close modal">
                    <i class="fas fa-times text-3xl"></i>
                </button>
            </div>
            
            <!-- Analysis Status -->
            <div id="drugInteractionStatus" class="mb-4">
                <!-- Status will be shown here -->
            </div>
            
            <!-- Analysis Results -->
            <div id="drugInteractionResults" class="space-y-4">
                <!-- Results will be shown here -->
            </div>
            
            <!-- Close Button -->
            <div class="flex justify-end mt-6">
                <button onclick="closeDrugInteractionModal()" 
                        class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Close
                </button>
            <!-- Clinical Tab -->
            <div id="clinicalContent" class="tab-pane hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Consultations Card -->
                    <div class="modern-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Recent Consultations</h3>
                            <button id="newConsultationBtn" class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all">
                                <i class="fas fa-stethoscope"></i>
                                <span>New</span>
                            </button>
                        </div>
                        <div id="consultationsList">
                            <!-- Consultations will be loaded here -->
                        </div>
                    </div>

                    <!-- Triage Assessments Card -->
                    <div class="modern-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Triage Assessments</h3>
                            <span class="text-sm text-gray-500" id="triageCount">0 assessments</span>
                        </div>
                        <div id="triagesList">
                            <!-- Triage assessments will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div id="documentsContent" class="tab-pane hidden">
                <div class="modern-card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Medical Documents</h3>
                            <p class="text-sm text-gray-600">Upload and analyze medical documents with AI extraction</p>
                        </div>
                        <button class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all">
                            <i class="fas fa-upload"></i>
                            <span>Upload</span>
                        </button>
                    </div>
                    <div id="documentsList">
                        <!-- Documents will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Reports Tab -->
            <div id="reportsContent" class="tab-pane hidden">
                <div class="modern-card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Holistic Patient Report</h3>
                            <p class="text-sm text-gray-600">Generate comprehensive reports including consultations, triages, and medical data</p>
                        </div>
                        <button class="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all">
                            <i class="fas fa-chart-line"></i>
                            <span>Generate Report</span>
                        </button>
                    </div>
                    <div id="reportsArea">
                        <!-- Report generation interface will be here -->
                    </div>
                </div>
            </div>

            </div> <!-- End tab-pane active (overview) -->
        </div> <!-- End tab-content -->

        <!-- Floating Action Button -->
        <button class="fab" onclick="showQuickActionsMenu()" title="Quick Actions">
            <i class="fas fa-plus"></i>
        </button>

        <!-- Quick Actions Menu (hidden by default) -->
        <div id="quickActionsMenu" class="hidden fixed bottom-20 right-6 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 z-50">
            <div class="flex flex-col gap-2">
                <button onclick="document.getElementById('startTriageBtn').click()" class="flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-microphone text-white text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">Audio Triage</div>
                        <div class="text-xs text-gray-500">Voice analysis</div>
                    </div>
                </button>
                <button onclick="document.getElementById('newConsultationBtn').click()" class="flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-stethoscope text-white text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">Consultation</div>
                        <div class="text-xs text-gray-500">New visit</div>
                    </div>
                </button>
                <button onclick="document.getElementById('cpoeBtn').click()" class="flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clipboard-list text-white text-sm"></i>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">CPOE</div>
                        <div class="text-xs text-gray-500">Orders</div>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('doctorToken');
        if (!token) {
            window.location.href = '/doctor-login.html';
        }

        // Check authentication function
        function checkAuth() {
            const token = localStorage.getItem('doctorToken');
            if (!token) {
                window.location.href = '/doctor-login.html';
                return null;
            }
            return token.trim();
        }

        // Utility function to show notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        type === 'warning' ? 'fa-exclamation-triangle' :
                        'fa-info-circle'
                    } mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Get patient ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        let patientId = urlParams.get('id');
        const triageId = urlParams.get('triageId');

        // Global variables - must be declared before use
        let currentPatientId = null;
        let currentPatient = null;
        let pendingAllergies = [];

        // If triageId is provided, fetch patient ID from triage data
        if (triageId && !patientId) {
            fetchPatientIdFromTriage(triageId);
        } else if (!patientId && !triageId) {
            window.location.href = '/dashboard.html';
        } else if (patientId) {
            // Set current patient ID and load details
            currentPatientId = patientId;
            loadPatientDetails();
        }

        // Function to fetch patient ID from triage data
        async function fetchPatientIdFromTriage(triageId) {
            try {
                const token = localStorage.getItem('doctorToken');
                if (!token) {
                    window.location.href = '/doctor-login.html';
                    return;
                }

                const response = await fetch(`/api/patients/by-triage/${triageId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.patientId) {
                        patientId = data.patientId;
                        currentPatientId = patientId;
                        // Update URL to show patient ID instead of triage ID
                        window.history.replaceState({}, '', `/patient-details.html?id=${patientId}`);
                        // Now load all patient data
                        loadPatientDetails();
                        loadConsultations();
                        loadPatientMedications();
                        loadPatientDocuments();
                    } else {
                        console.error('Failed to get patient ID from triage:', data);
                        showNotification('Failed to load patient data from triage', 'error');
                        setTimeout(() => window.location.href = '/dashboard.html', 2000);
                    }
                } else {
                    console.error('Failed to fetch patient ID from triage:', response.status);
                    showNotification('Failed to load patient data', 'error');
                    setTimeout(() => window.location.href = '/dashboard.html', 2000);
                }
            } catch (error) {
                console.error('Error fetching patient ID from triage:', error);
                showNotification('Error loading patient data', 'error');
                setTimeout(() => window.location.href = '/dashboard.html', 2000);
            }
        }

        // Global Time Filter Variables
        let currentTimeFilter = 'all';
        let currentStartDate = null;
        let currentEndDate = null;
        let allConsultations = [];
        let allTriages = [];
        let allDocuments = [];

        // Global Time Filter Functions
        function initializeTimeFilter() {
            const globalTimeFilter = document.getElementById('globalTimeFilter');
            const customDateRangeFilter = document.getElementById('customDateRangeFilter');
            const applyFilterBtn = document.getElementById('applyFilterBtn');
            const clearFilterBtn = document.getElementById('clearFilterBtn');
            const filterStartDate = document.getElementById('filterStartDate');
            const filterEndDate = document.getElementById('filterEndDate');

            // Set "All Time" as default and apply it immediately
            globalTimeFilter.value = 'all';
            currentTimeFilter = 'all';

            // Show/hide custom date range
            globalTimeFilter.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateRangeFilter.classList.remove('hidden');
                } else {
                    customDateRangeFilter.classList.add('hidden');
                }
            });

            // Apply filter
            applyFilterBtn.addEventListener('click', function() {
                applyGlobalTimeFilter();
            });

            // Clear filter
            clearFilterBtn.addEventListener('click', function() {
                clearGlobalTimeFilter();
            });

            // Set default end date to today
            const today = new Date().toISOString().split('T')[0];
            filterEndDate.value = today;

            // Automatically apply "All Time" filter on initialization
            setTimeout(() => {
                applyGlobalTimeFilter();
            }, 100);
        }

        function applyGlobalTimeFilter() {
            const filterValue = document.getElementById('globalTimeFilter').value;
            const startDate = document.getElementById('filterStartDate').value;
            const endDate = document.getElementById('filterEndDate').value;

            if (filterValue === 'custom' && (!startDate || !endDate)) {
                showNotification('Please select both start and end dates for custom range', 'warning');
                return;
            }

            currentTimeFilter = filterValue;
            
            // Calculate date range
            const now = new Date();
            let filterStartDate, filterEndDate;

            switch (filterValue) {
                case 'all':
                    filterStartDate = null;
                    filterEndDate = null;
                    break;
                case '7days':
                    filterStartDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    filterEndDate = now;
                    break;
                case '1month':
                    filterStartDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    filterEndDate = now;
                    break;
                case '3months':
                    filterStartDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                    filterEndDate = now;
                    break;
                case '6months':
                    filterStartDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
                    filterEndDate = now;
                    break;
                case '1year':
                    filterStartDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    filterEndDate = now;
                    break;
                case 'custom':
                    filterStartDate = new Date(startDate);
                    filterEndDate = new Date(endDate);
                    filterEndDate.setHours(23, 59, 59, 999); // Include full end date
                    break;
            }

            currentStartDate = filterStartDate;
            currentEndDate = filterEndDate;

            // Apply filter to all data
            filterAllData();
            updateFilterStatus();
            showFilterButtons();
        }

        function clearGlobalTimeFilter() {
            currentTimeFilter = 'all';
            currentStartDate = null;
            currentEndDate = null;
            
            document.getElementById('globalTimeFilter').value = 'all';
            document.getElementById('customDateRangeFilter').classList.add('hidden');
            document.getElementById('filterStartDate').value = '';
            document.getElementById('filterEndDate').value = '';
            
            // Reload all data without filter
            filterAllData();
            hideFilterStatus();
            hideFilterButtons();
        }

        function filterAllData() {
            // Filter and display consultations
            const filteredConsultations = filterDataByDate(allConsultations, 'created_at');
            displayMedicalConsultations(filteredConsultations);

            // Filter and display triages
            const filteredTriages = filterDataByDate(allTriages, 'created_at');
            displayTriageAssessments(filteredTriages);

            // Filter and display documents
            const filteredDocuments = filterDataByDate(allDocuments, 'uploaded_at');
            displayPatientDocuments(filteredDocuments);

            // Update quick stats
            updateQuickStats(filteredConsultations);
        }

        function filterDataByDate(data, dateField) {
            if (currentTimeFilter === 'all' || !currentStartDate || !currentEndDate) {
                return data;
            }

            return data.filter(item => {
                const itemDate = new Date(item[dateField]);
                return itemDate >= currentStartDate && itemDate <= currentEndDate;
            });
        }

        function updateFilterStatus() {
            const filterStatus = document.getElementById('filterStatus');
            const filterStatusText = document.getElementById('filterStatusText');
            
            let statusText = '';
            switch (currentTimeFilter) {
                case '7days':
                    statusText = 'Showing data from last 7 days';
                    break;
                case '1month':
                    statusText = 'Showing data from last month';
                    break;
                case '3months':
                    statusText = 'Showing data from last 3 months';
                    break;
                case '6months':
                    statusText = 'Showing data from last 6 months';
                    break;
                case '1year':
                    statusText = 'Showing data from last year';
                    break;
                case 'custom':
                    const startStr = currentStartDate.toLocaleDateString();
                    const endStr = currentEndDate.toLocaleDateString();
                    statusText = `Showing data from ${startStr} to ${endStr}`;
                    break;
            }

            filterStatusText.textContent = statusText;
            filterStatus.classList.remove('hidden');

            // Update counts
            const filteredConsultations = filterDataByDate(allConsultations, 'created_at');
            const filteredTriages = filterDataByDate(allTriages, 'created_at');
            const filteredDocuments = filterDataByDate(allDocuments, 'uploaded_at');

            document.getElementById('filteredConsultationsCount').textContent = filteredConsultations.length;
            document.getElementById('filteredTriagesCount').textContent = filteredTriages.length;
            document.getElementById('filteredDocumentsCount').textContent = filteredDocuments.length;
        }

        function hideFilterStatus() {
            document.getElementById('filterStatus').classList.add('hidden');
        }

        function showFilterButtons() {
            document.getElementById('clearFilterBtn').classList.remove('hidden');
        }

        function hideFilterButtons() {
            document.getElementById('clearFilterBtn').classList.add('hidden');
        }

        function updateQuickStats(filteredConsultations) {
            // Update total consultations count based on filtered data
            const totalConsultationsElement = document.getElementById('totalConsultations');
            if (totalConsultationsElement) {
                totalConsultationsElement.textContent = filteredConsultations.length;
            }
            
            // Update last consultation date
            const lastConsultationElement = document.getElementById('lastConsultation');
            if (lastConsultationElement) {
                if (filteredConsultations.length > 0) {
                    const lastConsultation = filteredConsultations.sort((a, b) => 
                        new Date(b.created_at) - new Date(a.created_at)
                    )[0];
                    lastConsultationElement.textContent = 
                        new Date(lastConsultation.created_at).toLocaleDateString();
                } else {
                    lastConsultationElement.textContent = 'No consultations in selected period';
                }
            }
        }

        // Variables are now declared at the top of the script

        // Fetch and display patient details
        async function loadPatientDetails() {
            // Validate patient ID before making API call
            if (!patientId || patientId === 'null' || patientId === 'undefined') {
                console.log('loadPatientDetails: No valid patient ID, skipping');
                return;
            }
            
            try {
                const response = await fetch(`/api/patients/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const patient = await response.json();
                
                if (response.ok) {
                    // Store patient data globally for allergy checking
                    currentPatient = patient;
                    
                    // Update patient header (includes ID, name, age, gender)
                    updatePatientHeader(patient);
                    
                    // Update basic information
                    const patientIdElement = document.getElementById('patientId');
                    if (patientIdElement) patientIdElement.textContent = patient.id;
                    
                    const patientNameElement = document.getElementById('patientName');
                    if (patientNameElement) patientNameElement.textContent = patient.name;
                    
                    const patientAgeElement = document.getElementById('patientAge');
                    if (patientAgeElement) patientAgeElement.textContent = patient.age || 'Not specified';
                    
                    const patientGenderElement = document.getElementById('patientGender');
                    if (patientGenderElement) patientGenderElement.textContent = patient.gender || 'Not specified';
                    
                    // Update patient summary badge
                    const patientSummaryElement = document.getElementById('patientSummary');
                    if (patientSummaryElement) {
                        patientSummaryElement.textContent = `ID: ${patient.id} | Age: ${patient.age || 'N/A'} | Gender: ${patient.gender || 'N/A'}`;
                    }

                    // Update allergies display
                    updateAllergiesDisplay(patient.allergies);

                    // Update medications display
                    updateMedicationsDisplay(patient.medications);

                    // Update medical history
                    const patientConditionsElement = document.getElementById('patientConditions');
                    if (patientConditionsElement) {
                        patientConditionsElement.textContent = patient.conditions || 'None reported';
                    }

                    // Update quick stats
                    const totalConsultationsElement = document.getElementById('totalConsultations');
                    if (totalConsultationsElement) {
                        totalConsultationsElement.textContent = patient.consultation_count || 0;
                    }
                    
                    const lastConsultationElement = document.getElementById('lastConsultation');
                    if (lastConsultationElement) {
                        lastConsultationElement.textContent = patient.last_consultation ? 
                            new Date(patient.last_consultation).toLocaleDateString() : 'No consultations';
                    }
                    
                    const currentPriorityElement = document.getElementById('currentPriority');
                    if (currentPriorityElement) {
                        currentPriorityElement.textContent = patient.priority || 'Not set';
                    }

                    // NOTE: Consultation loading is now handled separately by loadConsultations()
                    // with separated triage assessments and medical consultations sections
                    
                    /*
                    // OLD CODE - Update consultation list
                    const consultationList = document.getElementById('consultationList');
                    consultationList.innerHTML = '';

                    if (patient.consultations && patient.consultations.length > 0) {
                        console.log('Loading consultations:', patient.consultations);
                        patient.consultations.forEach(consultation => {
                            console.log('Creating card for consultation:', consultation.id, consultation);
                            const card = createConsultationCard(consultation);
                            consultationList.appendChild(card);
                        });
                    } else {
                        consultationList.innerHTML = '<p class="text-gray-500 text-center py-4">No consultations found</p>';
                    }
                    */
                }
            } catch (error) {
                console.error('Error loading patient details:', error);
            }
        }

        // Add this function before createConsultationCard
        function getPriorityClass(priority) {
            const priorityLower = priority?.toLowerCase() || '';
            
            // Handle standardized CTAS format
            if (priorityLower.includes('ctas 1') || priorityLower.includes('resuscitation')) {
                return 'bg-red-100 text-red-800 border border-red-300';
            } else if (priorityLower.includes('ctas 2') || priorityLower.includes('emergent')) {
                return 'bg-orange-100 text-orange-800 border border-orange-300';
            } else if (priorityLower.includes('ctas 3') || priorityLower.includes('urgent')) {
                return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
            } else if (priorityLower.includes('ctas 4') || priorityLower.includes('less urgent')) {
                return 'bg-blue-100 text-blue-800 border border-blue-300';
            } else if (priorityLower.includes('ctas 5') || priorityLower.includes('non-urgent')) {
                return 'bg-green-100 text-green-800 border border-green-300';
            }
            
            // Handle legacy format
            switch (priorityLower) {
                case 'level 1':
                case 'immediate':
                    return 'bg-red-100 text-red-800 border border-red-300';
                case 'level 2':
                    return 'bg-orange-100 text-orange-800 border border-orange-300';
                case 'level 3':
                    return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
                case 'level 4':
                    return 'bg-blue-100 text-blue-800 border border-blue-300';
                case 'level 5':
                    return 'bg-green-100 text-green-800 border border-green-300';
                default:
                    return 'bg-gray-100 text-gray-800 border border-gray-300';
            }
        }

        // Add this function to extract priority from analysis
        function extractPriorityFromAnalysis(analysis) {
            if (!analysis) return null;
            
            // Look for triage priority in the analysis, handling various formats
            const priorityPatterns = [
                /TRIAGE PRIORITY:\s*\*\s*([A-Z]+)\s*(?:\(([^)]+)\))?\s*\*/i,  // With asterisks
                /TRIAGE PRIORITY:\s*([A-Z]+)\s*(?:\(([^)]+)\))?/i,           // Without asterisks
                /Priority:\s*([A-Z]+)\s*(?:\(([^)]+)\))?/i                   // Alternative format
            ];

            for (const pattern of priorityPatterns) {
                const match = analysis.match(pattern);
                if (match) {
                    return {
                        level: match[1].toLowerCase(),
                        color: match[2]?.toLowerCase() || null
                    };
                }
            }
            return null;
        }

        // Create consultation card
        function createConsultationCard(consultation) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-lg shadow p-4 mb-4';
            
            const date = new Date(consultation.created_at).toLocaleString();
            
            // Get priority from database or extract from analysis
            let priority = consultation.priority;
            
            // Get the CTAS level from the priority
            let analysisPriority;
            if (priority) {
                // With our new standardized format, we can use the priority directly
                if (priority.startsWith('CTAS')) {
                    // Already in the standardized format
                    analysisPriority = priority;
                    console.log(`Using standardized priority '${priority}' from database for consultation #${consultation.id}`);
                } else {
                    // Convert old format or general terms to new standardized format
                    const priorityLower = priority.toLowerCase();
                    if (priorityLower.includes('resuscitation') || priorityLower.includes('immediate') || priorityLower.includes('critical')) {
                        analysisPriority = 'CTAS 1 - RESUSCITATION';
                    } else if (priorityLower.includes('emergent') || priorityLower.includes('emergency') || priorityLower.includes('high')) {
                        analysisPriority = 'CTAS 2 - EMERGENT';
                    } else if (priorityLower.includes('urgent') || priorityLower.includes('medium')) {
                        analysisPriority = 'CTAS 3 - URGENT';
                    } else if (priorityLower.includes('less urgent') || priorityLower.includes('low')) {
                        analysisPriority = 'CTAS 4 - LESS URGENT';
                    } else if (priorityLower.includes('non-urgent') || priorityLower.includes('minimal')) {
                        analysisPriority = 'CTAS 5 - NON-URGENT';
                    } else {
                        // Default mapping based on common terms
                        analysisPriority = 'CTAS 3 - URGENT';
                    }
                    console.log(`Converted priority '${priority}' to standardized format: ${analysisPriority}`);
                }
            } else {
                // Fall back to extracting from analysis
                const extractedLevel = consultation.severity?.level || 
                                      extractPriorityFromAnalysis(consultation.analysis_result?.analysis)?.level || 
                                      'level 3';
                
                // Convert extracted level to standardized format
                if (extractedLevel.includes('level')) {
                    const level = extractedLevel.match(/level\s*([1-5])/i)?.[1] || '3';
                    switch(level) {
                        case '1': analysisPriority = 'CTAS 1 - RESUSCITATION'; break;
                        case '2': analysisPriority = 'CTAS 2 - EMERGENT'; break;
                        case '3': analysisPriority = 'CTAS 3 - URGENT'; break;
                        case '4': analysisPriority = 'CTAS 4 - LESS URGENT'; break;
                        case '5': analysisPriority = 'CTAS 5 - NON-URGENT'; break;
                        default: analysisPriority = 'CTAS 3 - URGENT';
                    }
                } else {
                    analysisPriority = 'CTAS 3 - URGENT';
                }
                console.log(`No priority in database for consultation #${consultation.id}, using extracted and converted: ${analysisPriority}`);
            }
            
            const priorityClass = getPriorityClass(analysisPriority);
            
            // Format the analysis text with better structure
            let formattedAnalysis = '';
            const analysisText = consultation.analysis_result?.analysis || 'No analysis available';
            
            if (analysisText) {
                // Replace section headers with styled headers
                formattedAnalysis = analysisText
                    // Format main section headers
                    .replace(/##\s*([^#\n]+)/g, '<h3 class="text-lg font-semibold text-indigo-700 mt-4 mb-2">$1</h3>')
                    // Format subsection headers
                    .replace(/\*\*([0-9]+\.)\s*([^*]+)\*\*/g, '<h4 class="text-md font-semibold text-indigo-600 mt-3 mb-1">$1 $2</h4>')
                    // Format bold text
                    .replace(/\*\*([^*]+)\*\*/g, '<span class="font-semibold">$1</span>')
                    // Format bullet points
                    .replace(/\*\s+([^\n]+)/g, '<li class="ml-5 list-disc">$1</li>')
                    // Convert newlines to breaks
                    .replace(/\n\n/g, '<br><br>')
                    .replace(/\n(?!<)/g, '<br>');
            }
            
            card.innerHTML = `
                <div class="flex justify-between items-start mb-3 border-b pb-2">
                    <h3 class="text-xl font-bold text-gray-800">Consultation #${consultation.id}</h3>
                    <div class="flex flex-col items-end">
                        <span class="px-3 py-1 rounded-full text-sm font-bold ${priorityClass} shadow-sm">${analysisPriority.toUpperCase()}</span>
                        ${consultation.severity?.reasoning ? 
                            `<span class="text-xs text-gray-500 mt-1">${consultation.severity.reasoning}</span>` : ''}
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-calendar-alt text-indigo-500 mr-2"></i>
                        <p class="text-gray-600 text-sm">${date}</p>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-user-md text-indigo-500 mr-2"></i>
                        <p class="text-gray-600 text-sm">${consultation.doctor_name || 'Not assigned'}</p>
                    </div>
                </div>
                
                ${consultation.allergies_mentioned && consultation.allergies_mentioned.length > 0 ? 
                    `<div class="mb-4 p-3 bg-red-50 rounded-lg border border-red-100">
                        <p class="text-sm font-medium text-red-700 mb-2"><i class="fas fa-exclamation-triangle mr-1"></i> Allergies Mentioned:</p>
                        <div class="flex flex-wrap gap-1 mt-1">
                            ${consultation.allergies_mentioned.map(allergy => 
                                `<span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium shadow-sm">${allergy}</span>`
                            ).join('')}
                        </div>
                    </div>` : 
                    `<div class="mb-4 p-3 bg-green-50 rounded-lg border border-green-100">
                        <p class="text-sm font-medium text-green-700"><i class="fas fa-check-circle mr-1"></i> No allergies mentioned</p>
                    </div>`
                }
                
                <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 consultation-content">
                    <div class="prose max-w-none">${formattedAnalysis}</div>
                </div>
                
                <div class="mt-4 flex justify-end space-x-2 border-t pt-3">
                    <button onclick="editConsultation(${consultation.id})" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </button>
                    <button onclick="deleteConsultation(${consultation.id})" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        <i class="fas fa-trash mr-1"></i>Delete
                    </button>
                </div>
            `;
            
            return card;
        }

        // Add function to handle allergy confirmation
        async function handleAllergyConfirmation(allergies) {
            if (!currentPatientId || allergies.length === 0) return;
            
            try {
                const response = await fetch(`/api/patients/${currentPatientId}/allergies`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('doctorToken')}`
                    },
                    body: JSON.stringify({ allergies })
                });
                
                if (!response.ok) throw new Error('Failed to update allergies');
                
                const result = await response.json();
                showNotification('Allergies updated successfully', 'success');
                
                // Refresh patient details to show updated allergies
                loadPatientDetails(currentPatientId);
            } catch (error) {
                console.error('Error updating allergies:', error);
                showNotification('Failed to update allergies', 'error');
            }
        }

        // Update the analyzeAudio function
        async function analyzeAudio(patientId) {
            try {
                const response = await fetch(`/api/analyze-audio/${patientId}`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) throw new Error('Failed to analyze audio');

                const result = await response.json();
                
                // Filter and process allergies
                const validAllergies = result.allergies?.new?.filter(allergy => {
                    const trimmed = allergy.trim().toLowerCase();
                    return trimmed && 
                           trimmed !== 'none' && 
                           !trimmed.includes('no allergies') &&
                           !trimmed.startsWith('**') &&
                           !trimmed.endsWith('**');
                }) || [];

                if (validAllergies.length > 0) {
                    currentPatientId = patientId;
                    pendingAllergies = validAllergies;
                    
                    // Show allergy confirmation modal
                    const modal = document.getElementById('allergyModal');
                    const allergiesList = document.getElementById('newAllergiesList');
                    allergiesList.innerHTML = pendingAllergies.map(allergy => 
                        `<div class="flex items-center mb-2">
                            <input type="checkbox" class="allergy-checkbox mr-2" checked value="${allergy}">
                            <span>${allergy}</span>
                        </div>`
                    ).join('');
                    
                    modal.classList.remove('hidden');
                }

                // Update the consultation list
                const consultationList = document.getElementById('consultationList');
                consultationList.innerHTML = '';
                
                // Extract priority from analysis
                const analysisPriority = extractPriorityFromAnalysis(result.triageNote.analysis);
                
                // Add the new consultation to the list
                const newConsultation = {
                    id: result.triageNote.patientId,
                    created_at: result.triageNote.timestamp,
                    analysis_result: { analysis: result.triageNote.analysis },
                    allergies_mentioned: validAllergies,
                    severity: {
                        level: analysisPriority?.level || 'level 3',
                        reasoning: 'Based on AI analysis of symptoms and vital signs'
                    }
                };
                
                const card = createConsultationCard(newConsultation);
                consultationList.appendChild(card);
                
                showNotification('Audio analyzed successfully', 'success');
            } catch (error) {
                console.error('Error analyzing audio:', error);
                showNotification('Failed to analyze audio', 'error');
            }
        }

        // Add event listeners for the allergy modal
        document.getElementById('confirmAllergies').addEventListener('click', () => {
            const selectedAllergies = Array.from(document.querySelectorAll('.allergy-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            handleAllergyConfirmation(selectedAllergies);
            document.getElementById('allergyModal').classList.add('hidden');
        });

        document.getElementById('cancelAllergies').addEventListener('click', () => {
            document.getElementById('allergyModal').classList.add('hidden');
        });

        // Initialize (patient details loading is now handled conditionally above)

        // Audio Recording Variables
        let mediaRecorder = null;
        let audioChunks = [];
        let recordedBlob = null;
        let recordingTimer = null;
        let recordingSeconds = 0;
        let currentAudioFile = null;

        // Triage Interface Controls
        document.getElementById('startTriageBtn').addEventListener('click', () => {
            document.getElementById('triageInterface').classList.remove('hidden');
            document.getElementById('triageInterface').scrollIntoView({ behavior: 'smooth' });
            // Show Vital Signs section immediately when triage interface opens
            document.getElementById('vitalSignsSection').classList.remove('hidden');
            document.getElementById('vitalSignsSection').scrollIntoView({ behavior: 'smooth' });
        });

        document.getElementById('closeTriageBtn').addEventListener('click', () => {
            stopRecording();
            document.getElementById('triageInterface').classList.add('hidden');
            // Hide Vital Signs section when triage interface is closed
            document.getElementById('vitalSignsSection').classList.add('hidden');
            resetTriageInterface();
        });

        // Audio Recording Functionality
        document.getElementById('microphoneButton').addEventListener('click', async () => {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                stopRecording();
            } else {
                await startRecording();
            }
        });

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                recordingSeconds = 0;

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    recordedBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    currentAudioFile = recordedBlob;
                    
                    // Update UI
                    document.getElementById('recordingControls').classList.add('hidden');
                    document.getElementById('recordingComplete').classList.remove('hidden');
                    document.getElementById('analyzeAudioBtn').disabled = false;
                    
                    // Stop the stream
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start();
                
                // Update UI for recording state
                document.getElementById('recordingStatus').classList.remove('hidden');
                document.getElementById('microphoneButton').classList.add('bg-red-600', 'hover:bg-red-700');
                document.getElementById('microphoneButton').classList.remove('bg-green-600', 'hover:bg-green-700');
                
                // Start timer
                recordingTimer = setInterval(() => {
                    recordingSeconds++;
                    const minutes = Math.floor(recordingSeconds / 60);
                    const seconds = recordingSeconds % 60;
                    document.getElementById('recordingTimer').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }, 1000);

            } catch (error) {
                console.error('Error accessing microphone:', error);
                showNotification('Error accessing microphone. Please check permissions.', 'error');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                clearInterval(recordingTimer);
                
                // Reset UI
                document.getElementById('recordingStatus').classList.add('hidden');
                document.getElementById('microphoneButton').classList.remove('bg-red-600', 'hover:bg-red-700');
                document.getElementById('microphoneButton').classList.add('bg-green-600', 'hover:bg-green-700');
            }
        }

        // Play recorded audio
        document.getElementById('playRecordingBtn').addEventListener('click', () => {
            if (recordedBlob) {
                const audio = new Audio(URL.createObjectURL(recordedBlob));
                audio.play();
            }
        });

        // Record again
        document.getElementById('recordAgainBtn').addEventListener('click', () => {
            resetTriageInterface();
        });

        // File upload handling
        document.getElementById('audioFileInput').addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                currentAudioFile = file;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSelected').classList.remove('hidden');
                document.getElementById('analyzeAudioBtn').disabled = false;
                
                // Hide recording interface if file is selected
                document.getElementById('recordingControls').classList.add('hidden');
                document.getElementById('recordingComplete').classList.add('hidden');
            }
        });

        // Analyze audio button
        document.getElementById('analyzeAudioBtn').addEventListener('click', async () => {
            if (!currentAudioFile) {
                showNotification('Please record or upload an audio file first', 'error');
                return;
            }

            // Show progress
            document.getElementById('analysisProgress').classList.remove('hidden');
            document.getElementById('analyzeAudioBtn').disabled = true;

            try {
                await analyzeAudioFile(currentAudioFile);
                
                // Hide triage interface after successful analysis
                setTimeout(() => {
                    document.getElementById('triageInterface').classList.add('hidden');
                    resetTriageInterface();
                }, 2000);
                
            } catch (error) {
                console.error('Error analyzing audio:', error);
                
                // Check if it's a credit error (but not for admins with infinite credits)
                if (error.message && error.message.includes('Insufficient credits') && userCredits !== Infinity) {
                    showCreditsErrorModal();
                } else {
                    showNotification('Failed to analyze audio', 'error');
                }
                
                document.getElementById('analysisProgress').classList.add('hidden');
                document.getElementById('analyzeAudioBtn').disabled = false;
            }
        });

        // Reset triage interface
        function resetTriageInterface() {
            // Reset recording state
            recordedBlob = null;
            currentAudioFile = null;
            audioChunks = [];
            recordingSeconds = 0;
            
            // Clear timer
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
            
            // Reset UI elements
            document.getElementById('recordingControls').classList.remove('hidden');
            document.getElementById('recordingComplete').classList.add('hidden');
            document.getElementById('recordingStatus').classList.add('hidden');
            document.getElementById('fileSelected').classList.add('hidden');
            document.getElementById('analysisProgress').classList.add('hidden');
            document.getElementById('audioFileInput').value = '';
            document.getElementById('analyzeAudioBtn').disabled = true;
            document.getElementById('recordingTimer').textContent = '00:00';
            
            // Reset microphone button
            document.getElementById('microphoneButton').classList.remove('bg-red-600', 'hover:bg-red-700');
            document.getElementById('microphoneButton').classList.add('bg-green-600', 'hover:bg-green-700');
            
            // Hide Vital Signs section when triage interface is reset
            document.getElementById('vitalSignsSection').classList.add('hidden');
        }

        // Vital Signs System
        let vitalSignsMediaRecorder = null;
        let vitalSignsAudioChunks = [];
        let vitalSignsRecordingTimer = null;
        let vitalSignsRecordingSeconds = 0;

        // Vital Signs Tracing Button (with null check)
        const vitalSignsTracingBtn = document.getElementById('vitalSignsTracingBtn');
        if (vitalSignsTracingBtn) {
            vitalSignsTracingBtn.addEventListener('click', () => {
            window.location.href = `/vital-signs-tracing.html?id=${currentPatientId}`;
        });
        }

        // Skip vital signs button (with null check)
        const skipVitalSignsBtn = document.getElementById('skipVitalSignsBtn');
        if (skipVitalSignsBtn) {
            skipVitalSignsBtn.addEventListener('click', skipVitalSigns);
        }

        function skipVitalSigns() {
            document.getElementById('vitalSignsSection').classList.add('hidden');
            showNotification('Vital signs entry skipped');
        }

        // Vital Signs Voice Input (with null check)
        const vitalSignsVoiceBtn = document.getElementById('vitalSignsVoiceBtn');
        if (vitalSignsVoiceBtn) {
            vitalSignsVoiceBtn.addEventListener('click', async () => {
            if (vitalSignsMediaRecorder && vitalSignsMediaRecorder.state === 'recording') {
                stopVitalSignsRecording();
            } else {
                await startVitalSignsRecording();
            }
        });
        }

        async function startVitalSignsRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                vitalSignsMediaRecorder = new MediaRecorder(stream);
                vitalSignsAudioChunks = [];
                vitalSignsRecordingSeconds = 0;

                vitalSignsMediaRecorder.ondataavailable = (event) => {
                    vitalSignsAudioChunks.push(event.data);
                };

                vitalSignsMediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(vitalSignsAudioChunks, { type: 'audio/webm' });
                    await processVitalSignsVoiceInput(audioBlob);
                    stream.getTracks().forEach(track => track.stop());
                };

                vitalSignsMediaRecorder.start();
                
                // Update UI
                document.getElementById('vitalSignsVoiceBtn').classList.add('bg-red-600', 'hover:bg-red-700');
                document.getElementById('vitalSignsVoiceBtn').classList.remove('bg-yellow-600', 'hover:bg-yellow-700');
                document.getElementById('vitalSignsVoiceStatus').textContent = 'Recording... Click to stop';
                
                // Start timer
                vitalSignsRecordingTimer = setInterval(() => {
                    vitalSignsRecordingSeconds++;
                    const minutes = Math.floor(vitalSignsRecordingSeconds / 60);
                    const seconds = vitalSignsRecordingSeconds % 60;
                    document.getElementById('vitalSignsVoiceStatus').textContent = 
                        `Recording... ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')} - Click to stop`;
                }, 1000);

            } catch (error) {
                console.error('Error accessing microphone for vital signs:', error);
                showNotification('Error accessing microphone. Please check permissions.', 'error');
            }
        }

        function stopVitalSignsRecording() {
            if (vitalSignsMediaRecorder && vitalSignsMediaRecorder.state === 'recording') {
                vitalSignsMediaRecorder.stop();
                clearInterval(vitalSignsRecordingTimer);
                
                // Reset UI
                document.getElementById('vitalSignsVoiceBtn').classList.remove('bg-red-600', 'hover:bg-red-700');
                document.getElementById('vitalSignsVoiceBtn').classList.add('bg-yellow-600', 'hover:bg-yellow-700');
                document.getElementById('vitalSignsVoiceStatus').textContent = 'Processing...';
            }
        }

        async function processVitalSignsVoiceInput(audioBlob) {
            try {
                const token = localStorage.getItem('doctorToken') || localStorage.getItem('token');
                if (!token) {
                    showNotification('Authentication required', 'error');
                    return;
                }

                const formData = new FormData();
                formData.append('audio', audioBlob, 'vital-signs-voice.webm');

                // First, transcribe the audio
                const transcriptionResponse = await fetch('/api/consultations/voice-to-text', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (!transcriptionResponse.ok) {
                    throw new Error('Failed to transcribe audio');
                }

                const transcriptionResult = await transcriptionResponse.json();
                const transcription = transcriptionResult.text;

                // Then extract vital signs from transcription
                const extractionResponse = await fetch('/api/vital-signs/extract-from-voice', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ transcription })
                });

                if (!extractionResponse.ok) {
                    throw new Error('Failed to extract vital signs');
                }

                const extractionResult = await extractionResponse.json();
                const extractedVitalSigns = extractionResult.vitalSigns;

                // Debug: Log the extracted vital signs
                console.log('Extracted Vital Signs:', extractedVitalSigns);
                console.log('Original Transcription:', transcription);

                // Display extracted vital signs
                displayExtractedVitalSigns(extractedVitalSigns, transcription);
                
                // Fill form fields with extracted values
                fillVitalSignsForm(extractedVitalSigns);

                showNotification('Vital signs extracted successfully');

            } catch (error) {
                console.error('Error processing vital signs voice input:', error);
                showNotification('Failed to process voice input', 'error');
                document.getElementById('vitalSignsVoiceStatus').textContent = 'Click to record vital signs';
            }
        }

        function displayExtractedVitalSigns(vitalSigns, transcription) {
            const container = document.getElementById('extractedVitalSigns');
            const resultDiv = document.getElementById('vitalSignsVoiceResult');
            
            let html = `<p class="text-xs text-gray-500 mb-2">"${transcription}"</p><div class="space-y-1">`;
            
            Object.entries(vitalSigns).forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                    const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    html += `<div><span class="font-medium">${label}:</span> ${value}</div>`;
                }
            });
            
            html += '</div>';
            container.innerHTML = html;
            resultDiv.classList.remove('hidden');
        }

        function fillVitalSignsForm(vitalSigns) {
            if (vitalSigns.systolic_bp) document.getElementById('systolicBp').value = vitalSigns.systolic_bp;
            if (vitalSigns.diastolic_bp) document.getElementById('diastolicBp').value = vitalSigns.diastolic_bp;
            if (vitalSigns.heart_rate) document.getElementById('heartRate').value = vitalSigns.heart_rate;
            if (vitalSigns.respiratory_rate) document.getElementById('respiratoryRate').value = vitalSigns.respiratory_rate;
            if (vitalSigns.temperature_celsius) document.getElementById('temperature').value = vitalSigns.temperature_celsius;
            if (vitalSigns.oxygen_saturation) document.getElementById('oxygenSaturation').value = vitalSigns.oxygen_saturation;
            if (vitalSigns.random_blood_sugar) document.getElementById('randomBloodSugar').value = vitalSigns.random_blood_sugar;
            if (vitalSigns.pain_scale) document.getElementById('painScale').value = vitalSigns.pain_scale;
        }

        // Vital Signs Form Submission (with null check)
        const vitalSignsForm = document.getElementById('vitalSignsForm');
        if (vitalSignsForm) {
            vitalSignsForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                systolic_bp: document.getElementById('systolicBp').value ? parseInt(document.getElementById('systolicBp').value) : null,
                diastolic_bp: document.getElementById('diastolicBp').value ? parseInt(document.getElementById('diastolicBp').value) : null,
                heart_rate: document.getElementById('heartRate').value ? parseInt(document.getElementById('heartRate').value) : null,
                respiratory_rate: document.getElementById('respiratoryRate').value ? parseInt(document.getElementById('respiratoryRate').value) : null,
                temperature_celsius: document.getElementById('temperature').value ? parseFloat(document.getElementById('temperature').value) : null,
                oxygen_saturation: document.getElementById('oxygenSaturation').value ? parseInt(document.getElementById('oxygenSaturation').value) : null,
                random_blood_sugar: document.getElementById('randomBloodSugar').value ? parseInt(document.getElementById('randomBloodSugar').value) : null,
                pain_scale: document.getElementById('painScale').value ? parseInt(document.getElementById('painScale').value) : null,
                notes: document.getElementById('vitalSignsNotes')?.value || null
            };

            // Check if at least one vital sign is provided
            const hasVitalSigns = Object.values(formData).some(value => value !== null && value !== '');
            if (!hasVitalSigns) {
                showNotification('Please enter at least one vital sign', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('doctorToken') || localStorage.getItem('token');
                if (!token) {
                    showNotification('Authentication required', 'error');
                    return;
                }

                const requestData = {
                    patient_id: parseInt(currentPatientId),
                    ...formData
                };
                
                console.log('Sending vital signs data:', requestData);
                
                const response = await fetch('/api/vital-signs/add', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showNotification('Vital signs saved successfully');
                        document.getElementById('vitalSignsSection').classList.add('hidden');
                        document.getElementById('vitalSignsForm').reset();
                        document.getElementById('vitalSignsVoiceResult').classList.add('hidden');
                        document.getElementById('vitalSignsVoiceStatus').textContent = 'Click to record';
                    }
                } else {
                    const errorData = await response.json();
                    console.error('Vital signs validation error details:', errorData);
                    if (errorData.details) {
                        if (Array.isArray(errorData.details)) {
                            console.error('Specific validation failures:', errorData.details);
                            console.error('Full error details:', JSON.stringify(errorData.details, null, 2));
                            const errorMessages = errorData.details.map(detail => `${detail.param || 'unknown'}: ${detail.msg}`).join(', ');
                            throw new Error(`Validation failed: ${errorMessages}`);
                        } else {
                            console.error('Error details:', errorData.details);
                            throw new Error(`Server error: ${errorData.details}`);
                        }
                    }
                    throw new Error(errorData.error || 'Failed to save vital signs');
                }
            } catch (error) {
                console.error('Error saving vital signs:', error);
                showNotification(error.message, 'error');
            }
        });
        }

        // Credits Management Functions
        let userCredits = 0;

        // Load user credits
        async function loadCredits() {
            try {
                const token = localStorage.getItem('doctorToken');
                if (!token) {
                    console.log('No token found, using default credits');
                    userCredits = parseInt(localStorage.getItem('doctorCredits') || '2000');
                    document.getElementById('creditsCount').textContent = userCredits.toLocaleString();
                    return;
                }

                const response = await fetch('/api/credits/balance', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    userCredits = data.remaining_credits || 0;
                    
                    // Handle infinite credits for admins
                    if (userCredits === Infinity || data.is_admin || data.is_superuser) {
                        document.getElementById('creditsCount').textContent = '∞';
                        userCredits = Infinity;
                        
                        // Hide request button for admins
                        const requestBtn = document.querySelector('button[onclick="showCreditsModal()"]');
                        if (requestBtn) {
                            requestBtn.style.display = 'none';
                        }
                    } else {
                        document.getElementById('creditsCount').textContent = userCredits.toLocaleString();
                        
                        // Show request button for regular users
                        const requestBtn = document.querySelector('button[onclick="showCreditsModal()"]');
                        if (requestBtn) {
                            requestBtn.style.display = 'inline-flex';
                        }
                    }
                    
                    // Update credits color based on amount
                    const creditsCount = document.getElementById('creditsCount');
                    if (userCredits === Infinity) {
                        creditsCount.className = 'px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold';
                    } else if (userCredits < 100) {
                        creditsCount.className = 'px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm font-semibold';
                    } else if (userCredits < 500) {
                        creditsCount.className = 'px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold';
                    } else {
                        creditsCount.className = 'px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold';
                    }
                } else {
                    console.error('Failed to fetch credits');
                    userCredits = parseInt(localStorage.getItem('doctorCredits') || '2000');
                    document.getElementById('creditsCount').textContent = userCredits.toLocaleString();
                }
            } catch (error) {
                console.error('Error loading credits:', error);
                userCredits = parseInt(localStorage.getItem('doctorCredits') || '2000');
                document.getElementById('creditsCount').textContent = userCredits.toLocaleString();
            }
        }

        // Credits modal functions
        function showCreditsModal() {
            document.getElementById('creditsModal').classList.remove('hidden');
        }

        function hideCreditsModal() {
            document.getElementById('creditsModal').classList.add('hidden');
        }

        // Credits amount change
        const creditsAmountElement = document.getElementById('creditsAmount');
        if (creditsAmountElement) {
            creditsAmountElement.addEventListener('change', function() {
                const customDiv = document.getElementById('customCreditsDiv');
                if (this.value === 'custom') {
                    customDiv.classList.remove('hidden');
                } else {
                    customDiv.classList.add('hidden');
                }
            });
        }

        // Credits form submission
        const creditsFormElement = document.getElementById('creditsForm');
        if (creditsFormElement) {
            creditsFormElement.addEventListener('submit', submitCreditsRequest);
        }

        async function submitCreditsRequest(e) {
            e.preventDefault();
            
            const creditsRequested = document.getElementById('creditsAmount').value === 'custom'
                ? document.getElementById('customCredits').value
                : document.getElementById('creditsAmount').value;
            
            const reason = document.getElementById('creditsReason').value;

            try {
                const token = localStorage.getItem('doctorToken');
                const response = await fetch('/api/credits/request', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ creditsRequested, reason })
                });

                if (response.ok) {
                    showNotification('Credits request submitted successfully!', 'success');
                    hideCreditsModal();
                    document.getElementById('creditsForm').reset();
                    document.getElementById('customCreditsDiv').classList.add('hidden');
                } else {
                    const errorData = await response.json();
                    showNotification(errorData.error || 'Failed to submit request', 'error');
                }
            } catch (error) {
                console.error('Error submitting credits request:', error);
                showNotification('Failed to submit request', 'error');
            }
        }

        // Credits error modal functions
        function showCreditsErrorModal() {
            // Update current credits display
            if (userCredits === Infinity) {
                document.getElementById('currentCreditsDisplay').textContent = '∞';
            } else {
                document.getElementById('currentCreditsDisplay').textContent = userCredits.toLocaleString();
            }
            document.getElementById('creditsErrorModal').classList.remove('hidden');
        }

        function closeCreditsErrorModal() {
            document.getElementById('creditsErrorModal').classList.add('hidden');
        }

        // Updated analyzeAudio function to work with the new interface
        async function analyzeAudioFile(audioFile) {
            if (!patientId) {
                throw new Error('Patient ID not found');
            }

            const formData = new FormData();
            formData.append('audio', audioFile);

            const token = localStorage.getItem('doctorToken');
            if (!token) {
                window.location.href = '/doctor-login.html';
                return;
            }

            const response = await fetch(`/api/analyze-audio/${patientId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                
                // Check if it's a credit error (403 status)
                if (response.status === 403 && errorData.error === 'Insufficient credits') {
                    throw new Error('Insufficient credits');
                }
                
                // Check if it's a service unavailable error (503 status)
                if (response.status === 503) {
                    throw new Error('AI Service Temporarily Unavailable - Please try again in a few minutes');
                }
                
                // Check if it's a rate limit error (429 status)
                if (response.status === 429) {
                    throw new Error('AI Service Busy - Please try again in a few minutes');
                }
                
                throw new Error(errorData.error || 'Failed to analyze audio');
            }

            const result = await response.json();
            
            // Show triage results modal immediately
            showTriageResultsModal(result);
            
            // Filter and process allergies
            const validAllergies = result.allergies?.current?.filter(allergy => {
                const trimmed = allergy.trim().toLowerCase();
                return trimmed && 
                       trimmed !== 'none' && 
                       !trimmed.includes('no allergies') &&
                       !trimmed.startsWith('**') &&
                       !trimmed.endsWith('**');
            }) || [];

            if (validAllergies.length > 0) {
                currentPatientId = patientId;
                pendingAllergies = validAllergies;
                
                // Show allergy confirmation modal after triage results modal
                setTimeout(() => {
                    const modal = document.getElementById('allergyModal');
                    const allergiesList = document.getElementById('newAllergiesList');
                    allergiesList.innerHTML = pendingAllergies.map(allergy => 
                        `<div class="flex items-center mb-2">
                            <input type="checkbox" class="allergy-checkbox mr-2" checked value="${allergy}">
                            <span>${allergy}</span>
                        </div>`
                    ).join('');
                    
                    modal.classList.remove('hidden');
                }, 1000); // Show after 1 second delay
            }

            // Vital signs section is now shown immediately when triage interface opens
            // No need to show it again after analysis

            // Reload patient details to show new consultation
            await loadPatientDetails();
            
            return result;
        }

        // Show triage results modal
        function showTriageResultsModal(result) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.id = 'triageResultsModal';
            
            // Extract key information from analysis with safer defaults
            const analysis = result.triageNote?.analysis || 'Analysis not available';
            
            // Safer priority handling with multiple fallbacks
            let priority = result.triageNote?.priority || {};
            if (!priority.level || typeof priority.level !== 'string') {
                priority = { 
                    level: 'Medium', 
                    reasoning: 'Standard assessment - priority level determined by system default' 
                };
            }
            
            const allergies = result.allergies?.mentioned || [];
            
            // Parse analysis sections
            const sections = parseTriageAnalysis(analysis);
            
            // Get priority styling - ensure level is a string
            const priorityLevel = (priority.level || 'Medium').toString();
            const priorityStyle = getPriorityModalStyle(priorityLevel);
            
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                        <!-- Header with Priority -->
                        <div class="relative ${priorityStyle.headerBg} ${priorityStyle.textColor} p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-12 h-12 ${priorityStyle.iconBg} rounded-full flex items-center justify-center">
                                        <i class="${priorityStyle.icon} text-2xl"></i>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold">AI Triage Analysis Complete</h2>
                                        <p class="opacity-90">Priority Level: ${priorityLevel.toUpperCase()}</p>
                                    </div>
                                </div>
                                <button onclick="closeTriageResultsModal()" class="text-white hover:text-gray-200 transition-colors">
                                    <i class="fas fa-times text-2xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="overflow-y-auto max-h-[70vh] p-6">
                            <!-- Priority Summary Card -->
                            <div class="mb-6 p-4 ${priorityStyle.cardBg} border ${priorityStyle.borderColor} rounded-lg">
                                <div class="flex items-center space-x-3 mb-2">
                                    <i class="${priorityStyle.icon} ${priorityStyle.textColor}"></i>
                                    <h3 class="font-semibold ${priorityStyle.textColor}">Priority Assessment</h3>
                                </div>
                                <p class="text-gray-700">${priority.reasoning || 'Assessment based on presented symptoms and clinical indicators.'}</p>
                            </div>

                            <!-- Key Findings Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <!-- Chief Complaint -->
                                ${sections.chiefComplaint ? `
                                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                    <h3 class="font-semibold text-blue-800 mb-2 flex items-center">
                                        <i class="fas fa-stethoscope mr-2"></i>Chief Complaint
                                    </h3>
                                    <p class="text-gray-700">${sections.chiefComplaint}</p>
                                </div>
                                ` : ''}

                                <!-- Symptoms -->
                                ${sections.symptoms ? `
                                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                    <h3 class="font-semibold text-yellow-800 mb-2 flex items-center">
                                        <i class="fas fa-thermometer-half mr-2"></i>Symptoms
                                    </h3>
                                    <p class="text-gray-700">${sections.symptoms}</p>
                                </div>
                                ` : ''}

                                <!-- Pain Assessment -->
                                ${sections.painAssessment ? `
                                <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                                    <h3 class="font-semibold text-red-800 mb-2 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-2"></i>Pain Assessment
                                    </h3>
                                    <p class="text-gray-700">${sections.painAssessment}</p>
                                </div>
                                ` : ''}

                                <!-- Medical Scoring Systems -->
                                ${(() => {
                                    if (!sections.medicalScoring) return '';
                                    const scoringSystems = parseMedicalScoring(sections.medicalScoring);
                                    if (!scoringSystems) return '';
                                    
                                    return scoringSystems.map(system => {
                                        const colors = getScoringSystemColor(system.name, system.totalScore, system.interpretation);
                                        return `
                                        <div class="${colors.bg} p-4 rounded-lg border ${colors.border}">
                                            <h3 class="font-semibold ${colors.text} mb-2 flex items-center">
                                                <i class="fas fa-calculator ${colors.icon} mr-2"></i>${system.name}
                                                ${system.totalScore ? `<span class="ml-2 px-2 py-1 text-xs rounded-full bg-white ${colors.text} font-bold">Score: ${system.totalScore}</span>` : ''}
                                            </h3>
                                            <div class="text-gray-700 space-y-2">
                                                <p>${system.details}</p>
                                                ${system.interpretation ? `
                                                <div class="mt-2 p-2 bg-white rounded border-l-4 ${colors.border}">
                                                    <span class="font-medium ${colors.text}">Clinical Interpretation:</span>
                                                    <span class="ml-1">${system.interpretation}</span>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>`;
                                    }).join('');
                                })()}

                                <!-- Allergies -->
                                ${allergies.length > 0 ? `
                                <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                                    <h3 class="font-semibold text-orange-800 mb-2 flex items-center">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>Allergies Identified
                                    </h3>
                                    <ul class="list-disc list-inside text-gray-700">
                                        ${allergies.map(allergy => `<li>${allergy}</li>`).join('')}
                                    </ul>
                                </div>
                                ` : ''}
                            </div>

                            <!-- Recommended Actions -->
                            ${sections.recommendedActions ? `
                            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <h3 class="font-semibold text-green-800 mb-2 flex items-center">
                                    <i class="fas fa-clipboard-check mr-2"></i>Recommended Actions
                                </h3>
                                <p class="text-gray-700">${sections.recommendedActions}</p>
                            </div>
                            ` : ''}

                            <!-- Red Flags (if any) -->
                            ${sections.redFlags ? `
                            <div class="mb-6 p-4 bg-red-100 border border-red-300 rounded-lg">
                                <h3 class="font-semibold text-red-800 mb-2 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Red Flags / Urgent Concerns
                                </h3>
                                <p class="text-red-700 font-medium">${sections.redFlags}</p>
                            </div>
                            ` : ''}

                            <!-- Full Analysis (Collapsible) -->
                            <div class="border border-gray-200 rounded-lg">
                                <button onclick="toggleFullAnalysis()" class="w-full p-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between">
                                    <span class="font-semibold text-gray-800">
                                        <i class="fas fa-file-medical mr-2"></i>View Complete Analysis
                                    </span>
                                    <i id="analysisChevron" class="fas fa-chevron-down text-gray-600"></i>
                                </button>
                                <div id="fullAnalysisContent" class="hidden p-4 border-t border-gray-200 bg-white">
                                    <div class="prose max-w-none text-sm">
                                        <pre class="whitespace-pre-wrap font-sans text-gray-700">${analysis}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Footer Actions -->
                        <div class="border-t border-gray-200 p-6 bg-gray-50">
                            <div class="flex flex-col sm:flex-row gap-3 justify-end">
                                <button onclick="printTriageReport()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-print mr-2"></i>Print Report
                                </button>
                                <button onclick="viewFullConsultation(${result.consultationId})" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-eye mr-2"></i>View Full Details
                                </button>
                                <button onclick="closeTriageResultsModal()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-check mr-2"></i>Done
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Add animation
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('animate-modalSlideIn');
            }, 10);
        }

        // New Consultation button handler
        document.getElementById('newConsultationBtn').addEventListener('click', async () => {
            await checkTriageAndStartConsultation();
        });

        // CPOE Orders button handler (with null check)
        const cpoeBtn = document.getElementById('cpoeBtn');
        if (cpoeBtn) {
            cpoeBtn.addEventListener('click', () => {
            const token = checkAuth();
            if (!token) return;
            
            // Navigate to CPOE page with patient ID
            window.location.href = `/cpoe.html?patientId=${patientId}`;
        });
        }

        // Check for triage before starting consultation
        async function checkTriageAndStartConsultation() {
            const token = checkAuth();
            if (!token) return;

            try {
                // Show loading state
                document.getElementById('newConsultationBtn').innerHTML = 
                    '<i class="fas fa-spinner fa-spin mr-2"></i>Checking...';
                document.getElementById('newConsultationBtn').disabled = true;

                // Check if patient has recent triage
                const response = await fetch(`/api/consultations/check-triage/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to check triage status');
                }

                const triageCheck = await response.json();

                // Reset button
                document.getElementById('newConsultationBtn').innerHTML = 
                    '<i class="fas fa-stethoscope mr-2"></i>Doctor Consultation';
                document.getElementById('newConsultationBtn').disabled = false;

                if (triageCheck.hasRecentTriage) {
                    // Patient has recent triage - start consultation normally
                    startTriageBasedConsultation(triageCheck.triageData.id);
                } else {
                    // No recent triage - show warning modal
                    showNoTriageWarningModal(triageCheck.patient);
                }

            } catch (error) {
                console.error('Error checking triage:', error);
                showNotification('Failed to check triage status', 'error');
                
                // Reset button
                document.getElementById('newConsultationBtn').innerHTML = 
                    '<i class="fas fa-stethoscope mr-2"></i>Doctor Consultation';
                document.getElementById('newConsultationBtn').disabled = false;
            }
        }

        // Show warning modal when no triage exists
        function showNoTriageWarningModal(patient) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.id = 'noTriageWarningModal';
            
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-6 border w-11/12 max-w-lg shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex items-center justify-center mb-4">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                                <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">
                                ⚠️ No Triage Found for ${patient.name}
                            </h3>
                            <div class="text-sm text-gray-600 text-left mb-6 bg-gray-50 p-4 rounded-lg">
                                <p class="mb-2">This patient has no active triage.</p>
                                <p class="mb-2">Continuing without triage means:</p>
                                <ul class="list-disc list-inside space-y-1 ml-4">
                                    <li>No CTAS priority assessment</li>
                                    <li>No allergy alerts</li>
                                    <li>No symptom analysis</li>
                                    <li>No initial vital signs</li>
                                </ul>
                            </div>
                            
                            <!-- Manual CTAS Assessment Form (Initially Hidden) -->
                            <div id="manualTriageForm" class="hidden mb-6 text-left bg-blue-50 p-4 rounded-lg border">
                                <h4 class="font-medium text-blue-900 mb-3">
                                    <i class="fas fa-clipboard-check mr-2"></i>Manual CTAS Assessment
                                </h4>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">CTAS Level *</label>
                                        <select id="manualCtasLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">Select CTAS Level</option>
                                            <option value="CTAS 1 - RESUSCITATION">CTAS 1 - Resuscitation (Red)</option>
                                            <option value="CTAS 2 - EMERGENT">CTAS 2 - Emergent (Orange)</option>
                                            <option value="CTAS 3 - URGENT">CTAS 3 - Urgent (Yellow)</option>
                                            <option value="CTAS 4 - LESS URGENT">CTAS 4 - Less Urgent (Blue)</option>
                                            <option value="CTAS 5 - NON-URGENT">CTAS 5 - Non-Urgent (Green)</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Clinical Reasoning *</label>
                                        <textarea id="manualReasoning" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                                  rows="2" 
                                                  placeholder="Brief clinical reasoning for CTAS level..."></textarea>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Known Allergies (Optional)</label>
                                        <input type="text" 
                                               id="manualAllergies" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                               placeholder="e.g., Penicillin, Shellfish">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Quick Assessment (Optional)</label>
                                        <textarea id="manualAssessment" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                                  rows="2" 
                                                  placeholder="Brief vital signs or initial assessment..."></textarea>
                                    </div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <button id="saveManualTriageBtn" 
                                        class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <i class="fas fa-save mr-2"></i>Save & Start Consultation
                                    </button>
                                    <button id="cancelManualTriageBtn" 
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                            
                            <div id="triageOptions" class="flex flex-col space-y-3">
                                <button id="proceedAnywayBtn" 
                                    class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2">
                                    <i class="fas fa-forward mr-2"></i>Proceed Anyway
                                </button>
                                <button id="manualCtasBtn"
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-clipboard-check mr-2"></i>Manual CTAS Assessment
                                </button>
                                <button id="doQuickTriageBtn"
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                    <i class="fas fa-microphone mr-2"></i>Use AI Voice Triage
                                </button>
                                <button id="cancelModalBtn"
                                    class="w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Add event listeners
            document.getElementById('proceedAnywayBtn').addEventListener('click', () => {
                closeNoTriageModal();
                startDirectConsultation();
            });
            
            document.getElementById('manualCtasBtn').addEventListener('click', () => {
                showManualTriageForm();
            });
            
            document.getElementById('doQuickTriageBtn').addEventListener('click', () => {
                closeNoTriageModal();
                startAudioTriage();
            });
            
            document.getElementById('cancelModalBtn').addEventListener('click', () => {
                closeNoTriageModal();
            });
            
            // Manual triage form event listeners
            document.getElementById('saveManualTriageBtn').addEventListener('click', () => {
                saveManualTriage();
            });
            
            document.getElementById('cancelManualTriageBtn').addEventListener('click', () => {
                hideManualTriageForm();
            });
            
            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeNoTriageModal();
                }
            });
        }

        // Show manual triage form
        function showManualTriageForm() {
            document.getElementById('triageOptions').classList.add('hidden');
            document.getElementById('manualTriageForm').classList.remove('hidden');
        }

        // Hide manual triage form
        function hideManualTriageForm() {
            document.getElementById('manualTriageForm').classList.add('hidden');
            document.getElementById('triageOptions').classList.remove('hidden');
            
            // Reset form
            document.getElementById('manualCtasLevel').value = '';
            document.getElementById('manualReasoning').value = '';
            document.getElementById('manualAllergies').value = '';
            document.getElementById('manualAssessment').value = '';
        }

        // Save manual triage and start consultation
        async function saveManualTriage() {
            const ctasLevel = document.getElementById('manualCtasLevel').value;
            const reasoning = document.getElementById('manualReasoning').value.trim();
            const allergies = document.getElementById('manualAllergies').value.trim();
            const assessment = document.getElementById('manualAssessment').value.trim();

            // Validation
            if (!ctasLevel) {
                showNotification('Please select a CTAS level', 'error');
                return;
            }
            if (!reasoning) {
                showNotification('Please provide clinical reasoning', 'error');
                return;
            }

            const token = checkAuth();
            if (!token) return;

            try {
                // Show loading state
                document.getElementById('saveManualTriageBtn').innerHTML = 
                    '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
                document.getElementById('saveManualTriageBtn').disabled = true;

                // Create manual triage record
                const manualTriageData = {
                    patientId: patientId,
                    ctasLevel: ctasLevel,
                    reasoning: reasoning,
                    allergies: allergies ? allergies.split(',').map(a => a.trim()).filter(a => a) : [],
                    assessment: assessment,
                    triageType: 'manual'
                };

                const response = await fetch('/api/consultations/manual-triage', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(manualTriageData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to save manual triage');
                }

                const result = await response.json();
                
                closeNoTriageModal();
                showNotification('Manual triage saved successfully!', 'success');
                
                // Start consultation with the manual triage
                startTriageBasedConsultation(result.triageConsultation.id);
                
            } catch (error) {
                console.error('Error saving manual triage:', error);
                showNotification(error.message || 'Failed to save manual triage', 'error');
                
                // Reset button
                document.getElementById('saveManualTriageBtn').innerHTML = 
                    '<i class="fas fa-save mr-2"></i>Save & Start Consultation';
                document.getElementById('saveManualTriageBtn').disabled = false;
            }
        }

        // Start consultation with existing triage
        function startTriageBasedConsultation(triageConsultationId) {
            // Use the existing API flow with triage
            startConsultationFromTriage(triageConsultationId);
        }

        // Start direct consultation without triage
        async function startDirectConsultation() {
            const token = checkAuth();
            if (!token) return;

            try {
                showNotification('Starting direct consultation...', 'info');

                const response = await fetch('/api/consultations/start', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patientId: patientId,
                        consultationType: 'routine'
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to start consultation');
                }

                const result = await response.json();
                
                showNotification('Direct consultation started successfully!', 'success');
                
                // Redirect to consultation interface
                window.location.href = `/consultation.html?id=${result.consultation.id}`;
                
            } catch (error) {
                console.error('Error starting direct consultation:', error);
                showNotification(error.message || 'Failed to start consultation', 'error');
            }
        }

        // Start audio triage (existing functionality)
        function startAudioTriage() {
            document.getElementById('startTriageBtn').click();
        }

        // Start consultation from existing triage
        async function startConsultationFromTriage(triageConsultationId) {
            const token = checkAuth();
            if (!token) return;

            try {
                showNotification('Starting consultation...', 'info');
                console.log('🔄 Starting consultation from triage ID:', triageConsultationId);

                const response = await fetch('/api/consultations/start', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        triageConsultationId: triageConsultationId,
                        consultationType: 'routine'
                    })
                });

                console.log('🔄 Response status:', response.status);

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('❌ Error response:', errorData);
                    throw new Error(errorData.error || 'Failed to start consultation');
                }

                const result = await response.json();
                console.log('✅ Consultation created successfully:', result);
                
                showNotification('Consultation started successfully!', 'success');
                
                // Check if we have the consultation ID
                if (result.consultation && result.consultation.id) {
                    console.log('🔄 Redirecting to consultation page with ID:', result.consultation.id);
                    window.location.href = `/consultation.html?id=${result.consultation.id}`;
                } else if (result.consultationId) {
                    console.log('🔄 Redirecting to consultation page with consultationId:', result.consultationId);
                    window.location.href = `/consultation.html?id=${result.consultationId}`;
                } else {
                    console.error('❌ No consultation ID found in response:', result);
                    throw new Error('Consultation created but no ID returned');
                }
                
            } catch (error) {
                console.error('Error starting consultation from triage:', error);
                showNotification(error.message || 'Failed to start consultation', 'error');
            }
        }

        // Sort functionality - REPLACED WITH SEPARATE SORTING FOR TRIAGE AND MEDICAL CONSULTATIONS
        // Old single-section sorting has been replaced with sortTriageAssessments() and sortMedicalConsultations()
        /*
        const sortSelect = document.querySelector('select');
        sortSelect.addEventListener('change', (e) => {
            const sortBy = e.target.value;
            const consultationList = document.getElementById('consultationList');
            const cards = Array.from(consultationList.children);
            
            cards.sort((a, b) => {
                if (sortBy === 'priority') {
                    const priorityA = a.querySelector('span').textContent;
                    const priorityB = b.querySelector('span').textContent;
                    return priorityA.localeCompare(priorityB);
                } else {
                    // Most recent
                    const dateA = new Date(a.querySelector('p').textContent);
                    const dateB = new Date(b.querySelector('p').textContent);
                    return dateB - dateA;
                }
            });
            
            cards.forEach(card => consultationList.appendChild(card));
        });
        */

        // Add function to handle allergy deletion
        async function deleteAllergy(allergy) {
            if (!confirm(`Are you sure you want to remove the allergy: ${allergy}?`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/patients/${patientId}/allergies/${encodeURIComponent(allergy)}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) throw new Error('Failed to remove allergy');
                
                const result = await response.json();
                showNotification('Allergy removed successfully', 'success');
                
                // Update the allergies display
                updateAllergiesDisplay(result.allergies);
            } catch (error) {
                console.error('Error removing allergy:', error);
                showNotification('Failed to remove allergy', 'error');
            }
        }

        // Add function to update allergies display
        function updateAllergiesDisplay(allergies) {
            const allergiesList = document.getElementById('allergiesList');
            
            // Filter out empty or "none" entries and deduplicate
            const validAllergies = allergies?.filter(allergy => {
                const trimmed = allergy.trim().toLowerCase();
                return trimmed && 
                       trimmed !== 'none' && 
                       !trimmed.includes('no allergies') &&
                       !trimmed.startsWith('**') &&
                       !trimmed.endsWith('**');
            }) || [];

            // Remove duplicates while preserving order
            const uniqueAllergies = [...new Set(validAllergies)];

            if (uniqueAllergies.length === 0) {
                allergiesList.innerHTML = '<p class="text-gray-500 text-sm">None reported</p>';
                return;
            }
            
            allergiesList.innerHTML = uniqueAllergies.map(allergy => `
                <div class="flex items-center bg-red-100 text-red-800 rounded-full px-2 py-1 text-xs">
                    <span>${allergy}</span>
                    <button onclick="deleteAllergy('${allergy}')" class="ml-1 text-red-600 hover:text-red-800">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
            `).join('');
        }

        // Add function to update medications display
        function updateMedicationsDisplay(medications) {
            const medicationsList = document.getElementById('medicationsList');
            if (!medications || medications.length === 0) {
                medicationsList.innerHTML = '<p class="text-gray-500 text-sm">None reported</p>';
                return;
            }
            
            medicationsList.innerHTML = medications.map(med => `
                <div class="flex items-center justify-between bg-blue-50 rounded px-2 py-1">
                    <div class="flex flex-wrap items-center gap-1">
                        <span class="font-medium text-blue-800 text-xs">${med.name}</span>
                        ${med.dosage ? `<span class="text-xs text-blue-600">${med.dosage}</span>` : ''}
                        ${med.frequency ? `<span class="text-xs text-blue-600">${med.frequency}</span>` : ''}
                    </div>
                    <button onclick="deleteMedication('${med.name}')" class="text-blue-600 hover:text-blue-800 ml-2">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
            `).join('');
        }

        // Add function to handle medication deletion
        async function deleteMedication(medication) {
            if (!confirm(`Are you sure you want to remove the medication: ${medication}?`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/patients/${patientId}/medications/${encodeURIComponent(medication)}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) throw new Error('Failed to remove medication');
                
                const result = await response.json();
                showNotification('Medication removed successfully', 'success');
                
                // Update the medications display
                updateMedicationsDisplay(result.medications);
            } catch (error) {
                console.error('Error removing medication:', error);
                showNotification('Failed to remove medication', 'error');
            }
        }

        // Mobile menu functionality
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileBackBtn = document.getElementById('mobileBackBtn');
        const closeSidebarBtn = document.getElementById('closeSidebarBtn');
        const sidebar = document.getElementById('sidebar');
        
        mobileMenuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        mobileBackBtn.addEventListener('click', () => {
            window.history.back();
        });

        closeSidebarBtn.addEventListener('click', () => {
            sidebar.classList.remove('active');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !mobileMenuBtn.contains(e.target) && 
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Edit consultation function
        function editConsultation(consultationId) {
            console.log('Editing consultation:', consultationId, 'for patient:', patientId);
            window.location.href = `/edit-consultation.html?id=${consultationId}&patientId=${patientId}`;
        }

        // Delete consultation function
        async function deleteConsultation(consultationId) {
            if (!confirm('Are you sure you want to delete this consultation? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/consultations/${consultationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to delete consultation');
                }
                
                showNotification('Consultation deleted successfully', 'success');
                
                // Reload patient details to refresh the consultation list
                loadPatientDetails();
                
            } catch (error) {
                console.error('Error deleting consultation:', error);
                showNotification('Failed to delete consultation', 'error');
            }
        }

        // Make functions available globally for onclick handlers
        window.deleteAllergy = deleteAllergy;
        window.deleteMedication = deleteMedication;
        window.editConsultation = editConsultation;
        window.deleteConsultation = deleteConsultation;
        window.startConsultationFromTriage = startConsultationFromTriage;

        // Close no triage warning modal
        function closeNoTriageModal() {
            const modal = document.getElementById('noTriageWarningModal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // Load and display consultation history
        async function loadConsultations() {
            try {
                const response = await fetch(`/api/consultations/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('doctorToken')}`
                    }
                });

                if (response.ok) {
                    const consultations = await response.json();
                    
                    // Separate triage assessments from medical consultations
                    const triageAssessments = [];
                    const medicalConsultations = [];
                    
                    consultations.forEach(consultation => {
                        if (consultation.consultation_type === 'doctor_consultation') {
                            medicalConsultations.push(consultation);
                        } else {
                            // This includes 'voice_triage', 'manual_ctas', and other triage types
                            triageAssessments.push(consultation);
                        }
                    });
                    
                    displayTriageAssessments(triageAssessments);
                    displayMedicalConsultations(medicalConsultations);
                } else {
                    console.error('Failed to load consultations');
                }
            } catch (error) {
                console.error('Error loading consultations:', error);
            }
        }

        // Display triage assessments
        function displayTriageAssessments(assessments) {
            const triageList = document.getElementById('triageAssessmentsList');
            
            // Update summary badge
            document.getElementById('triageSummary').textContent = `${assessments.length} assessment${assessments.length !== 1 ? 's' : ''}`;
            
            if (assessments.length === 0) {
                triageList.innerHTML = `
                    <div class="text-center py-6 text-gray-500">
                        <i class="fas fa-clipboard-check text-2xl mb-2 text-blue-300"></i>
                        <p class="text-sm">No triage assessments found</p>
                    </div>
                `;
                return;
            }

            triageList.innerHTML = assessments.map((assessment, index) => {
                const priority = assessment.priority || 'Unknown';
                const priorityColor = getPriorityColor(priority);
                const assessmentType = getAssessmentType(assessment.consultation_type);
                
                // Parse the assessment text to extract key information
                const parsedData = parseTriageAssessment(assessment.assessment || '');
                
                return createTriageCard(assessment, assessmentType, priorityColor, parsedData, index);
            }).join('');
        }

        // Parse triage assessment to extract structured data
        function parseTriageAssessment(assessmentText) {
            const data = {
                chiefComplaint: '',
                symptoms: [],
                vitalSigns: [],
                redFlags: [],
                recommendations: [],
                summary: ''
            };

            if (!assessmentText) return data;

            // Extract Chief Complaint
            const ccMatch = assessmentText.match(/(?:\*\*)?(?:1\.\s*)?CHIEF COMPLAINT:?\*?\*?\s*([^\n*]+)/i);
            if (ccMatch) {
                data.chiefComplaint = ccMatch[1].trim();
            }

            // Extract Symptoms
            const symptomsSection = assessmentText.match(/(?:\*\*)?(?:2\.\s*)?SYMPTOMS:?\*?\*?\s*([\s\S]*?)(?=\*\*(?:3\.|PAIN|VITAL|MEDICAL|ALLERGIES|TRIAGE|RED FLAGS)|\n\n|$)/i);
            if (symptomsSection) {
                const symptoms = symptomsSection[1].match(/\*\s*([^\n]+)/g);
                if (symptoms) {
                    data.symptoms = symptoms.map(s => s.replace(/^\*\s*/, '').trim()).filter(s => s);
                }
            }

            // Extract Vital Signs
            const vitalMatch = assessmentText.match(/(?:\*\*)?(?:4\.\s*)?VITAL SIGNS[^:]*:?\*?\*?\s*([\s\S]*?)(?=\*\*(?:5\.|MEDICAL|ALLERGIES|TRIAGE|RED FLAGS)|\n\n|$)/i);
            if (vitalMatch) {
                const vitals = vitalMatch[1].match(/\*\s*([^\n]+)/g);
                if (vitals) {
                    data.vitalSigns = vitals.map(v => v.replace(/^\*\s*/, '').trim()).filter(v => v);
                }
            }

            // Extract Red Flags
            const redFlagsMatch = assessmentText.match(/(?:\*\*)?(?:9\.\s*)?RED FLAGS[^:]*:?\*?\*?\s*([\s\S]*?)(?=\*\*(?:10\.|RECOMMENDED|ADDITIONAL)|\n\n|$)/i);
            if (redFlagsMatch) {
                const flags = redFlagsMatch[1].match(/\*\s*\*\*([^*]+)\*\*[^*]*([^\n*]+)/g);
                if (flags) {
                    data.redFlags = flags.map(f => {
                        const match = f.match(/\*\s*\*\*([^*]+)\*\*[^*]*([^\n*]+)/);
                        return match ? `${match[1].trim()}: ${match[2].trim()}` : f.replace(/^\*\s*/, '').trim();
                    }).filter(f => f);
                }
            }

            // Extract Recommendations
            const recsMatch = assessmentText.match(/(?:\*\*)?(?:10\.\s*)?RECOMMENDED ACTIONS:?\*?\*?\s*([\s\S]*?)(?=\*\*(?:11\.|ADDITIONAL)|\n\n|$)/i);
            if (recsMatch) {
                const recs = recsMatch[1].match(/(?:\d+\.\s*)?([^\n]+)/g);
                if (recs) {
                    data.recommendations = recs.map(r => r.replace(/^\d+\.\s*/, '').trim()).filter(r => r && !r.startsWith('*'));
                }
            }

            return data;
        }

        // Create enhanced triage card
        function createTriageCard(assessment, assessmentType, priorityColor, parsedData, index) {
            const cardId = `triage-card-${index}`;
            const isRedFlag = assessment.priority && (assessment.priority.includes('CTAS 1') || assessment.priority.includes('CTAS 2'));
            
            return `
                <div class="border-l-4 ${priorityColor.border} bg-blue-50 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200">
                    <!-- Compact Summary Header -->
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-2">
                                    <i class="${assessmentType.icon} text-blue-600"></i>
                                    <span class="font-medium text-blue-800">${assessmentType.label}</span>
                                </div>
                                <div class="px-3 py-1 ${priorityColor.bg} ${priorityColor.text} rounded-full text-sm font-bold">
                                    ${assessment.priority}
                                </div>
                                ${isRedFlag ? '<span class="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium"><i class="fas fa-exclamation-triangle mr-1"></i>Critical</span>' : ''}
                            </div>
                            <span class="text-sm text-gray-500">
                                ${new Date(assessment.created_at).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </span>
                        </div>
                        
                        <!-- Quick Summary -->
                        <div class="space-y-2">
                            ${parsedData.chiefComplaint ? `
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-notes-medical text-blue-500 mt-0.5 text-sm"></i>
                                    <span class="text-gray-700 text-sm"><strong>Chief Complaint:</strong> ${parsedData.chiefComplaint}</span>
                                </div>
                            ` : ''}
                            
                            ${parsedData.redFlags.length > 0 ? `
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-exclamation-triangle text-red-500 mt-0.5 text-sm"></i>
                                    <span class="text-red-700 text-sm"><strong>Red Flags:</strong> ${parsedData.redFlags.slice(0, 1).join('; ')}${parsedData.redFlags.length > 1 ? ` (+${parsedData.redFlags.length - 1} more)` : ''}</span>
                                </div>
                            ` : ''}
                            
                            ${parsedData.vitalSigns.length > 0 ? `
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-heartbeat text-green-500 mt-0.5 text-sm"></i>
                                    <span class="text-gray-700 text-sm"><strong>Vitals:</strong> ${parsedData.vitalSigns.slice(0, 2).join(', ')}</span>
                                </div>
                            ` : ''}
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex items-center justify-between mt-4 pt-3 border-t border-blue-200">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span><i class="fas fa-user-md mr-1"></i>${assessment.doctor_name || 'AI System'}</span>
                                <span><i class="fas fa-clock mr-1"></i>${assessment.duration || 'N/A'}</span>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <button onclick="toggleTriageDetails('${cardId}')" 
                                    class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm transition-colors">
                                    <i class="fas fa-chevron-down mr-1" id="${cardId}-icon"></i>
                                    <span id="${cardId}-text">View Details</span>
                                </button>
                                <button onclick="startConsultationFromTriage(${assessment.id})" 
                                    class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm transition-colors">
                                    <i class="fas fa-stethoscope mr-1"></i>Consult
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Expandable Detailed Content -->
                    <div id="${cardId}-details" class="hidden border-t border-blue-200 bg-white">
                        <div class="p-4 space-y-4">
                            <!-- Organized Sections -->
                            ${parsedData.symptoms.length > 0 ? `
                                <div class="bg-yellow-50 rounded-lg p-3 border-l-4 border-yellow-400">
                                    <h4 class="font-semibold text-yellow-800 mb-2">
                                        <i class="fas fa-list-ul mr-2"></i>Symptoms
                                    </h4>
                                    <ul class="space-y-1">
                                        ${parsedData.symptoms.map(symptom => `<li class="text-sm text-yellow-700">• ${symptom}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}

                            ${parsedData.vitalSigns.length > 0 ? `
                                <div class="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                                    <h4 class="font-semibold text-green-800 mb-2">
                                        <i class="fas fa-heartbeat mr-2"></i>Vital Signs
                                    </h4>
                                    <ul class="space-y-1">
                                        ${parsedData.vitalSigns.map(vital => `<li class="text-sm text-green-700">• ${vital}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}

                            ${parsedData.redFlags.length > 0 ? `
                                <div class="bg-red-50 rounded-lg p-3 border-l-4 border-red-400">
                                    <h4 class="font-semibold text-red-800 mb-2">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>Red Flags & Critical Issues
                                    </h4>
                                    <ul class="space-y-1">
                                        ${parsedData.redFlags.map(flag => `<li class="text-sm text-red-700">• ${flag}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}

                            ${parsedData.recommendations.length > 0 ? `
                                <div class="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-400">
                                    <h4 class="font-semibold text-blue-800 mb-2">
                                        <i class="fas fa-clipboard-list mr-2"></i>Recommended Actions
                                    </h4>
                                    <ol class="space-y-1">
                                        ${parsedData.recommendations.slice(0, 5).map((rec, i) => `<li class="text-sm text-blue-700">${i + 1}. ${rec}</li>`).join('')}
                                        ${parsedData.recommendations.length > 5 ? `<li class="text-sm text-blue-600 italic">... and ${parsedData.recommendations.length - 5} more recommendations</li>` : ''}
                                    </ol>
                                </div>
                            ` : ''}

                            <!-- Full Report Button -->
                            <div class="pt-3 border-t border-gray-200 text-center">
                                <button onclick="showFullTriageReport(${assessment.id}, this)" 
                                    data-assessment-text="${btoa(encodeURIComponent(assessment.assessment || ''))}"
                                    data-assessment-type="${assessmentType.label}"
                                    class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm transition-colors">
                                    <i class="fas fa-file-medical-alt mr-2"></i>View Full Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Toggle triage details visibility
        function toggleTriageDetails(cardId) {
            const detailsDiv = document.getElementById(`${cardId}-details`);
            const icon = document.getElementById(`${cardId}-icon`);
            const text = document.getElementById(`${cardId}-text`);
            
            if (detailsDiv.classList.contains('hidden')) {
                detailsDiv.classList.remove('hidden');
                icon.className = 'fas fa-chevron-up mr-1';
                text.textContent = 'Hide Details';
            } else {
                detailsDiv.classList.add('hidden');
                icon.className = 'fas fa-chevron-down mr-1';
                text.textContent = 'View Details';
            }
        }

        // Show full triage report in modal
        function showFullTriageReport(assessmentId, buttonElement) {
            try {
                // Safely retrieve assessment data from button's data attributes
                const encodedText = buttonElement.getAttribute('data-assessment-text');
                const assessmentType = buttonElement.getAttribute('data-assessment-type');
                
                // Decode the assessment text
                const assessmentText = encodedText ? decodeURIComponent(atob(encodedText)) : '';
                
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
                modal.id = 'fullReportModal';
                
                // Format the assessment text with proper styling
                const formattedText = formatAssessmentText(assessmentText);
                
                modal.innerHTML = `
                    <div class="relative top-10 mx-auto p-6 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-800">
                                <i class="fas fa-file-medical-alt mr-2 text-blue-600"></i>
                                ${assessmentType} - Full Report
                            </h3>
                            <button onclick="closeFullReportModal()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="prose max-w-none bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                            ${formattedText}
                        </div>
                        <div class="mt-4 flex justify-end space-x-2">
                            <button onclick="printTriageReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-print mr-2"></i>Print
                            </button>
                            <button onclick="closeFullReportModal()" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                                Close
                            </button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                
            } catch (error) {
                console.error('Error showing full triage report:', error);
                showNotification('Failed to load full report', 'error');
            }
        }

        // Format assessment text for better readability
        function formatAssessmentText(text) {
            if (!text) return '<p class="text-gray-500">No assessment text available</p>';
            
            return text
                // Format main section headers
                .replace(/\*\*(\d+\.\s*[^*\n]+)\*\*/g, '<h3 class="text-lg font-bold text-blue-800 mt-4 mb-2 border-b border-blue-200 pb-1">$1</h3>')
                // Format subsection headers
                .replace(/\*\*([^*\n]+)\*\*/g, '<h4 class="text-md font-semibold text-blue-700 mt-3 mb-1">$1</h4>')
                // Format bullet points
                .replace(/\*\s+([^\n]+)/g, '<li class="ml-5 list-disc text-gray-700 mb-1">$1</li>')
                // Convert double newlines to paragraphs
                .replace(/\n\n/g, '</p><p class="mb-2">')
                // Convert single newlines to breaks
                .replace(/\n/g, '<br>')
                // Wrap in paragraph tags
                .replace(/^/, '<p class="mb-2">')
                .replace(/$/, '</p>');
        }

        // Close full report modal
        function closeFullReportModal() {
            const modal = document.getElementById('fullReportModal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // Print triage report
        function printTriageReport() {
            const modal = document.getElementById('triageResultsModal');
            const content = modal.querySelector('.overflow-y-auto').innerHTML;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Triage Report</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .no-print { display: none; }
                        h2, h3 { color: #333; }
                        .grid { display: block; }
                        .card { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                        .icon { display: none; }
                    </style>
                </head>
                <body>
                    <h1>AI Triage Analysis Report</h1>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                    <div>${content}</div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // View full consultation details
        function viewFullConsultation(consultationId) {
            if (consultationId) {
                closeTriageResultsModal();
                // Navigate to consultation view or expand consultation section
                window.location.href = `/consultation.html?id=${consultationId}`;
            }
        }

        // Make functions globally available
        window.toggleTriageDetails = toggleTriageDetails;
        window.showFullTriageReport = showFullTriageReport;
        window.closeFullReportModal = closeFullReportModal;
        window.printTriageReport = printTriageReport;
        // NOTE: startConsultationFromTriage is already defined as async function above

        // Display medical consultations
        function displayMedicalConsultations(consultations) {
            const consultationList = document.getElementById('medicalConsultationsList');
            
            // Update summary badge
            document.getElementById('consultationsSummary').textContent = `${consultations.length} consultation${consultations.length !== 1 ? 's' : ''}`;
            
            if (consultations.length === 0) {
                consultationList.innerHTML = `
                    <div class="text-center py-6 text-gray-500">
                        <i class="fas fa-stethoscope text-2xl mb-2 text-green-300"></i>
                        <p class="text-sm">No medical consultations found</p>
                    </div>
                `;
                return;
            }

            consultationList.innerHTML = consultations.map(consultation => {
                const status = consultation.status || 'Unknown';
                const statusColor = getStatusColor(status);
                
                return `
                    <div class="border-l-4 border-green-400 bg-green-50 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-stethoscope text-green-600"></i>
                                    <span class="font-medium text-green-800">Medical Consultation</span>
                                </div>
                                <div class="px-3 py-1 ${statusColor.bg} ${statusColor.text} rounded-full text-sm font-medium">
                                    ${status.toUpperCase()}
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">
                                ${new Date(consultation.created_at).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </span>
                        </div>
                        
                        ${consultation.diagnosis ? `
                            <div class="mb-3">
                                <span class="font-medium text-gray-700">Diagnosis:</span>
                                <p class="text-gray-600 mt-1">${consultation.diagnosis}</p>
                            </div>
                        ` : ''}
                        
                        ${consultation.treatment_plan ? `
                            <div class="mb-3">
                                <span class="font-medium text-gray-700">Treatment Plan:</span>
                                <p class="text-gray-600 mt-1">${consultation.treatment_plan}</p>
                            </div>
                        ` : ''}
                        
                        ${consultation.notes ? `
                            <div class="mb-3">
                                <span class="font-medium text-gray-700">Notes:</span>
                                <p class="text-gray-600 mt-1">${consultation.notes}</p>
                            </div>
                        ` : ''}
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 mt-3 pt-3 border-t border-green-200">
                            <span>Consulting Doctor: ${consultation.doctor_name || 'Unknown'}</span>
                            <span>Duration: ${consultation.duration || 'N/A'}</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Get assessment type info
        function getAssessmentType(consultationType) {
            switch (consultationType) {
                case 'voice_triage':
                    return { icon: 'fas fa-microphone', label: 'AI Voice Triage' };
                case 'manual_ctas':
                    return { icon: 'fas fa-clipboard-list', label: 'Manual CTAS Assessment' };
                default:
                    return { icon: 'fas fa-clipboard-check', label: 'Triage Assessment' };
            }
        }

        // Get status color for medical consultations
        function getStatusColor(status) {
            switch (status.toLowerCase()) {
                case 'completed':
                    return { bg: 'bg-green-100', text: 'text-green-800' };
                case 'in_progress':
                    return { bg: 'bg-yellow-100', text: 'text-yellow-800' };
                case 'pending':
                    return { bg: 'bg-blue-100', text: 'text-blue-800' };
                default:
                    return { bg: 'bg-gray-100', text: 'text-gray-800' };
            }
        }

        // Get priority color (keeping existing function for triage assessments)
        function getPriorityColor(priority) {
            const priorityMap = {
                'CTAS 1 - RESUSCITATION': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-500' },
                'CTAS 2 - EMERGENT': { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-500' },
                'CTAS 3 - URGENT': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-500' },
                'CTAS 4 - LESS URGENT': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-500' },
                'CTAS 5 - NON-URGENT': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-500' }
            };
            return priorityMap[priority] || { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-400' };
        }

        // Add sorting functionality for both sections
        document.addEventListener('DOMContentLoaded', function() {
            // Load credits first
            loadCredits();
            
            // Triage assessments sorting
            const triageSort = document.getElementById('triageSort');
            if (triageSort) {
                triageSort.addEventListener('change', (e) => {
                    sortTriageAssessments(e.target.value);
                });
            }

            // Medical consultations sorting
            const consultationSort = document.getElementById('consultationSort');
            if (consultationSort) {
                consultationSort.addEventListener('change', (e) => {
                    sortMedicalConsultations(e.target.value);
                });
            }

            // Initialize time filter
            initializeTimeFilter();

            // Only load data if we have a valid patient ID
            // For triage-based access, data loading is handled by fetchPatientIdFromTriage()
            if (patientId && patientId !== 'null') {
                loadPatientDetails();
                loadConsultations();
                loadPatientMedications();
                loadPatientDocuments();
            }
        });

        // Global storage for sorted data
        let currentTriageAssessments = [];
        let currentMedicalConsultations = [];

        // Enhanced loadConsultations to store data for sorting and filtering
        async function loadConsultationsEnhanced() {
            // Validate patient ID before making API call
            if (!patientId || patientId === 'null' || patientId === 'undefined') {
                console.log('loadConsultationsEnhanced: No valid patient ID, skipping');
                return;
            }
            
            try {
                const response = await fetch(`/api/consultations/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('doctorToken')}`
                    }
                });

                if (response.ok) {
                    const consultations = await response.json();
                    
                    // Separate and store all data in global arrays for filtering
                    allConsultations = [];
                    allTriages = [];
                    currentTriageAssessments = [];
                    currentMedicalConsultations = [];
                    
                    consultations.forEach(consultation => {
                        if (consultation.consultation_type === 'doctor_consultation') {
                            allConsultations.push(consultation);
                            currentMedicalConsultations.push(consultation);
                        } else {
                            allTriages.push(consultation);
                            currentTriageAssessments.push(consultation);
                        }
                    });
                    
                    // Apply current filter (if any) and display
                    filterAllData();
                } else {
                    console.error('Failed to load consultations');
                }
            } catch (error) {
                console.error('Error loading consultations:', error);
            }
        }

        // Sort triage assessments
        function sortTriageAssessments(sortBy) {
            let sortedAssessments = [...currentTriageAssessments];
            
            if (sortBy === 'priority') {
                const priorityOrder = ['CTAS 1', 'CTAS 2', 'CTAS 3', 'CTAS 4', 'CTAS 5'];
                sortedAssessments.sort((a, b) => {
                    const priorityA = a.priority || 'CTAS 5';
                    const priorityB = b.priority || 'CTAS 5';
                    
                    const orderA = priorityOrder.findIndex(p => priorityA.includes(p));
                    const orderB = priorityOrder.findIndex(p => priorityB.includes(p));
                    
                    return orderA - orderB;
                });
            } else {
                // Most recent
                sortedAssessments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            }
            
            displayTriageAssessments(sortedAssessments);
        }

        // Sort medical consultations
        function sortMedicalConsultations(sortBy) {
            let sortedConsultations = [...currentMedicalConsultations];
            
            if (sortBy === 'status') {
                const statusOrder = ['pending', 'in_progress', 'completed'];
                sortedConsultations.sort((a, b) => {
                    const statusA = (a.status || 'pending').toLowerCase();
                    const statusB = (b.status || 'pending').toLowerCase();
                    
                    const orderA = statusOrder.indexOf(statusA);
                    const orderB = statusOrder.indexOf(statusB);
                    
                    return orderA - orderB;
                });
            } else {
                // Most recent
                sortedConsultations.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            }
            
            displayMedicalConsultations(sortedConsultations);
        }

        // Replace the original loadConsultations with the enhanced version
        const originalLoadConsultations = loadConsultations;
        loadConsultations = loadConsultationsEnhanced;

        // Display patient documents function (placeholder for now)
        function displayPatientDocuments(documents) {
            // For now, just update the count since documents section exists but may not have display logic
            // This can be enhanced later when document display is implemented
            console.log('Filtered documents:', documents.length);
        }

        // Parse triage analysis into sections
        function parseTriageAnalysis(analysisText) {
            const sections = {};
            
            // Extract sections using regex patterns - improved to handle malformed AI output
            const patterns = {
                chiefComplaint: /(?:\d+\.\s*)?CHIEF COMPLAINT[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                symptoms: /(?:\d+\.\s*)?SYMPTOMS[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                painAssessment: /(?:\d+\.\s*)?PAIN ASSESSMENT[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                vitalSigns: /(?:\d+\.\s*)?VITAL SIGNS MENTIONED[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                medicalHistory: /(?:\d+\.\s*)?MEDICAL HISTORY[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                allergies: /(?:\d+\.\s*)?ALLERGIES[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                medications: /(?:\d+\.\s*)?CURRENT MEDICATIONS[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                medicalScoring: /(?:\d+\.\s*)?RELEVANT MEDICAL SCORING SYSTEMS[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                recommendedActions: /(?:\d+\.\s*)?RECOMMENDED ACTIONS[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                redFlags: /(?:\d+\.\s*)?RED FLAGS[^:]*[:\s]*(.*?)(?=\*\*\d+\.|$)/is,
                additionalNotes: /(?:\d+\.\s*)?ADDITIONAL NOTES[:\s]*(.*?)(?=\*\*\d+\.|$)/is
            };
            
            for (const [key, pattern] of Object.entries(patterns)) {
                const match = analysisText.match(pattern);
                if (match && match[1]) {
                    sections[key] = match[1].trim().replace(/\n+/g, ' ').replace(/\s+/g, ' ');
                }
            }
            
            return sections;
        }

        // Parse and format medical scoring systems
        function parseMedicalScoring(scoringText) {
            // Debug logging
            console.log('Medical Scoring Text:', scoringText);
            
            if (!scoringText) {
                return null;
            }

            // Check if AI says "no relevant scoring system identified"
            if (scoringText.toLowerCase().includes('no relevant scoring system identified')) {
                // Determine if this is due to insufficient data or truly not applicable
                const isInsufficientData = scoringText.toLowerCase().includes('without further information') ||
                                         scoringText.toLowerCase().includes('once more data is gathered') ||
                                         scoringText.toLowerCase().includes('at this stage') ||
                                         scoringText.toLowerCase().includes('may become apparent') ||
                                         scoringText.toLowerCase().includes('more appropriate scoring system');

                if (isInsufficientData) {
                    // AI indicates scoring systems could be relevant with more data
                    const analysisContext = document.querySelector('.prose pre')?.textContent || scoringText;
                    return generateScoringSuggestions(analysisContext);
                }

                // Even if AI doesn't explicitly mention insufficient data, 
                // check if clinical context warrants scoring suggestions
                const analysisContext = document.querySelector('.prose pre')?.textContent || scoringText;
                const contextLower = analysisContext.toLowerCase();
                
                // Check for high-yield clinical scenarios that warrant scoring
                const shouldSuggestScoring = 
                    (contextLower.includes('chest pain') && contextLower.includes('shortness of breath')) ||
                    (contextLower.includes('chest pain') && contextLower.includes('exertion')) ||
                    (contextLower.includes('chest pain') && contextLower.includes('cardiac')) ||
                    (contextLower.includes('dyspnea') && contextLower.includes('pulmonary')) ||
                    (contextLower.includes('stroke') || contextLower.includes('neurological')) ||
                    (contextLower.includes('sepsis') || contextLower.includes('infection'));

                if (shouldSuggestScoring) {
                    return generateScoringSuggestions(analysisContext);
                }
                
                return null; // Truly not applicable
            }

            // If we have actual scoring content, don't show fallback suggestions

            // Clean up the text
            const cleanText = scoringText.trim().replace(/\n+/g, '\n').replace(/\s+/g, ' ');
            
            // Try to extract scoring system information
            const scoringSystems = [];
            
            // Enhanced patterns for AI-generated scoring systems
            const patterns = [
                // Pattern for **System Name:** format
                /\*\*([^*:]+(?:Score|Criteria|Classification|Scale)[^*:]*)\*\*[:\s]*(.*?)(?=\s*\*\*|$)/gi,
                // Pattern for bullet points with scoring systems
                /\*\s*\*\*([^*:]+(?:Score|Criteria|Classification|Scale)[^*:]*)\*\*[:\s]*(.*?)(?=\s*\*|$)/gi,
                // Traditional patterns
                /([A-Z][A-Za-z\s\-\d]+(?:Score|Criteria|Classification|Scale))[:\s]+(.*?)(?=\n[A-Z]|$)/gi,
                // CURB-65, Wells Score, etc.
                /([A-Z-]+(?:\d+)?(?:[-\s][A-Z]+)*)\s*(?:Score|Criteria)?[:\s]+(.*?)(?=\n[A-Z]|$)/gi
            ];

            for (const pattern of patterns) {
                let match;
                while ((match = pattern.exec(cleanText)) !== null) {
                    const systemName = match[1].trim();
                    const details = match[2].trim();
                    
                    if (systemName.length > 2 && details.length > 5) {
                        // Extract total score and interpretation if available
                        const totalMatch = details.match(/Total\s*(?:Score)?[:\s]*(\d+(?:\/\d+)?)/i);
                        const interpretationMatch = details.match(/Interpretation[:\s]*(.+?)(?=\n|$)/i);
                        
                        scoringSystems.push({
                            name: systemName,
                            details: details,
                            totalScore: totalMatch ? totalMatch[1] : null,
                            interpretation: interpretationMatch ? interpretationMatch[1].trim() : null
                        });
                    }
                }
            }

            // Remove duplicates based on system name
            const uniqueSystems = [];
            const seenNames = new Set();
            
            for (const system of scoringSystems) {
                const normalizedName = system.name.toLowerCase().replace(/[^a-z0-9]/g, '');
                if (!seenNames.has(normalizedName)) {
                    seenNames.add(normalizedName);
                    uniqueSystems.push(system);
                }
            }

            // If we have extracted systems, return them
            if (uniqueSystems.length > 0) {
                return uniqueSystems;
            }

            // If no structured scoring found but we have content, return as general assessment
            if (cleanText.length > 20) {
                return [{
                    name: 'Clinical Scoring Assessment',
                    details: cleanText,
                    totalScore: null,
                    interpretation: null
                }];
            }

            return null;
        }

        // Generate scoring system suggestions based on clinical context
        function generateScoringSuggestions(analysisContext) {
            const contextLower = analysisContext.toLowerCase();
            const suggestions = [];
            
            // 🚨 Emergency & Triage - High Priority
            if (contextLower.includes('sepsis') || contextLower.includes('systemic infection') ||
                (contextLower.includes('fever') && (contextLower.includes('altered mental') || contextLower.includes('hypotension')))) {
                suggestions.push('qSOFA (rapid sepsis risk: ≥2 = high mortality risk)');
            }
            
            if (contextLower.includes('deteriorating') || contextLower.includes('vital signs') || 
                contextLower.includes('early warning') || contextLower.includes('icu')) {
                suggestions.push('NEWS2 (general clinical deterioration early warning)');
            }
            
            if (contextLower.includes('triage') || contextLower.includes('emergency department') || 
                contextLower.includes('priority')) {
                suggestions.push('ESI (Emergency Severity Index: 5-level triage)');
            }
            
            // ❤️ Cardiac & Chest Pain
            if (contextLower.includes('chest pain')) {
                suggestions.push('HEART Score (chest pain risk stratification)');
                
                if (contextLower.includes('acs') || contextLower.includes('acute coronary') || 
                    contextLower.includes('nstemi') || contextLower.includes('unstable angina')) {
                    suggestions.push('TIMI Score (ACS prognosis and risk of adverse events)');
                    suggestions.push('GRACE Score (ACS mortality risk - more complex but validated)');
                }
                
                if (contextLower.includes('aortic') || contextLower.includes('dissection') || 
                    contextLower.includes('back pain')) {
                    suggestions.push('ADD-RS (aortic dissection detection in chest/back pain)');
                }
            }
            
            // 🫁 Pulmonary / PE / DVT
            if (contextLower.includes('shortness of breath') || contextLower.includes('dyspnea') || 
                contextLower.includes('pulmonary embolism') || contextLower.includes('pe') || contextLower.includes('dvt')) {
                suggestions.push('Wells Score (PE or DVT probability: <2 low, ≥6 high)');
                suggestions.push('PERC Rule (rule out PE - use only in low-risk patients)');
                suggestions.push('YEARS Algorithm (rule out PE with age-adjusted D-dimer)');
                suggestions.push('BOVA Score (PE short-term complications/RV dysfunction risk)');
            }
            
            // 🤒 Infectious Diseases / Pneumonia
            if (contextLower.includes('pneumonia') || contextLower.includes('cap') || 
                (contextLower.includes('respiratory') && contextLower.includes('infection'))) {
                suggestions.push('CURB-65 (CAP severity & admission: ≥2 → hospitalize)');
                suggestions.push('PSI/PORT Score (pneumonia mortality - more comprehensive than CURB-65)');
            }
            
            // 🧠 Neurologic / Trauma
            if (contextLower.includes('consciousness') || contextLower.includes('altered mental') || 
                contextLower.includes('gcs') || contextLower.includes('coma')) {
                suggestions.push('GCS (Glasgow Coma Scale: level of consciousness 3-15)');
            }
            
            // 🧬 Stroke & Vascular
            if (contextLower.includes('stroke') || contextLower.includes('cva') || contextLower.includes('neurological deficit')) {
                suggestions.push('NIHSS (stroke severity assessment)');
            }
            
            if (contextLower.includes('atrial fibrillation') || contextLower.includes('afib')) {
                suggestions.push('CHA₂DS₂-VASc (stroke risk in AFib - guide anticoagulation)');
                suggestions.push('HAS-BLED (bleeding risk in AFib patients on anticoagulation)');
            }
            
            if (contextLower.includes('syncope') || contextLower.includes('fainting')) {
                suggestions.push('Canadian Syncope Risk Score (risk stratification after syncope)');
            }
            
            // Trauma
            if (contextLower.includes('head trauma') || contextLower.includes('head injury')) {
                suggestions.push('Canadian CT Head Rule (head CT decision post-trauma: GCS 13-15)');
                if (contextLower.includes('child') || contextLower.includes('pediatric')) {
                    suggestions.push('PECARN (pediatric head trauma CT decision - age-specific)');
                }
            }
            
            if (contextLower.includes('neck') || contextLower.includes('cervical') || contextLower.includes('c-spine')) {
                suggestions.push('Canadian C-Spine Rule (C-spine clearance after trauma if alert/stable)');
                suggestions.push('NEXUS Criteria (C-spine injury rule-out - if all negative → no imaging)');
            }
            
            // 🔥 Abdomen & Sepsis
            if (contextLower.includes('abdominal pain') || contextLower.includes('right lower quadrant') || 
                contextLower.includes('appendicitis')) {
                suggestions.push('Alvarado Score (appendicitis probability: ≥7 = probable)');
                if (contextLower.includes('child') || contextLower.includes('pediatric')) {
                    suggestions.push('Pediatric Appendicitis Score (PAS: similar to Alvarado but pediatric)');
                }
            }
            
            if (contextLower.includes('pancreatitis')) {
                suggestions.push('Ranson Criteria (pancreatitis severity: 48h inpatient mortality predictor)');
            }
            
            if (contextLower.includes('necrotizing fasciitis') || contextLower.includes('soft tissue infection') ||
                contextLower.includes('cellulitis')) {
                suggestions.push('LRINEC Score (necrotizing fasciitis suspicion: ≥6 = concern)');
            }
            
            // 👶 Pediatrics
            if (contextLower.includes('child') || contextLower.includes('pediatric') || contextLower.includes('infant')) {
                if (contextLower.includes('asthma') || contextLower.includes('respiratory distress')) {
                    suggestions.push('PRAM (asthma severity in children: mild/moderate/severe)');
                }
                
                if (contextLower.includes('croup')) {
                    suggestions.push('Westley Croup Score (croup severity: stridor, retractions, etc.)');
                }
                
                if (contextLower.includes('joint') || contextLower.includes('arthritis') || contextLower.includes('septic')) {
                    suggestions.push('Kocher Criteria (predict septic arthritis in children)');
                }
                
                if (contextLower.includes('fever') && contextLower.includes('infant')) {
                    suggestions.push('AAP Febrile Infant Guidelines (evaluation in infants <60 days)');
                    suggestions.push('UTI Calc (predict UTI in febrile children)');
                }
                
                if (contextLower.includes('brue') || contextLower.includes('unexplained event')) {
                    suggestions.push('BRUE Criteria (brief resolved unexplained event in infants)');
                }
            }
            
            // Additional Common Scores
            if (contextLower.includes('sore throat') || contextLower.includes('pharyngitis') || 
                contextLower.includes('strep')) {
                suggestions.push('Centor Criteria (likelihood of streptococcal pharyngitis)');
            }
            
            if (contextLower.includes('alcohol withdrawal') || contextLower.includes('delirium tremens')) {
                suggestions.push('CIWA-Ar (alcohol withdrawal severity)');
            }
            
            if (contextLower.includes('opioid withdrawal') || contextLower.includes('drug withdrawal')) {
                suggestions.push('COWS (opioid withdrawal severity)');
            }

            if (suggestions.length > 0) {
                return [{
                    name: 'Recommended Clinical Scoring',
                    details: `Based on presenting symptoms, consider applying: ${suggestions.join(', ')}.`,
                    totalScore: null,
                    interpretation: 'Apply these scoring systems once complete clinical data is available'
                }];
            }

            return null;
        }

        // Get severity color for scoring systems
        function getScoringSystemColor(systemName, totalScore, interpretation) {
            const name = systemName.toLowerCase();
            const interp = interpretation ? interpretation.toLowerCase() : '';
            
            // High severity indicators
            if (interp.includes('severe') || interp.includes('high risk') || interp.includes('immediate') || 
                interp.includes('critical') || interp.includes('emergency')) {
                return {
                    bg: 'bg-red-50',
                    border: 'border-red-200',
                    text: 'text-red-800',
                    icon: 'text-red-600'
                };
            }
            
            // Medium severity indicators
            if (interp.includes('moderate') || interp.includes('medium') || interp.includes('consider') || 
                interp.includes('monitor') || interp.includes('caution')) {
                return {
                    bg: 'bg-yellow-50',
                    border: 'border-yellow-200',
                    text: 'text-yellow-800',
                    icon: 'text-yellow-600'
                };
            }
            
            // Low severity indicators
            if (interp.includes('low') || interp.includes('minimal') || interp.includes('stable') || 
                interp.includes('reassuring') || interp.includes('normal')) {
                return {
                    bg: 'bg-green-50',
                    border: 'border-green-200',
                    text: 'text-green-800',
                    icon: 'text-green-600'
                };
            }
            
            // Default to blue for neutral/informational
            return {
                bg: 'bg-blue-50',
                border: 'border-blue-200',
                text: 'text-blue-800',
                icon: 'text-blue-600'
            };
        }

        // Get priority styling for modal
        function getPriorityModalStyle(priorityLevel) {
            if (!priorityLevel || typeof priorityLevel !== 'string') {
                priorityLevel = 'medium';
            }
            
            const level = priorityLevel.toLowerCase().trim();
            
            const styles = {
                'immediate': {
                    headerBg: 'bg-red-600',
                    iconBg: 'bg-red-100',
                    cardBg: 'bg-red-50',
                    borderColor: 'border-red-200',
                    textColor: 'text-red-800',
                    icon: 'fas fa-exclamation-triangle'
                },
                'urgent': {
                    headerBg: 'bg-orange-600',
                    iconBg: 'bg-orange-100',
                    cardBg: 'bg-orange-50',
                    borderColor: 'border-orange-200',
                    textColor: 'text-orange-800',
                    icon: 'fas fa-clock'
                },
                'high': {
                    headerBg: 'bg-orange-600',
                    iconBg: 'bg-orange-100',
                    cardBg: 'bg-orange-50',
                    borderColor: 'border-orange-200',
                    textColor: 'text-orange-800',
                    icon: 'fas fa-clock'
                },
                'medium': {
                    headerBg: 'bg-yellow-600',
                    iconBg: 'bg-yellow-100',
                    cardBg: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    textColor: 'text-yellow-800',
                    icon: 'fas fa-hourglass-half'
                },
                'less urgent': {
                    headerBg: 'bg-yellow-600',
                    iconBg: 'bg-yellow-100',
                    cardBg: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    textColor: 'text-yellow-800',
                    icon: 'fas fa-hourglass-half'
                },
                'low': {
                    headerBg: 'bg-green-600',
                    iconBg: 'bg-green-100',
                    cardBg: 'bg-green-50',
                    borderColor: 'border-green-200',
                    textColor: 'text-green-800',
                    icon: 'fas fa-check-circle'
                },
                'non-urgent': {
                    headerBg: 'bg-green-600',
                    iconBg: 'bg-green-100',
                    cardBg: 'bg-green-50',
                    borderColor: 'border-green-200',
                    textColor: 'text-green-800',
                    icon: 'fas fa-check-circle'
                }
            };
            
            // Check for exact matches first
            if (styles[level]) {
                return styles[level];
            }
            
            // Check for partial matches
            if (level.includes('immediate') || level.includes('emergency')) {
                return styles['immediate'];
            } else if (level.includes('urgent') || level.includes('high')) {
                return styles['urgent'];
            } else if (level.includes('low') || level.includes('non-urgent')) {
                return styles['low'];
            } else {
                // Default to medium priority
                return styles['medium'];
            }
        }

        // Close triage results modal
        function closeTriageResultsModal() {
            const modal = document.getElementById('triageResultsModal');
            if (modal) {
                modal.querySelector('.bg-white').classList.add('animate-modalSlideOut');
                setTimeout(() => {
                    modal.remove();
                    showNotification('Triage analysis completed successfully!', 'success');
                }, 300);
            }
        }

        // Toggle full analysis visibility
        function toggleFullAnalysis() {
            const content = document.getElementById('fullAnalysisContent');
            const chevron = document.getElementById('analysisChevron');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                chevron.classList.replace('fa-chevron-down', 'fa-chevron-up');
            } else {
                content.classList.add('hidden');
                chevron.classList.replace('fa-chevron-up', 'fa-chevron-down');
            }
        }

        // ========== DOCUMENT UPLOAD FUNCTIONALITY ==========
        
        let selectedFiles = [];
        
        // Document upload event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Upload button click
            document.getElementById('uploadDocumentBtn').addEventListener('click', function() {
                const form = document.getElementById('documentUploadForm');
                // Always show the form when upload button is clicked
                form.classList.remove('hidden');
                // Don't automatically trigger file picker - let user click "Choose Files" button
            });
            
            // File selection
            document.getElementById('documentFiles').addEventListener('change', function(e) {
                selectedFiles = Array.from(e.target.files);
                displaySelectedFiles();
            });
            
            // Upload files button
            document.getElementById('uploadFiles').addEventListener('click', uploadDocuments);
            
            // Cancel upload button
            document.getElementById('cancelUpload').addEventListener('click', function() {
                selectedFiles = [];
                document.getElementById('documentFiles').value = '';
                document.getElementById('selectedFiles').classList.add('hidden');
                document.getElementById('documentUploadForm').classList.add('hidden');
            });
            
            // Load existing documents
            loadPatientDocuments();
        });
        
        // Display selected files
        function displaySelectedFiles() {
            const filesList = document.getElementById('filesList');
            const selectedFilesDiv = document.getElementById('selectedFiles');
            
            if (selectedFiles.length === 0) {
                selectedFilesDiv.classList.add('hidden');
                return;
            }
            
            selectedFilesDiv.classList.remove('hidden');
            filesList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between bg-white p-3 rounded border';
                fileItem.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-file-alt text-purple-600 mr-3"></i>
                        <div>
                            <div class="font-medium">${file.name}</div>
                            <div class="text-sm text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        </div>
                    </div>
                    <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                filesList.appendChild(fileItem);
            });
        }
        
        // Remove file from selection
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displaySelectedFiles();
            
            // Update file input
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            document.getElementById('documentFiles').files = dt.files;
        }
        
        // Upload documents
        async function uploadDocuments() {
            if (selectedFiles.length === 0) {
                showNotification('Please select files to upload', 'warning');
                return;
            }
            
            const token = checkAuth();
            if (!token) return;
            
            // Show progress but keep form open
            document.getElementById('uploadProgress').classList.remove('hidden');
            
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('documents', file);
            });
            
            try {
                // Simulate progress
                updateProgress(20);
                
                const response = await fetch(`/api/documents/patients/${patientId}/documents/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                updateProgress(80);
                
                if (response.ok) {
                    const result = await response.json();
                    updateProgress(100);
                    
                    setTimeout(() => {
                        document.getElementById('uploadProgress').classList.add('hidden');
                        document.getElementById('documentUploadForm').classList.add('hidden');
                        selectedFiles = [];
                        document.getElementById('documentFiles').value = '';
                        document.getElementById('selectedFiles').classList.add('hidden');
                        
                        showNotification(`Successfully processed ${result.results.length} documents`, 'success');
                        loadPatientDocuments();
                    }, 1000);
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Upload failed');
                }
            } catch (error) {
                console.error('Document upload error:', error);
                document.getElementById('uploadProgress').classList.add('hidden');
                // Don't close the form on error - let user try again
                showNotification(`Upload failed: ${error.message}`, 'error');
            }
        }
        
        // Update progress bar
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = `${percentage}%`;
        }
        
        // Load patient documents
        async function loadPatientDocuments() {
            const token = checkAuth();
            if (!token) return;
            
            // Validate patient ID before making API call
            if (!patientId || patientId === 'null' || patientId === 'undefined') {
                console.log('loadPatientDocuments: No valid patient ID, skipping');
                return;
            }
            
            try {
                const response = await fetch(`/api/documents/patients/${patientId}/documents`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayDocuments(data.documents);
                } else {
                    console.error('Failed to load documents');
                }
            } catch (error) {
                console.error('Error loading documents:', error);
            }
        }
        
        // Display documents
        function displayDocuments(documents) {
            const documentsList = document.getElementById('documentsList');
            const noDocuments = document.getElementById('noDocuments');
            
            // Update summary badge
            const docCount = documents ? documents.length : 0;
            document.getElementById('documentsSummary').textContent = `${docCount} document${docCount !== 1 ? 's' : ''}`;
            
            if (!documents || documents.length === 0) {
                noDocuments.classList.remove('hidden');
                return;
            }
            
            noDocuments.classList.add('hidden');
            
            // Clear existing documents except the no documents message
            const existingDocs = documentsList.querySelectorAll('.document-card');
            existingDocs.forEach(doc => doc.remove());
            
            documents.forEach(doc => {
                const docCard = createDocumentCard(doc);
                documentsList.insertBefore(docCard, noDocuments);
            });
        }
        
        // Create document card
        function createDocumentCard(doc) {
            const card = document.createElement('div');
            card.className = 'document-card bg-white border rounded-lg p-4 hover:shadow-md transition-shadow duration-200';
            
            const statusColor = {
                'completed': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'processing': 'bg-yellow-100 text-yellow-800'
            }[doc.extraction_status] || 'bg-gray-100 text-gray-800';
            
            const typeIcon = {
                'lab_report': 'fa-vial',
                'prescription': 'fa-prescription',
                'radiology': 'fa-x-ray',
                'referral': 'fa-file-medical'
            }[doc.document_type] || 'fa-file-alt';
            
            card.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center">
                        <i class="fas ${typeIcon} text-purple-600 mr-3 text-lg"></i>
                        <div>
                            <h4 class="font-semibold text-gray-800">${doc.document_name}</h4>
                            <p class="text-sm text-gray-500">${doc.original_filename} (${doc.file_size_mb}MB)</p>
                        </div>
                    </div>
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${statusColor}">
                        ${doc.extraction_status}
                    </span>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm">
                    <div>
                        <span class="text-gray-500">Type:</span>
                        <span class="font-medium">${doc.document_type.replace('_', ' ').toUpperCase()}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">Uploaded:</span>
                        <span class="font-medium">${new Date(doc.upload_date).toLocaleDateString()}</span>
                    </div>

                    <div class="flex space-x-2">
                        <button onclick="viewDocument(${doc.id})" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteDocument(${doc.id})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                ${doc.extraction_status === 'completed' ? `
                    <div class="bg-gray-50 rounded p-3 text-sm">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            ${(() => {
                                let vitalSigns = doc.vital_signs;
                                if (typeof vitalSigns === 'string') {
                                    try { vitalSigns = JSON.parse(vitalSigns); } catch(e) { vitalSigns = {}; }
                                }
                                return vitalSigns && Object.keys(vitalSigns).length > 0 ? `
                                    <div>
                                        <span class="font-medium text-blue-600">Vital Signs:</span>
                                        ${Object.entries(vitalSigns).map(([key, value]) => {
                                            if (typeof value === 'object' && value !== null) {
                                                return Object.entries(value).map(([subKey, subValue]) => 
                                                    `<span class="ml-2">${subKey}: ${subValue}</span>`
                                                ).join(', ');
                                            } else {
                                                return `<span class="ml-2">${key}: ${value}</span>`;
                                            }
                                        }).join(', ')}
                                    </div>
                                ` : '';
                            })()}
                            ${(() => {
                                let labValues = doc.lab_values;
                                if (typeof labValues === 'string') {
                                    try { labValues = JSON.parse(labValues); } catch(e) { labValues = {}; }
                                }
                                return labValues && Object.keys(labValues).length > 0 ? `
                                    <div>
                                        <span class="font-medium text-green-600">Lab Values:</span>
                                        ${Object.entries(labValues).map(([key, value]) => {
                                            if (typeof value === 'object' && value !== null) {
                                                return Object.entries(value).map(([subKey, subValue]) => 
                                                    `<span class="ml-2">${subKey}: ${subValue}</span>`
                                                ).join(', ');
                                            } else {
                                                return `<span class="ml-2">${key}: ${value}</span>`;
                                            }
                                        }).join(', ')}
                                    </div>
                                ` : '';
                            })()}
                            ${(() => {
                                let medications = doc.medications;
                                if (typeof medications === 'string') {
                                    try { medications = JSON.parse(medications); } catch(e) { medications = []; }
                                }
                                return medications && Array.isArray(medications) && medications.length > 0 ? `
                                    <div>
                                        <span class="font-medium text-purple-600">Medications:</span>
                                        ${medications.map(med => `<span class="ml-2">${med}</span>`).join(', ')}
                                    </div>
                                ` : '';
                            })()}
                            ${(() => {
                                let diagnoses = doc.diagnoses;
                                if (typeof diagnoses === 'string') {
                                    try { diagnoses = JSON.parse(diagnoses); } catch(e) { diagnoses = []; }
                                }
                                return diagnoses && Array.isArray(diagnoses) && diagnoses.length > 0 ? `
                                    <div>
                                        <span class="font-medium text-red-600">Diagnoses:</span>
                                        ${diagnoses.map(diag => `<span class="ml-2">${diag}</span>`).join(', ')}
                                    </div>
                                ` : '';
                            })()}
                            ${doc.severity && doc.severity !== 'unknown' ? `
                                <div>
                                    <span class="font-medium text-orange-600">Severity:</span>
                                    <span class="ml-2 px-2 py-1 rounded text-xs font-medium ${
                                        doc.severity === 'critical' ? 'bg-red-100 text-red-800' :
                                        doc.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                                        doc.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-green-100 text-green-800'
                                    }">${doc.severity.toUpperCase()}</span>
                                </div>
                            ` : ''}
                            ${doc.clinical_urgency && doc.clinical_urgency !== 'routine' ? `
                                <div>
                                    <span class="font-medium text-purple-600">Urgency:</span>
                                    <span class="ml-2 px-2 py-1 rounded text-xs font-medium ${
                                        doc.clinical_urgency === 'emergency' ? 'bg-red-100 text-red-800' :
                                        doc.clinical_urgency === 'urgent' ? 'bg-orange-100 text-orange-800' :
                                        'bg-blue-100 text-blue-800'
                                    }">${doc.clinical_urgency.toUpperCase()}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                ` : ''}
                
                ${doc.error_message ? `
                    <div class="bg-red-50 border border-red-200 rounded p-3 mt-3">
                        <p class="text-red-800 text-sm">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            ${doc.error_message}
                        </p>
                    </div>
                ` : ''}
            `;
            
            return card;
        }
        
        // View document details
        async function viewDocument(docId) {
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch(`/api/documents/patients/${patientId}/documents/${docId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const doc = await response.json();
                    showDocumentModal(doc);
                } else {
                    showNotification('Failed to load document details', 'error');
                }
            } catch (error) {
                console.error('Error viewing document:', error);
                showNotification('Error loading document', 'error');
            }
        }
        
        // Show document modal
        function showDocumentModal(doc) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold text-gray-900">${doc.document_name}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="max-h-96 overflow-y-auto mb-4">
                        <h4 class="font-semibold mb-2">Extracted Text:</h4>
                        <div class="bg-gray-50 p-3 rounded text-sm whitespace-pre-wrap">${doc.extracted_text}</div>
                        
                        ${doc.extraction_status === 'completed' ? `
                            <div class="mt-4 space-y-3">
                                ${(() => {
                                    let vitalSigns = doc.vital_signs;
                                    if (typeof vitalSigns === 'string') {
                                        try { vitalSigns = JSON.parse(vitalSigns); } catch(e) { vitalSigns = {}; }
                                    }
                                    return vitalSigns && Object.keys(vitalSigns).length > 0 ? `
                                        <div>
                                            <h5 class="font-semibold text-blue-600">Vital Signs:</h5>
                                            <div class="bg-blue-50 p-2 rounded text-sm">
                                                ${Object.entries(vitalSigns).map(([key, value]) => {
                                                    if (typeof value === 'object' && value !== null) {
                                                        return Object.entries(value).map(([subKey, subValue]) => 
                                                            `<div>${subKey}: <span class="font-medium">${subValue}</span></div>`
                                                        ).join('');
                                                    } else {
                                                        return `<div>${key}: <span class="font-medium">${value}</span></div>`;
                                                    }
                                                }).join('')}
                                            </div>
                                        </div>
                                    ` : '';
                                })()}
                                
                                ${(() => {
                                    let labValues = doc.lab_values;
                                    if (typeof labValues === 'string') {
                                        try { labValues = JSON.parse(labValues); } catch(e) { labValues = {}; }
                                    }
                                    return labValues && Object.keys(labValues).length > 0 ? `
                                        <div>
                                            <h5 class="font-semibold text-green-600">Lab Values:</h5>
                                            <div class="bg-green-50 p-2 rounded text-sm">
                                                ${Object.entries(labValues).map(([key, value]) => {
                                                    if (typeof value === 'object' && value !== null) {
                                                        return Object.entries(value).map(([subKey, subValue]) => 
                                                            `<div>${subKey}: <span class="font-medium">${subValue}</span></div>`
                                                        ).join('');
                                                    } else {
                                                        return `<div>${key}: <span class="font-medium">${value}</span></div>`;
                                                    }
                                                }).join('')}
                                            </div>
                                        </div>
                                    ` : '';
                                })()}
                                
                                ${(() => {
                                    let medications = doc.medications;
                                    if (typeof medications === 'string') {
                                        try { medications = JSON.parse(medications); } catch(e) { medications = []; }
                                    }
                                    return medications && Array.isArray(medications) && medications.length > 0 ? `
                                        <div>
                                            <h5 class="font-semibold text-purple-600">Medications:</h5>
                                            <div class="bg-purple-50 p-2 rounded text-sm">
                                                ${medications.map(med => `<div>• ${med}</div>`).join('')}
                                            </div>
                                        </div>
                                    ` : '';
                                })()}
                                
                                ${(() => {
                                    let diagnoses = doc.diagnoses;
                                    if (typeof diagnoses === 'string') {
                                        try { diagnoses = JSON.parse(diagnoses); } catch(e) { diagnoses = []; }
                                    }
                                    return diagnoses && Array.isArray(diagnoses) && diagnoses.length > 0 ? `
                                        <div>
                                            <h5 class="font-semibold text-red-600">Diagnoses:</h5>
                                            <div class="bg-red-50 p-2 rounded text-sm">
                                                ${diagnoses.map(diag => `<div>• ${diag}</div>`).join('')}
                                            </div>
                                        </div>
                                    ` : '';
                                })()}
                                ${doc.severity && doc.severity !== 'unknown' ? `
                                    <div>
                                        <h5 class="font-semibold text-orange-600">Severity:</h5>
                                        <div class="p-2 rounded text-sm">
                                            <span class="px-3 py-1 rounded text-sm font-medium ${
                                                doc.severity === 'critical' ? 'bg-red-100 text-red-800' :
                                                doc.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                                                doc.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-green-100 text-green-800'
                                            }">${doc.severity.toUpperCase()}</span>
                                        </div>
                                    </div>
                                ` : ''}
                                ${doc.clinical_urgency && doc.clinical_urgency !== 'routine' ? `
                                    <div>
                                        <h5 class="font-semibold text-purple-600">Clinical Urgency:</h5>
                                        <div class="p-2 rounded text-sm">
                                            <span class="px-3 py-1 rounded text-sm font-medium ${
                                                doc.clinical_urgency === 'emergency' ? 'bg-red-100 text-red-800' :
                                                doc.clinical_urgency === 'urgent' ? 'bg-orange-100 text-orange-800' :
                                                'bg-blue-100 text-blue-800'
                                            }">${doc.clinical_urgency.toUpperCase()}</span>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="flex justify-end">
                        <button onclick="this.closest('.fixed').remove()" 
                                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                            Close
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        // Delete document
        async function deleteDocument(docId) {
            if (!confirm('Are you sure you want to delete this document?')) return;
            
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch(`/api/documents/patients/${patientId}/documents/${docId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    showNotification('Document deleted successfully', 'success');
                    loadPatientDocuments();
                } else {
                    showNotification('Failed to delete document', 'error');
                }
            } catch (error) {
                console.error('Error deleting document:', error);
                showNotification('Error deleting document', 'error');
            }
        }

        // Report Generation Form Handler
        document.getElementById('reportGenerationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = checkAuth();
            if (!token) return;

            try {
                const timeRange = document.getElementById('timeRange').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                
                const reportData = {
                    patientId: patientId,
                    includeConsultations: document.getElementById('includeConsultations').checked,
                    includeTriages: document.getElementById('includeTriages').checked,
                    includeMedicalReports: document.getElementById('includeMedicalReports').checked,
                    includeVitalSigns: document.getElementById('includeVitalSigns').checked,
                    includeMedications: document.getElementById('includeMedications').checked
                };

                if (timeRange === 'custom') {
                    if (!startDate || !endDate) {
                        showNotification('Please select both start and end dates', 'error');
                        return;
                    }
                    reportData.startDate = startDate;
                    reportData.endDate = endDate;
                } else {
                    reportData.duration = timeRange;
                }

                // Show loading state
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating Report...';
                submitButton.disabled = true;

                let report;

                // Generate the basic report
                const reportResponse = await fetch('/api/consultations/generate-report', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(reportData)
                });

                if (!reportResponse.ok) {
                    const error = await reportResponse.json();
                    throw new Error(error.error || 'Failed to generate report');
                }

                const reportResult = await reportResponse.json();
                report = reportResult.report;

                // Reset button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;

                // Display the report
                displayReportPreview(report);

            } catch (error) {
                console.error('Error generating report:', error);
                showNotification(error.message || 'Failed to generate report', 'error');
                
                // Reset button on error
                const submitButton = this.querySelector('button[type="submit"]');
                submitButton.innerHTML = '<i class="fas fa-file-medical-alt mr-2"></i>Generate Report';
                submitButton.disabled = false;
            }
        });

        // Time Range Selection Handler
        document.getElementById('timeRange').addEventListener('change', function(e) {
            const customDateRange = document.getElementById('customDateRange');
            if (e.target.value === 'custom') {
                customDateRange.classList.remove('hidden');
            } else {
                customDateRange.classList.add('hidden');
            }
        });

        // Display Report Preview
        function displayReportPreview(report) {
            const modal = document.getElementById('reportPreviewModal');
            const content = document.getElementById('reportContent');
            
            // Format dates
            const formatDate = (date) => new Date(date).toLocaleDateString();
            
            // Generate simplified report HTML with only patient info
            let reportHtml = `
                <div class="space-y-6">
                    <!-- Patient Information -->
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Patient Information</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p><strong>Name:</strong> ${report.patient.name}</p>
                                <p><strong>Age:</strong> ${report.patient.age || 'Not specified'}</p>
                            </div>
                            <div>
                                <p><strong>Gender:</strong> ${report.patient.gender || 'Not specified'}</p>
                                <p><strong>Phone:</strong> ${report.patient.phone || 'Not specified'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Report Information -->
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Report Information</h2>
                        <p><strong>Generated:</strong> ${formatDate(report.generatedAt)}</p>
                        <p><strong>Period:</strong> ${formatDate(report.dateRange.start)} to ${formatDate(report.dateRange.end)}</p>
                    </div>
            `;

            // Add Clinical Analysis Section (Primary Analysis)
            if (report.clinicalAnalysis) {
                reportHtml += `
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-brain text-purple-600 mr-2"></i>
                            Clinical Analysis & Insights
                        </h2>
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <div class="prose max-w-none text-gray-800">
                                ${report.clinicalAnalysis.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                            </div>
                        </div>
                    </div>
                `;
            }

            // Add Risk Assessment Section
            if (report.riskAssessment) {
                const riskLevel = report.riskAssessment.overallRisk;
                const riskColor = riskLevel === 'High' ? 'red' : riskLevel === 'Moderate' ? 'yellow' : 'green';
                
                reportHtml += `
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-exclamation-triangle text-${riskColor}-600 mr-2"></i>
                            Risk Assessment
                            <span class="ml-2 px-3 py-1 bg-${riskColor}-100 text-${riskColor}-800 rounded-full text-sm font-semibold">
                                ${riskLevel} Risk
                            </span>
                        </h2>
                        <div class="bg-${riskColor}-50 p-4 rounded-lg border-l-4 border-${riskColor}-400">
                            <div class="mb-4">
                                <h3 class="font-semibold text-${riskColor}-800 mb-2">Risk Factors:</h3>
                                <ul class="list-disc list-inside space-y-1">
                                    ${report.riskAssessment.riskFactors.map(risk => 
                                        `<li class="text-gray-700">${risk}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-semibold text-${riskColor}-800 mb-2">Recommendations:</h3>
                                <ul class="list-disc list-inside space-y-1">
                                    ${report.riskAssessment.recommendations.map(rec => 
                                        `<li class="text-gray-700">${rec}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Add Vital Signs Analysis Section
            if (report.vitalSignsAnalysis) {
                reportHtml += `
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-chart-line text-green-600 mr-2"></i>
                            Vital Signs Analysis
                        </h2>
                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                            <p class="text-gray-800 mb-3"><strong>Summary:</strong> ${report.vitalSignsAnalysis.summary}</p>
                            ${report.vitalSignsAnalysis.trends.length > 0 ? `
                                <div class="mb-3">
                                    <h3 class="font-semibold text-green-800 mb-2">Trends:</h3>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${report.vitalSignsAnalysis.trends.map(trend => 
                                            `<li class="text-gray-700">${trend}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            ${report.vitalSignsAnalysis.recommendations.length > 0 ? `
                                <div>
                                    <h3 class="font-semibold text-green-800 mb-2">Recommendations:</h3>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${report.vitalSignsAnalysis.recommendations.map(rec => 
                                            `<li class="text-gray-700">${rec}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            // Add Medication Analysis Section
            if (report.medicationAnalysis) {
                reportHtml += `
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-pills text-blue-600 mr-2"></i>
                            Medication Analysis
                        </h2>
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <p class="text-gray-800 mb-3"><strong>Summary:</strong> ${report.medicationAnalysis.summary}</p>
                            ${report.medicationAnalysis.effectiveness.length > 0 ? `
                                <div class="mb-3">
                                    <h3 class="font-semibold text-blue-800 mb-2">Effectiveness Assessment:</h3>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${report.medicationAnalysis.effectiveness.map(eff => 
                                            `<li class="text-gray-700">${eff}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            ${report.medicationAnalysis.recommendations.length > 0 ? `
                                <div>
                                    <h3 class="font-semibold text-blue-800 mb-2">Recommendations:</h3>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${report.medicationAnalysis.recommendations.map(rec => 
                                            `<li class="text-gray-700">${rec}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }



            // Add Medication Intelligence Section (NEW - 4-part analysis)
            if (report.medicationIntelligence) {
                reportHtml += `
                    <div class="border-b pb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-pills text-blue-600 mr-2"></i>
                            Medication Intelligence Analysis
                        </h2>
                        
                        <!-- Drug Interaction Analysis -->
                        <div class="mb-6 bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                            <h3 class="font-semibold text-red-800 mb-2">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Drug Interaction Analysis
                            </h3>
                            <div class="prose max-w-none text-gray-800">
                                ${report.medicationIntelligence.drugInteractions.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                            </div>
                        </div>
                        
                        <!-- Medication Appropriateness -->
                        <div class="mb-6 bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <h3 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-check-circle mr-1"></i>
                                Medication Appropriateness
                            </h3>
                            <div class="prose max-w-none text-gray-800">
                                ${report.medicationIntelligence.appropriateness.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                            </div>
                        </div>
                        
                        <!-- Lab-Based Effectiveness -->
                        <div class="mb-6 bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                            <h3 class="font-semibold text-green-800 mb-2">
                                <i class="fas fa-chart-line mr-1"></i>
                                Lab-Based Effectiveness
                            </h3>
                            <div class="prose max-w-none text-gray-800">
                                ${report.medicationIntelligence.labEffectiveness.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                            </div>
                        </div>
                        
                        <!-- Comprehensive Summary -->
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <h3 class="font-semibold text-purple-800 mb-2">
                                <i class="fas fa-brain mr-1"></i>
                                Comprehensive Medication Summary
                            </h3>
                            <div class="prose max-w-none text-gray-800">
                                ${report.medicationIntelligence.comprehensiveSummary.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                            </div>
                        </div>
                    </div>
                `;
            }

            reportHtml += '</div>';
            content.innerHTML = reportHtml;
            modal.classList.remove('hidden');
        }

        // Close Report Preview Modal
        function closeReportPreviewModal() {
            const modal = document.getElementById('reportPreviewModal');
            modal.classList.add('hidden');
        }

        // Print Report
        function printReport() {
            const content = document.getElementById('reportContent').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Patient Report</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .no-print { display: none; }
                        h2, h3 { color: #333; }
                        .grid { display: block; }
                        .card { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                        .icon { display: none; }
                    </style>
                </head>
                <body>
                    <div>${content}</div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // ===== MEDICATION MANAGEMENT FUNCTIONS =====
        
        let selectedMedication = null;
        let currentMedicationId = null; // For editing
        
        // Load patient medications
        async function loadPatientMedications() {
            const token = checkAuth();
            if (!token) return;
            
            // Validate patient ID before making API call
            if (!patientId || patientId === 'null' || patientId === 'undefined') {
                console.log('loadPatientMedications: No valid patient ID, skipping');
                return;
            }
            
            try {
                const response = await fetch(`/api/medications/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.medications) {
                        displayPatientMedications(result.medications);
                    } else {
                        console.error('Invalid response format:', result);
                        displayPatientMedications([]);
                    }
                } else if (response.status === 401 || response.status === 403) {
                    console.error('Authentication failed - token expired or invalid');
                    localStorage.removeItem('doctorToken');
                    showNotification('Session expired. Please log in again.', 'error');
                    setTimeout(() => {
                        window.location.href = '/doctor-login.html';
                    }, 2000);
                } else {
                    console.error('Failed to load patient medications:', response.status);
                    displayPatientMedications([]);
                    showNotification('Failed to load medications. Please try refreshing the page.', 'error');
                }
            } catch (error) {
                console.error('Error loading patient medications:', error);
                displayPatientMedications([]);
                showNotification('Error loading medications. Please check your connection.', 'error');
            }
        }
        
        // Get medication status info for display
        function getMedicationStatusInfo(medication) {
            const status = medication.status || 'active';
            const durationType = medication.duration_type || 'until_discontinued';
            const isExpired = medication.is_expired || false;
            
            let iconColor = 'text-green-600';
            let badge = '';
            let durationClass = 'bg-gray-100 text-gray-800';
            let cardClass = 'bg-white border-gray-200';
            
            if (status === 'completed') {
                iconColor = 'text-blue-600';
                badge = '<span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Completed</span>';
                durationClass = 'bg-blue-100 text-blue-800';
            } else if (status === 'auto_expired' || isExpired) {
                iconColor = 'text-red-600';
                badge = '<span class="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Expired</span>';
                durationClass = 'bg-red-100 text-red-800';
                cardClass = 'bg-red-50 border-red-300';
            } else if (durationType === 'stat') {
                iconColor = 'text-purple-600';
                badge = '<span class="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">STAT</span>';
                durationClass = 'bg-purple-100 text-purple-800';
            } else if (durationType === 'prn') {
                iconColor = 'text-yellow-600';
                badge = '<span class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">PRN</span>';
                durationClass = 'bg-yellow-100 text-yellow-800';
            } else if (durationType === 'fixed') {
                durationClass = 'bg-blue-100 text-blue-800';
            }
            
            return {
                iconColor,
                badge,
                durationClass,
                cardClass
            };
        }

        // Display patient medications
        function displayPatientMedications(medications) {
            const medicationsList = document.getElementById('patientMedicationsList');
            
            // Update summary badge
            document.getElementById('medicationsSummary').textContent = `${medications.length} medication${medications.length !== 1 ? 's' : ''}`;
            
            if (medications.length === 0) {
                medicationsList.innerHTML = `
                    <div class="text-center py-6 text-gray-500">
                        <i class="fas fa-pills text-2xl mb-2 text-orange-300"></i>
                        <p class="text-sm">No medications recorded</p>
                        <p class="text-xs mt-1">Add medications to track patient's treatment plan</p>
                    </div>
                `;
                return;
            }
            
            medicationsList.innerHTML = medications.map(med => {
                const statusInfo = getMedicationStatusInfo(med);
                
                return `
                <div class="${statusInfo.cardClass} border rounded-md p-3 hover:shadow-sm transition-shadow">
                    <!-- Compact Header Row -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center flex-1 min-w-0">
                            <i class="fas fa-pills ${statusInfo.iconColor} mr-2 text-sm"></i>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-gray-800 text-sm truncate">${med.scientific_name}</h4>
                                ${med.trade_name ? `<span class="text-xs text-gray-500 truncate">(${med.trade_name})</span>` : ''}
                            </div>
                            ${statusInfo.badge}
                        </div>
                        <div class="flex items-center space-x-1 ml-2">
                            <button onclick="editMedication(${med.id})" 
                                    class="p-1 text-blue-600 hover:bg-blue-50 rounded text-xs transition-colors"
                                    title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="discontinueMedication(${med.id})" 
                                    class="p-1 text-red-600 hover:bg-red-50 rounded text-xs transition-colors"
                                    title="Stop">
                                <i class="fas fa-stop-circle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Compact Info Grid -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div>
                            <span class="text-gray-500">Dose:</span>
                            <span class="font-medium text-gray-800 ml-1">${med.dosage}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Freq:</span>
                            <span class="font-medium text-gray-800 ml-1">${med.frequency}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Route:</span>
                            <span class="font-medium text-gray-800 ml-1">${med.route || 'Oral'}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">For:</span>
                            <span class="inline-block bg-blue-100 text-blue-800 text-xs px-1 py-0.5 rounded ml-1">
                                ${med.medical_condition}
                            </span>
                        </div>
                    </div>
                    
                    <!-- Duration & Prescription Info -->
                    <div class="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
                        <div class="flex items-center space-x-2 text-xs">
                            <span class="inline-block ${statusInfo.durationClass} px-1.5 py-0.5 rounded text-xs">
                                ${med.duration_display || 'Ongoing'}
                            </span>
                            ${med.remaining_days !== null && med.remaining_days >= 0 ? `
                                <span class="text-gray-500">
                                    (${med.remaining_days}d left)
                                </span>
                            ` : ''}
                        </div>
                        <div class="text-xs text-gray-500">
                            ${new Date(med.prescribed_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            ${med.prescribed_by_name ? ` • ${med.prescribed_by_name}` : ''}
                        </div>
                    </div>
                    
                    ${med.notes ? `
                        <div class="mt-2 pt-2 border-t border-gray-100">
                            <span class="text-xs text-gray-500">Note:</span>
                            <span class="text-xs text-gray-700 ml-1">${med.notes}</span>
                        </div>
                    ` : ''}
                </div>
                `;
            }).join('');
        }
        
        // Open medication modal
        function openMedicationModal(editMode = false, medicationData = null) {
            const modal = document.getElementById('medicationModal');
            const title = document.getElementById('medicationModalTitle');
            
            if (editMode && medicationData) {
                title.textContent = 'Edit Medication';
                currentMedicationId = medicationData.id;
                populateMedicationForm(medicationData);
            } else {
                title.textContent = 'Add Medication';
                currentMedicationId = null;
                clearMedicationForm();
            }
            
            modal.classList.remove('hidden');
            loadCommonConditions();
            loadMedicationDurationPresets();
        }
        
        // Close medication modal
        function closeMedicationModal() {
            const modal = document.getElementById('medicationModal');
            modal.classList.add('hidden');
            clearMedicationForm();
            selectedMedication = null;
            currentMedicationId = null;
        }
        
        // Clear medication form
        function clearMedicationForm() {
            document.getElementById('medicationForm').reset();
            document.getElementById('selectedMedicationDisplay').classList.add('hidden');
            document.getElementById('medicationSearchResults').classList.add('hidden');
            document.getElementById('customFrequencyContainer').classList.add('hidden');
            document.getElementById('medicationFixedDurationOptions').classList.add('hidden');
            document.getElementById('medicationCalculatedEndDate').classList.add('hidden');
        }
        
        // Populate medication form for editing
        function populateMedicationForm(medication) {
            selectedMedication = {
                id: medication.medication_id,
                scientific_name: medication.scientific_name,
                trade_name: medication.trade_name,
                strength: medication.strength,
                pharmaceutical_form: medication.pharmaceutical_form
            };
            
            document.getElementById('selectedMedicationName').textContent = medication.scientific_name;
            document.getElementById('selectedMedicationDetails').textContent = 
                `${medication.trade_name || ''} ${medication.strength || ''}`.trim();
            document.getElementById('selectedMedicationDisplay').classList.remove('hidden');
            
            document.getElementById('medicationDosage').value = medication.dosage;
            document.getElementById('medicationFrequency').value = medication.frequency;
            document.getElementById('medicationRoute').value = medication.route || '';
            document.getElementById('customCondition').value = medication.medical_condition;
            document.getElementById('medicationNotes').value = medication.notes || '';
            
            // Populate duration fields
            document.getElementById('medicationDurationType').value = medication.duration_type || 'until_discontinued';
            document.getElementById('medicationDurationValue').value = medication.duration_value || '';
            document.getElementById('medicationDurationUnit').value = medication.duration_unit || 'days';
            
            // Trigger duration type change to show/hide relevant fields
            handleMedicationDurationTypeChange();
        }
        
        // Load common conditions
        async function loadCommonConditions() {
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch('/api/medications/conditions', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.conditions) {
                        const select = document.getElementById('commonConditions');
                        select.innerHTML = '<option value="">Select common condition</option>';
                        
                        result.conditions.forEach(condition => {
                            select.innerHTML += `<option value="${condition.condition_name}">${condition.condition_name}</option>`;
                        });
                    } else {
                        console.error('Invalid conditions response format:', result);
                    }
                }
            } catch (error) {
                console.error('Error loading common conditions:', error);
            }
        }

        // Duration Management Functions
        
        // Load duration presets
        async function loadMedicationDurationPresets() {
            try {
                const response = await fetch('/api/medications/duration-presets', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('doctorToken')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    populateMedicationDurationPresets(data.presets || []);
                } else {
                    console.error('Failed to load duration presets');
                }
            } catch (error) {
                console.error('Error loading duration presets:', error);
            }
        }

        // Populate duration presets dropdown
        function populateMedicationDurationPresets(presets) {
            const presetSelect = document.getElementById('medicationDurationPreset');
            
            // Clear existing options except the first one
            presetSelect.innerHTML = '<option value="">Select a preset or enter custom...</option>';
            
            // Group presets by category
            const grouped = {};
            presets.forEach(preset => {
                const category = preset.category || 'Other';
                if (!grouped[category]) grouped[category] = [];
                grouped[category].push(preset);
            });
            
            // Add options grouped by category
            Object.keys(grouped).forEach(category => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = category;
                
                grouped[category].forEach(preset => {
                    const option = document.createElement('option');
                    option.value = JSON.stringify({
                        duration_value: preset.duration_value,
                        duration_unit: preset.duration_unit
                    });
                    option.textContent = preset.name;
                    if (preset.is_common) {
                        option.textContent += ' ⭐';
                    }
                    optgroup.appendChild(option);
                });
                
                presetSelect.appendChild(optgroup);
            });
        }

        // Handle duration type change
        function handleMedicationDurationTypeChange() {
            const durationType = document.getElementById('medicationDurationType').value;
            const fixedOptions = document.getElementById('medicationFixedDurationOptions');
            const calculatedEndDate = document.getElementById('medicationCalculatedEndDate');
            const descriptionText = document.getElementById('medicationDurationDescriptionText');
            
            // Show/hide fixed duration options
            if (durationType === 'fixed') {
                fixedOptions.classList.remove('hidden');
            } else {
                fixedOptions.classList.add('hidden');
                calculatedEndDate.classList.add('hidden');
            }
            
            // Update description
            const descriptions = {
                'until_discontinued': 'Medication will continue until manually discontinued by a healthcare provider.',
                'fixed': 'Medication will automatically expire after the specified duration.',
                'stat': 'Single dose medication that will be marked as completed immediately.',
                'prn': 'As-needed medication available when required by the patient.'
            };
            
            descriptionText.textContent = descriptions[durationType] || 'Unknown duration type.';
        }

        // Apply duration preset
        function applyMedicationDurationPreset() {
            const presetSelect = document.getElementById('medicationDurationPreset');
            const selectedValue = presetSelect.value;
            
            if (selectedValue) {
                const preset = JSON.parse(selectedValue);
                document.getElementById('medicationDurationValue').value = preset.duration_value;
                document.getElementById('medicationDurationUnit').value = preset.duration_unit;
                calculateMedicationEndDate();
            }
        }

        // Calculate and display end date
        function calculateMedicationEndDate() {
            const durationType = document.getElementById('medicationDurationType').value;
            const durationValue = parseInt(document.getElementById('medicationDurationValue').value);
            const durationUnit = document.getElementById('medicationDurationUnit').value;
            
            if (durationType === 'fixed' && durationValue && durationUnit) {
                const startDate = new Date();
                const endDate = new Date(startDate.getTime()); // Use getTime() for proper copying
                
                switch (durationUnit) {
                    case 'days':
                        endDate.setDate(endDate.getDate() + durationValue);
                        break;
                    case 'weeks':
                        endDate.setDate(endDate.getDate() + (durationValue * 7));
                        break;
                    case 'months':
                        endDate.setMonth(endDate.getMonth() + durationValue);
                        break;
                }
                
                const endDateDisplay = document.getElementById('medicationEndDateDisplay');
                const calculatedEndDate = document.getElementById('medicationCalculatedEndDate');
                
                // Format date properly
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                endDateDisplay.textContent = endDate.toLocaleDateString('en-US', options);
                calculatedEndDate.classList.remove('hidden');
                
                console.log('Date calculation:', {
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString(),
                    durationValue,
                    durationUnit
                });
            } else {
                document.getElementById('medicationCalculatedEndDate').classList.add('hidden');
            }
        }
        
        // Search medications
        async function searchMedications(query) {
            if (query.length < 2) {
                document.getElementById('medicationSearchResults').classList.add('hidden');
                return;
            }
            
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch(`/api/medications/search?q=${encodeURIComponent(query)}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.medications) {
                        displayMedicationSearchResults(result.medications);
                    } else {
                        console.error('Invalid search response format:', result);
                        displayMedicationSearchResults([]);
                    }
                } else {
                    console.error('Failed to search medications');
                    displayMedicationSearchResults([]);
                }
            } catch (error) {
                console.error('Error searching medications:', error);
            }
        }
        
        // Display medication search results
        function displayMedicationSearchResults(medications) {
            const resultsDiv = document.getElementById('medicationSearchResults');
            
            if (medications.length === 0) {
                resultsDiv.innerHTML = '<div class="p-3 text-gray-500">No medications found</div>';
            } else {
                resultsDiv.innerHTML = medications.map(med => `
                    <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200" 
                         onclick="selectMedication(${JSON.stringify(med).replace(/"/g, '&quot;')})">
                        <div class="font-medium text-gray-800">${med.scientific_name}</div>
                        <div class="text-sm text-gray-600">
                            ${med.trade_name ? `${med.trade_name} • ` : ''}
                            ${med.strength ? `${med.strength} ${med.strength_unit || ''}` : ''}
                            ${med.pharmaceutical_form ? ` • ${med.pharmaceutical_form}` : ''}
                        </div>
                    </div>
                `).join('');
            }
            
            resultsDiv.classList.remove('hidden');
        }
        
        // Select medication from search results
        async function selectMedication(medication) {
            selectedMedication = medication;
            
            // Check for allergies when selecting medication
            const patientAllergies = currentPatient?.allergies || [];
            const allergyCheck = await checkMedicationAllergies(medication, patientAllergies);
            
            document.getElementById('selectedMedicationName').textContent = medication.scientific_name;
            document.getElementById('selectedMedicationDetails').textContent = 
                `${medication.trade_name || ''} ${medication.strength || ''}`.trim();
            
            // Add allergy warning if detected
            const selectedMedicationDisplay = document.getElementById('selectedMedicationDisplay');
            if (allergyCheck.hasAllergy) {
                // Add allergy warning to the selected medication display
                const existingWarning = selectedMedicationDisplay.querySelector('.allergy-warning');
                if (existingWarning) existingWarning.remove();
                
                const warningDiv = document.createElement('div');
                warningDiv.className = 'allergy-warning mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs';
                warningDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-red-600 mr-1"></i>
                    <span class="text-red-700 font-medium">Allergy Alert:</span>
                    <span class="text-red-600"> ${allergyCheck.matchedAllergies.join(', ')}</span>
                `;
                selectedMedicationDisplay.appendChild(warningDiv);
            } else {
                // Remove any existing warning
                const existingWarning = selectedMedicationDisplay.querySelector('.allergy-warning');
                if (existingWarning) existingWarning.remove();
            }
            
            selectedMedicationDisplay.classList.remove('hidden');
            document.getElementById('medicationSearchResults').classList.add('hidden');
            document.getElementById('medicationSearch').value = '';
        }
        
        // Clear selected medication
        function clearSelectedMedication() {
            selectedMedication = null;
            document.getElementById('selectedMedicationDisplay').classList.add('hidden');
        }
        
        // Edit medication
        async function editMedication(medicationId) {
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch(`/api/medications/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.medications) {
                        const medication = result.medications.find(med => med.id === medicationId);
                        if (medication) {
                            openMedicationModal(true, medication);
                        }
                    } else {
                        console.error('Invalid response format:', result);
                    }
                } else if (response.status === 401 || response.status === 403) {
                    console.error('Authentication failed - token expired or invalid');
                    localStorage.removeItem('doctorToken');
                    showNotification('Session expired. Please log in again.', 'error');
                    setTimeout(() => {
                        window.location.href = '/doctor-login.html';
                    }, 2000);
                } else {
                    console.error('Failed to load medication for editing:', response.status);
                    showNotification('Failed to load medication. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error loading medication for editing:', error);
                showNotification('Error loading medication. Please check your connection.', 'error');
            }
        }
        
        // Discontinue medication
        async function discontinueMedication(medicationId) {
            const reason = prompt('Please provide a reason for discontinuing this medication:');
            if (!reason) return;
            
            const token = checkAuth();
            if (!token) return;
            
            try {
                const response = await fetch(`/api/medications/patient-medication/${medicationId}`, {
                    method: 'PATCH',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: 'discontinued',
                        discontinue_reason: reason
                    })
                });
                
                if (response.ok) {
                    showNotification('Medication discontinued successfully', 'success');
                    loadPatientMedications();
                } else if (response.status === 401 || response.status === 403) {
                    console.error('Authentication failed - token expired or invalid');
                    localStorage.removeItem('doctorToken');
                    showNotification('Session expired. Please log in again.', 'error');
                    setTimeout(() => {
                        window.location.href = '/doctor-login.html';
                    }, 2000);
                } else {
                    try {
                        const error = await response.json();
                        showNotification(error.error || 'Failed to discontinue medication', 'error');
                    } catch (parseError) {
                        console.error('Failed to parse error response:', parseError);
                        showNotification('Failed to discontinue medication. Please try again.', 'error');
                    }
                }
            } catch (error) {
                console.error('Error discontinuing medication:', error);
                showNotification('Error discontinuing medication. Please check your connection.', 'error');
            }
        }
        
        // Event Listeners
        document.getElementById('addMedicationBtn').addEventListener('click', () => {
            openMedicationModal();
        });
        
        // Drug Interaction Analysis Button
        document.getElementById('analyzeDrugInteractionsBtn').addEventListener('click', () => {
            analyzeDrugInteractions();
        });
        
        // Medication search input
        let searchTimeout;
        document.getElementById('medicationSearch').addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchMedications(e.target.value);
            }, 300);
        });
        
        // Frequency selection handler
        document.getElementById('medicationFrequency').addEventListener('change', (e) => {
            const customContainer = document.getElementById('customFrequencyContainer');
            if (e.target.value === 'Other') {
                customContainer.classList.remove('hidden');
            } else {
                customContainer.classList.add('hidden');
            }
        });
        
        // Common conditions selection handler
        document.getElementById('commonConditions').addEventListener('change', (e) => {
            if (e.target.value) {
                document.getElementById('customCondition').value = e.target.value;
            }
        });

        // Duration calculation event listeners
        document.getElementById('medicationDurationValue').addEventListener('input', calculateMedicationEndDate);
        document.getElementById('medicationDurationUnit').addEventListener('change', calculateMedicationEndDate);
        
        // Check for medication allergies
        async function checkMedicationAllergies(medication, patientAllergies) {
            if (!patientAllergies || patientAllergies.length === 0) {
                return { hasAllergy: false, matchedAllergies: [] };
            }
            
            const medicationNames = [
                medication.scientific_name?.toLowerCase(),
                medication.trade_name?.toLowerCase()
            ].filter(name => name);
            
            const matchedAllergies = [];
            
            for (const allergy of patientAllergies) {
                const allergyLower = allergy.toLowerCase().trim();
                
                // Check for exact matches or partial matches
                for (const medName of medicationNames) {
                    if (medName.includes(allergyLower) || allergyLower.includes(medName)) {
                        matchedAllergies.push(allergy);
                        break;
                    }
                }
                
                // Check for common drug class allergies
                const drugClassMatches = {
                    'penicillin': ['amoxicillin', 'ampicillin', 'penicillin', 'benzylpenicillin'],
                    'sulfa': ['sulfamethoxazole', 'sulfadiazine', 'sulfasalazine', 'trimethoprim'],
                    'nsaid': ['ibuprofen', 'diclofenac', 'naproxen', 'aspirin', 'celecoxib'],
                    'cephalosporin': ['cephalexin', 'cefuroxime', 'ceftriaxone', 'cefixime'],
                    'quinolone': ['ciprofloxacin', 'levofloxacin', 'norfloxacin', 'ofloxacin']
                };
                
                for (const [allergyClass, medications] of Object.entries(drugClassMatches)) {
                    if (allergyLower.includes(allergyClass)) {
                        for (const medName of medicationNames) {
                            if (medications.some(med => medName.includes(med))) {
                                matchedAllergies.push(`${allergy} (drug class match)`);
                                break;
                            }
                        }
                    }
                }
            }
            
            return {
                hasAllergy: matchedAllergies.length > 0,
                matchedAllergies: [...new Set(matchedAllergies)]
            };
        }
        
        // Show allergy warning modal
        function showAllergyWarningModal(medication, matchedAllergies, callback) {
            const modalHtml = `
                <div id="allergyWarningModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <!-- Warning Header -->
                            <div class="flex items-center mb-4">
                                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                                </div>
                                <div class="ml-4 text-left">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                                        ⚠️ ALLERGY ALERT
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        Potential allergic reaction risk detected
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Warning Content -->
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                                <h4 class="font-semibold text-red-800 mb-2">
                                    <i class="fas fa-pills mr-2"></i>Medication:
                                </h4>
                                <p class="text-red-700 mb-3">
                                    <strong>${medication.scientific_name}</strong>
                                    ${medication.trade_name ? `(${medication.trade_name})` : ''}
                                </p>
                                
                                <h4 class="font-semibold text-red-800 mb-2">
                                    <i class="fas fa-exclamation-circle mr-2"></i>Matched Allergies:
                                </h4>
                                <ul class="text-red-700 space-y-1">
                                    ${matchedAllergies.map(allergy => `
                                        <li class="flex items-center">
                                            <i class="fas fa-dot-circle mr-2 text-xs"></i>
                                            ${allergy}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            
                            <!-- Warning Message -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                <p class="text-yellow-800 text-sm">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>Clinical Warning:</strong> This medication may cause an allergic reaction. 
                                    Please verify patient allergies and consider alternative medications if appropriate.
                                </p>
                            </div>
                            
                            <!-- Confirmation Input -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    To proceed with this medication despite the allergy risk, type "yes" below:
                                </label>
                                <input 
                                    type="text" 
                                    id="allergyConfirmationInput"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                    placeholder="Type 'yes' to confirm..."
                                    autocomplete="off"
                                >
                                <p class="text-xs text-gray-500 mt-1">
                                    This confirms you have reviewed the allergy information and choose to proceed.
                                </p>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-3 pt-4">
                                <button 
                                    id="confirmAllergyProceed" 
                                    class="flex-1 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled
                                >
                                    <i class="fas fa-check mr-2"></i>
                                    Proceed Despite Allergy Risk
                                </button>
                                <button 
                                    id="cancelAllergyProceed" 
                                    class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                >
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel & Choose Different Medication
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            const modal = document.getElementById('allergyWarningModal');
            const confirmInput = document.getElementById('allergyConfirmationInput');
            const confirmBtn = document.getElementById('confirmAllergyProceed');
            const cancelBtn = document.getElementById('cancelAllergyProceed');
            
            // Enable/disable confirm button based on input
            confirmInput.addEventListener('input', (e) => {
                const isValid = e.target.value.toLowerCase().trim() === 'yes';
                confirmBtn.disabled = !isValid;
                confirmBtn.classList.toggle('opacity-50', !isValid);
                confirmBtn.classList.toggle('cursor-not-allowed', !isValid);
            });
            
            // Handle confirm
            confirmBtn.addEventListener('click', () => {
                if (confirmInput.value.toLowerCase().trim() === 'yes') {
                    modal.remove();
                    callback(true);
                }
            });
            
            // Handle cancel
            cancelBtn.addEventListener('click', () => {
                modal.remove();
                callback(false);
            });
            
            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                    callback(false);
                }
            });
            
            // Focus on input
            setTimeout(() => confirmInput.focus(), 100);
        }

        // Medication form submission
        document.getElementById('medicationForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!selectedMedication) {
                showNotification('Please select a medication', 'error');
                return;
            }
            
            const token = checkAuth();
            if (!token) return;
            
            const formData = {
                medication_id: selectedMedication.id,
                dosage: document.getElementById('medicationDosage').value,
                frequency: document.getElementById('medicationFrequency').value === 'Other' ? 
                    document.getElementById('customFrequency').value : 
                    document.getElementById('medicationFrequency').value,
                route: document.getElementById('medicationRoute').value,
                medical_condition: document.getElementById('customCondition').value || 
                    document.getElementById('commonConditions').value,
                notes: document.getElementById('medicationNotes').value,
                duration_type: document.getElementById('medicationDurationType').value,
                duration_value: document.getElementById('medicationDurationValue').value || null,
                duration_unit: document.getElementById('medicationDurationUnit').value || null
            };
            
            if (!formData.medical_condition) {
                showNotification('Please specify a medical condition', 'error');
                return;
            }
            
            // Check for allergies before proceeding
            const patientAllergies = currentPatient?.allergies || [];
            const allergyCheck = await checkMedicationAllergies(selectedMedication, patientAllergies);
            
            if (allergyCheck.hasAllergy) {
                // Show allergy warning and wait for user confirmation
                showAllergyWarningModal(selectedMedication, allergyCheck.matchedAllergies, (proceed) => {
                    if (proceed) {
                        // User confirmed to proceed despite allergy
                        formData.allergy_override = true;
                        formData.allergy_warning = `Allergy override: ${allergyCheck.matchedAllergies.join(', ')}`;
                        submitMedicationForm(formData, token);
                    }
                    // If not proceeding, do nothing (user can select different medication)
                });
                return; // Don't proceed with normal submission
            }
            
            // No allergies detected, proceed normally
            submitMedicationForm(formData, token);
        });
        
        // Separate function to handle the actual form submission
        async function submitMedicationForm(formData, token) {
            try {
                let response;
                if (currentMedicationId) {
                    // Update existing medication
                    response = await fetch(`/api/medications/patient-medication/${currentMedicationId}`, {
                        method: 'PATCH',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // Add new medication
                    response = await fetch(`/api/medications/patient/${patientId}/medications`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                }
                
                if (response.ok) {
                    showNotification(
                        currentMedicationId ? 'Medication updated successfully' : 'Medication added successfully', 
                        'success'
                    );
                    closeMedicationModal();
                    loadPatientMedications();
                } else if (response.status === 401 || response.status === 403) {
                    console.error('Authentication failed - token expired or invalid');
                    localStorage.removeItem('doctorToken');
                    showNotification('Session expired. Please log in again.', 'error');
                    setTimeout(() => {
                        window.location.href = '/doctor-login.html';
                    }, 2000);
                } else {
                    try {
                        const error = await response.json();
                        showNotification(error.error || 'Failed to save medication', 'error');
                    } catch (parseError) {
                        console.error('Failed to parse error response:', parseError);
                        showNotification('Failed to save medication. Please try again.', 'error');
                    }
                }
            } catch (error) {
                console.error('Error saving medication:', error);
                showNotification('Error saving medication. Please check your connection.', 'error');
            }
        }
        
        // Hide search results when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#medicationSearch') && !e.target.closest('#medicationSearchResults')) {
                document.getElementById('medicationSearchResults').classList.add('hidden');
            }
        });

        // Drug Interaction Analysis Functions
        async function analyzeDrugInteractions() {
            const token = checkAuth();
            if (!token || !currentPatientId) {
                showNotification('Authentication required or patient not found', 'error');
                return;
            }
            
            try {
                // Show loading state
                showDrugInteractionModal();
                showDrugInteractionStatus('Analyzing medications for drug-drug interactions...', 'loading');
                
                // Get current medications
                const medicationsResponse = await fetch(`/api/medications/patient/${currentPatientId}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (!medicationsResponse.ok) {
                    throw new Error('Failed to fetch patient medications');
                }
                
                const medicationsData = await medicationsResponse.json();
                const medications = medicationsData.medications || [];
                
                if (medications.length < 2) {
                    showDrugInteractionStatus('Patient has fewer than 2 medications. At least 2 medications are required for interaction analysis.', 'info');
                    return;
                }
                
                // Call API for drug interaction analysis
                const analysisResponse = await fetch(`/api/medications/patient/${currentPatientId}/analyze-interactions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!analysisResponse.ok) {
                    throw new Error('Failed to analyze drug interactions');
                }
                
                const analysisData = await analysisResponse.json();
                
                if (analysisData.success) {
                    showDrugInteractionResults(analysisData.analysis, medications);
                } else {
                    throw new Error(analysisData.error || 'Analysis failed');
                }
                
            } catch (error) {
                console.error('Error analyzing drug interactions:', error);
                showDrugInteractionStatus(`Error: ${error.message}`, 'error');
            }
        }
        
        function showDrugInteractionModal() {
            document.getElementById('drugInteractionModal').classList.remove('hidden');
        }
        
        function closeDrugInteractionModal() {
            document.getElementById('drugInteractionModal').classList.add('hidden');
            // Clear previous results
            document.getElementById('drugInteractionStatus').innerHTML = '';
            document.getElementById('drugInteractionResults').innerHTML = '';
        }
        
        function showDrugInteractionStatus(message, type) {
            const statusDiv = document.getElementById('drugInteractionStatus');
            let bgColor, textColor, icon;
            
            switch (type) {
                case 'loading':
                    bgColor = 'bg-blue-50 border-blue-200';
                    textColor = 'text-blue-800';
                    icon = 'fas fa-spinner fa-spin text-blue-600';
                    break;
                case 'error':
                    bgColor = 'bg-red-50 border-red-200';
                    textColor = 'text-red-800';
                    icon = 'fas fa-exclamation-circle text-red-600';
                    break;
                case 'info':
                    bgColor = 'bg-yellow-50 border-yellow-200';
                    textColor = 'text-yellow-800';
                    icon = 'fas fa-info-circle text-yellow-600';
                    break;
                default:
                    bgColor = 'bg-gray-50 border-gray-200';
                    textColor = 'text-gray-800';
                    icon = 'fas fa-info-circle text-gray-600';
            }
            
            statusDiv.innerHTML = `
                <div class="border rounded-lg p-4 ${bgColor}">
                    <div class="flex items-center">
                        <i class="${icon} mr-3"></i>
                        <span class="${textColor}">${message}</span>
                    </div>
                </div>
            `;
        }
        
        function showDrugInteractionResults(analysis, medications) {
            const statusDiv = document.getElementById('drugInteractionStatus');
            const resultsDiv = document.getElementById('drugInteractionResults');
            
            // Clear loading status
            statusDiv.innerHTML = '';
            
            // Show medication list
            const medicationListHtml = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-pills mr-2"></i>
                        Analyzed Medications (${medications.length} total):
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        ${medications.map(med => `
                            <div class="bg-white p-2 rounded border text-sm">
                                <div class="font-medium text-gray-900">${med.scientific_name}</div>
                                <div class="text-gray-600">${med.trade_name || 'No trade name'}</div>
                                <div class="text-xs text-gray-500">${med.dosage} - ${med.frequency}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            // Format and display analysis results
            const formattedAnalysis = analysis
                .replace(/\*\*(.*?)\*\*/g, '<strong class="text-gray-900">$1</strong>')
                .replace(/## (.*?)$/gm, '<h3 class="text-lg font-bold text-red-700 mt-4 mb-2 border-b border-red-200 pb-1">$1</h3>')
                .replace(/### (.*?)$/gm, '<h4 class="text-md font-semibold text-red-600 mt-3 mb-2">$1</h4>')
                .replace(/- (.*?)$/gm, '<li class="ml-4 mb-1">$1</li>')
                .replace(/\n\n/g, '</p><p class="mb-3">')
                .replace(/\n/g, '<br>');
            
            const analysisHtml = `
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 mb-3">
                        <i class="fas fa-brain mr-2 text-purple-600"></i>
                        AI Analysis Results (Gemini 2.0-flash):
                    </h4>
                    <div class="prose max-w-none text-sm leading-relaxed">
                        <p class="mb-3">${formattedAnalysis}</p>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-3 mt-0.5"></i>
                        <div class="text-yellow-800 text-sm">
                            <p class="font-semibold mb-1">Important Disclaimer:</p>
                            <p>This AI analysis is for informational purposes only and should not replace professional clinical judgment. Always consult current drug interaction databases and consider individual patient factors when making prescribing decisions.</p>
                        </div>
                    </div>
                </div>
            `;
            
            resultsDiv.innerHTML = medicationListHtml + analysisHtml;
        }

        // Collapsible sections functionality
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + 'Content');
            const chevron = document.getElementById(sectionId + 'Chevron');
            
            if (content.classList.contains('collapsed')) {
                // Expand
                content.classList.remove('collapsed');
                content.classList.add('expanded');
                chevron.classList.add('rotated');
            } else {
                // Collapse
                content.classList.remove('expanded');
                content.classList.add('collapsed');
                chevron.classList.remove('rotated');
            }
        }

        // Update summary badges when data loads
        function updateSectionSummaries() {
            // Update patient summary
            const patientId = document.getElementById('patientId').textContent;
            const patientAge = document.getElementById('patientAge').textContent;
            const patientGender = document.getElementById('patientGender').textContent;
            document.getElementById('patientSummary').textContent = `ID: ${patientId} | Age: ${patientAge} | Gender: ${patientGender}`;
            
            // Update other summaries as data loads
            // These will be updated by the existing data loading functions
        }

        // Make functions globally available
        window.closeMedicationModal = closeMedicationModal;
        window.clearSelectedMedication = clearSelectedMedication;
        window.selectMedication = selectMedication;
        window.editMedication = editMedication;
        window.discontinueMedication = discontinueMedication;
        window.closeReportPreviewModal = closeReportPreviewModal;
        window.printReport = printReport;
        window.closeDrugInteractionModal = closeDrugInteractionModal;
        window.toggleSection = toggleSection;
        window.showCreditsModal = showCreditsModal;
        window.hideCreditsModal = hideCreditsModal;
        window.submitCreditsRequest = submitCreditsRequest;
        window.showCreditsErrorModal = showCreditsErrorModal;
        window.closeCreditsErrorModal = closeCreditsErrorModal;

        // ===== ENHANCED UI FUNCTIONS =====

        // Tab Management
        function switchTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Add active class to selected tab and content
            document.getElementById(tabName + 'Tab').classList.add('active');
            const content = document.getElementById(tabName + 'Content');
            content.classList.remove('hidden');
            content.classList.add('active');

            // Load content based on tab
            switch(tabName) {
                case 'clinical':
                    loadClinicalData();
                    break;
                case 'documents':
                    loadDocumentsData();
                    break;
                case 'reports':
                    loadReportsData();
                    break;
            }
        }

        // Quick Actions Menu
        function showQuickActionsMenu() {
            const menu = document.getElementById('quickActionsMenu');
            menu.classList.toggle('hidden');
            
            // Close menu when clicking outside
            if (!menu.classList.contains('hidden')) {
                document.addEventListener('click', function closeMenu(e) {
                    if (!menu.contains(e.target) && !e.target.closest('.fab')) {
                        menu.classList.add('hidden');
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }
        }

        // Enhanced Patient Data Loading
        function updatePatientHeader(patient) {
            // Update header elements
            document.getElementById('patientHeaderName').textContent = patient.name || 'Unknown Patient';
            document.getElementById('patientHeaderId').textContent = patient.id || '-';
            document.getElementById('patientHeaderAge').textContent = patient.age || '-';
            document.getElementById('patientHeaderGender').textContent = patient.gender || '-';
            document.getElementById('breadcrumbPatientName').textContent = patient.name || 'Patient Details';

            // Update avatar with patient initials
            const avatar = document.getElementById('patientAvatar');
            if (patient.name) {
                const initials = patient.name.split(' ').map(n => n[0]).join('').substring(0, 2);
                avatar.innerHTML = initials;
            }

            // Update priority badge
            const priorityBadge = document.getElementById('patientPriorityBadge');
            const priority = patient.priority || 'standard';
            priorityBadge.className = `priority-${priority} px-3 py-1 rounded-full text-sm font-medium`;
            priorityBadge.innerHTML = `<i class="fas fa-flag mr-1"></i>${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority`;
        }

        // Enhanced Stats Updates
        function updateStatsCards(stats) {
            document.getElementById('totalConsultations').textContent = stats.consultations || 0;
            document.getElementById('totalTriages').textContent = stats.triages || 0;
            document.getElementById('totalDocuments').textContent = stats.documents || 0;
            document.getElementById('totalMedications').textContent = stats.medications || 0;
        }

        // Enhanced Allergies Display
        function displayAllergies(allergies) {
            const container = document.getElementById('allergiesList');
            if (!allergies || allergies.length === 0) {
                container.innerHTML = '<span class="text-sm text-gray-500 italic">No known allergies</span>';
                return;
            }

            container.innerHTML = allergies.map(allergy => `
                <span class="allergy-tag">
                    <i class="fas fa-exclamation-triangle text-xs"></i>
                    ${allergy.name}
                    ${allergy.severity ? `<span class="text-xs opacity-75">(${allergy.severity})</span>` : ''}
                </span>
            `).join('');
        }

        // Enhanced Medications Display
        function displayMedications(medications) {
            const container = document.getElementById('medicationsList');
            if (!medications || medications.length === 0) {
                container.innerHTML = '<span class="text-sm text-gray-500 italic">No medications recorded</span>';
                return;
            }

            container.innerHTML = medications.map(med => `
                <div class="medication-tag flex items-center justify-between">
                    <div>
                        <div class="font-medium">${med.name}</div>
                        <div class="text-xs opacity-75">${med.dosage} - ${med.frequency}</div>
                    </div>
                    ${med.duration ? `<span class="text-xs bg-blue-100 px-2 py-1 rounded">${med.duration}</span>` : ''}
                </div>
            `).join('');
        }

        // Load Clinical Data
        function loadClinicalData() {
            // This will be called when switching to clinical tab
            console.log('Loading clinical data...');
        }

        // Load Documents Data
        function loadDocumentsData() {
            // This will be called when switching to documents tab
            console.log('Loading documents data...');
        }

        // Load Reports Data
        function loadReportsData() {
            // This will be called when switching to reports tab
            console.log('Loading reports data...');
        }

        // Enhanced Mobile Responsiveness
        function initMobileEnhancements() {
            // Bottom navigation for mobile
            if (window.innerWidth <= 768) {
                document.body.classList.add('mobile-view');
            }

            window.addEventListener('resize', () => {
                if (window.innerWidth <= 768) {
                    document.body.classList.add('mobile-view');
                } else {
                    document.body.classList.remove('mobile-view');
                }
            });
        }

        // Override existing loadPatientData function to use enhanced UI
        const originalLoadPatientData = window.loadPatientData;
        window.loadPatientData = async function(patientId) {
            try {
                const result = await originalLoadPatientData(patientId);
                
                // Update enhanced UI elements
                if (window.currentPatient) {
                    updatePatientHeader(window.currentPatient);
                    updateStatsCards({
                        consultations: window.currentPatient.totalConsultations || 0,
                        triages: window.currentPatient.totalTriages || 0,
                        documents: window.currentPatient.totalDocuments || 0,
                        medications: window.currentPatient.totalMedications || 0
                    });
                    displayAllergies(window.currentPatient.allergies);
                    displayMedications(window.currentPatient.medications);
                }
                
                return result;
            } catch (error) {
                console.error('Error in enhanced loadPatientData:', error);
            }
        };

        // Initialize enhanced UI
        document.addEventListener('DOMContentLoaded', function() {
            initMobileEnhancements();
        });

        // Export enhanced functions to global scope
        window.switchTab = switchTab;
        window.showQuickActionsMenu = showQuickActionsMenu;
        window.updatePatientHeader = updatePatientHeader;
        window.updateStatsCards = updateStatsCards;
        window.displayAllergies = displayAllergies;
        window.displayMedications = displayMedications;
    </script>

    <!-- Credits Request Modal -->
    <div id="creditsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Request Additional Credits</h3>
                <button onclick="hideCreditsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="creditsForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Credits Requested</label>
                    <select id="creditsAmount" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="2500">2,500 Credits (50 transcriptions)</option>
                        <option value="5000">5,000 Credits (100 transcriptions)</option>
                        <option value="10000">10,000 Credits (200 transcriptions)</option>
                        <option value="25000">25,000 Credits (500 transcriptions)</option>
                        <option value="custom">Custom Amount</option>
                    </select>
                </div>
                <div id="customCreditsDiv" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Custom Credits Amount</label>
                    <input type="number" id="customCredits" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Request</label>
                    <textarea id="creditsReason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Please explain why you need additional credits..."></textarea>
                </div>
                <div class="flex space-x-3">
                    <button type="button" onclick="hideCreditsModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Credits Error Modal -->
    <div id="creditsErrorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Insufficient Credits</h3>
                <p class="text-gray-600 mb-4">
                    You need at least 50 credits to perform an audio analysis. 
                    Each transcription costs 50 credits.
                </p>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <div class="flex items-center justify-center space-x-2">
                        <i class="fas fa-coins text-blue-600"></i>
                        <span class="text-sm font-medium text-blue-800">Current Credits:</span>
                        <span id="currentCreditsDisplay" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">--</span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button onclick="closeCreditsErrorModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        Close
                    </button>
                    <button onclick="showCreditsModal()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>Request Credits
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>