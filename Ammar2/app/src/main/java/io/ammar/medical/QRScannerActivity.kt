package io.ammar.medical
import io.ammar.medical.R
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@OptIn(androidx.camera.core.ExperimentalGetImage::class)
class QRScannerActivity : ComponentActivity() {
    private val TAG = "QRScannerActivity"
    private lateinit var cameraExecutor: java.util.concurrent.ExecutorService
    private var shouldScan = true

    // Create options for barcode scanner
    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
        .build()

    // Get barcode scanner
    private val scanner = BarcodeScanning.getClient(options)

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // Permission granted, start the scanner
            setContent {
                MaterialTheme {
                    QRScannerScreen(scanner)
                }
            }
        } else {
            // Show error message and return to previous screen
            Toast.makeText(
                this,
                "Camera permission is required to scan QR codes",
                Toast.LENGTH_LONG
            ).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create camera executor
        cameraExecutor = Executors.newSingleThreadExecutor()
        
        // Check for camera permission
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            // Permission already granted, start the scanner
            setContent {
                MaterialTheme {
                    QRScannerScreen(scanner)
                }
            }
        } else {
            // Request permission
            requestPermissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // Shut down camera executor
        cameraExecutor.shutdown()
        try {
            // Wait for tasks to complete before shutting down
            if (!cameraExecutor.awaitTermination(1000, TimeUnit.MILLISECONDS)) {
                cameraExecutor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            cameraExecutor.shutdownNow()
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun QRScannerScreen(scanner: BarcodeScanner) {
        val context = LocalContext.current
        val lifecycleOwner = LocalLifecycleOwner.current
        val scope = rememberCoroutineScope()
        
        var syncData by remember { mutableStateOf<String?>(null) }
        var isSyncing by remember { mutableStateOf(false) }
        var syncError by remember { mutableStateOf<String?>(null) }
        var syncSuccess by remember { mutableStateOf(false) }
        var userId by remember { mutableStateOf<String?>(null) }
        
        // Get user ID
        LaunchedEffect(Unit) {
            val auth = FirebaseAuth.getInstance()
            userId = auth.currentUser?.uid
            
            if (userId == null) {
                syncError = "You must be signed in to use sync"
            } else {
                // Get user sync info to display
                syncData = getUserSyncInfo()
            }
        }
        
        Box(modifier = Modifier.fillMaxSize()) {
            // Camera preview
            if (!syncSuccess && syncError == null) {
                AndroidView(
                    factory = { context ->
                        val previewView = PreviewView(context)
                        val preview = Preview.Builder().build()
                        val selector = CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                            .build()
                        
                        preview.setSurfaceProvider(previewView.surfaceProvider)
                        
                        val imageAnalysis = ImageAnalysis.Builder()
                            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                            .build()
                        
                        imageAnalysis.setAnalyzer(cameraExecutor) { imageProxy ->
                            processImageProxy(imageProxy, scanner) { qrCode ->
                                if (shouldScan && !isSyncing) {
                                    shouldScan = false // Prevent multiple scans
                                    isSyncing = true
                                    
                                    // Process the QR code data
                                    scope.launch {
                                        try {
                                            // Parse the QR code data (format: PC_UID:timestamp)
                                            val parts = qrCode.split(":")
                                            if (parts.size >= 2 && parts[0].startsWith("PC_")) {
                                                val pcUid = parts[0]
                                                val timestamp = parts[1].toLongOrNull() ?: 0
                                                
                                                // If the QR code is older than 5 minutes, reject it
                                                val currentTime = System.currentTimeMillis()
                                                if (currentTime - timestamp > 5 * 60 * 1000) {
                                                    syncError = "QR code has expired. Please generate a new one."
                                                    isSyncing = false
                                                    return@launch
                                                }
                                                
                                                // Authorize the PC to access the user's data
                                                val result = authorizePC(pcUid, userId!!)
                                                if (result) {
                                                    syncSuccess = true
                                                } else {
                                                    syncError = "Failed to authorize PC. Please try again."
                                                }
                                            } else {
                                                syncError = "Invalid QR code format. Please try again."
                                            }
                                        } catch (e: Exception) {
                                            Log.e(TAG, "Error processing QR code", e)
                                            syncError = "Error: ${e.message}"
                                        } finally {
                                            isSyncing = false
                                        }
                                    }
                                }
                            }
                        }
                        
                        try {
                            val cameraProvider = ProcessCameraProvider.getInstance(context).get()
                            cameraProvider.unbindAll()
                            cameraProvider.bindToLifecycle(
                                lifecycleOwner,
                                selector,
                                preview,
                                imageAnalysis
                            )
                        } catch (e: Exception) {
                            Log.e(TAG, "Camera binding failed", e)
                        }
                        
                        previewView
                    },
                    modifier = Modifier.fillMaxSize()
                )
                
                // QR code scanning guide
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .size(250.dp)
                            .border(
                                width = 2.dp,
                                color = Color.White,
                                shape = RoundedCornerShape(16.dp)
                            )
                    )
                }
            }
            
            // Top bar
            Column(modifier = Modifier.fillMaxSize()) {
                TopAppBar(
                    title = { Text(stringResource(R.string.qr_scanner)) },
                    navigationIcon = {
                        IconButton(onClick = { finish() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack, 
                                contentDescription = stringResource(R.string.back),
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.9f),
                        titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // User sync info
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.9f))
                        .padding(16.dp)
                ) {
                    when {
                        isSyncing -> {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                CircularProgressIndicator()
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "Syncing with PC...",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        }
                        syncSuccess -> {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "Success",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(48.dp)
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "Sync Successful!",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = "Your PC is now authorized to access your data",
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(onClick = { finish() }) {
                                    Text("Done")
                                }
                            }
                        }
                        syncError != null -> {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = "Error",
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(48.dp)
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "Sync Error",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.error
                                )
                                Text(
                                    text = syncError!!,
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = TextAlign.Center
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Button(onClick = {
                                        syncError = null
                                        shouldScan = true
                                    }) {
                                        Text("Try Again")
                                    }
                                    OutlinedButton(onClick = { finish() }) {
                                        Text("Cancel")
                                    }
                                }
                            }
                        }
                        else -> {
                            Column(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Info,
                                        contentDescription = "Info",
                                        tint = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(end = 8.dp)
                                    )
                                    Text(
                                        text = "Scan QR code from PC app to sync",
                                        style = MaterialTheme.typography.titleMedium,
                                    )
                                }
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                syncData?.let {
                                    Text(
                                        text = "User ID: ${userId ?: "Unknown"}",
                                        style = MaterialTheme.typography.bodyMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Text(
                                        text = "Ready to sync extracted content to PC",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @OptIn(androidx.camera.core.ExperimentalGetImage::class)
    private fun processImageProxy(
        imageProxy: ImageProxy,
        scanner: BarcodeScanner,
        onQrCodeDetected: (String) -> Unit
    ) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage,
                imageProxy.imageInfo.rotationDegrees
            )
            
            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    for (barcode in barcodes) {
                        val rawValue = barcode.rawValue
                        if (rawValue != null) {
                            onQrCodeDetected(rawValue)
                            break
                        }
                    }
                }
                .addOnFailureListener {
                    Log.e(TAG, "Barcode scanning failed", it)
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }

    private suspend fun authorizePC(pcUid: String, userId: String): Boolean {
        return try {
            val db = FirebaseFirestore.getInstance()
            
            // Create authorization record
            val authData = hashMapOf(
                "pcUid" to pcUid,
                "userId" to userId,
                "timestamp" to System.currentTimeMillis(),
                "expirationTime" to (System.currentTimeMillis() + 12 * 60 * 60 * 1000) // 12 hours
            )
            
            // Save to Firestore
            db.collection("pc_authorizations")
                .document("${userId}_${pcUid}")
                .set(authData)
                .await()
            
            Log.d(TAG, "PC authorization successful: $pcUid for user $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error authorizing PC", e)
            false
        }
    }

    private suspend fun getUserSyncInfo(): String {
        val auth = FirebaseAuth.getInstance()
        val userId = auth.currentUser?.uid ?: return "Not signed in"
        
        return """
            User ID: $userId
            Status: Ready to connect
            
            Point your camera at the QR code displayed
            in the PC application to establish a connection.
        """.trimIndent()
    }
}

// Extension function to add a border to a Box
fun Modifier.border(
    width: Dp,
    color: Color,
    shape: Shape
) = this
    .then(
        Modifier
            .background(Color.Transparent, shape)
            .padding(width)
            .background(Color.Transparent, shape)
    ) 