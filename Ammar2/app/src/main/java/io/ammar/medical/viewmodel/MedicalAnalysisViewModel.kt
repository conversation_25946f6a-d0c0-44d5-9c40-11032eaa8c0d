package io.ammar.medical.viewmodel

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.ammar.medical.data.MedicalAnalysis
import io.ammar.medical.data.SavedDoctor
import io.ammar.medical.repository.MedicalAnalysisRepository
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.Date
import java.util.UUID
import java.util.regex.Pattern
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import io.ammar.medical.QRScannerActivity
import com.google.firebase.firestore.FieldValue

class MedicalAnalysisViewModel : ViewModel() {
    private val repository = MedicalAnalysisRepository()
    
    // Model for image and text analysis
    private val geminiModel = GenerativeModel(
        modelName = "gemini-2.0-flash",
        apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk",
        generationConfig = generationConfig {
            temperature = 0.7f
            topK = 40
            topP = 0.95f
            maxOutputTokens = 2048
        }
    )
    
    /**
     * Get the Gemini model for use in UI
     */
    fun getGeminiModel(): GenerativeModel {
        return geminiModel
    }
    
    // State for the analysis
    private val _analysisState = MutableStateFlow<AnalysisState>(AnalysisState.Initial)
    val analysisState: StateFlow<AnalysisState> = _analysisState.asStateFlow()
    
    // Current analysis
    private val _currentAnalysis = MutableStateFlow<MedicalAnalysis?>(null)
    val currentAnalysis: StateFlow<MedicalAnalysis?> = _currentAnalysis.asStateFlow()
    
    // Doctor email for sending
    private val _doctorEmail = MutableStateFlow("")
    val doctorEmail: StateFlow<String> = _doctorEmail.asStateFlow()
    
    // Patient ID (would be set from login or patient selection)
    private val _patientId = MutableStateFlow("")
    val patientId: StateFlow<String> = _patientId.asStateFlow()
    
    // Saved doctors
    private val _savedDoctors = MutableStateFlow<List<SavedDoctor>>(emptyList())
    val savedDoctors: StateFlow<List<SavedDoctor>> = _savedDoctors.asStateFlow()
    
    // Selected doctors for sharing
    private val _selectedDoctors = MutableStateFlow<List<SavedDoctor>>(emptyList())
    val selectedDoctors: StateFlow<List<SavedDoctor>> = _selectedDoctors.asStateFlow()
    
    // State for doctor operations
    private val _doctorOperationState = MutableStateFlow<DoctorOperationState>(DoctorOperationState.Initial)
    val doctorOperationState: StateFlow<DoctorOperationState> = _doctorOperationState.asStateFlow()
    
    /**
     * Set the patient ID and load saved doctors
     */
    fun setPatientId(id: String) {
        _patientId.value = id
        loadSavedDoctors()
    }
    
    /**
     * Load saved doctors for the current patient
     */
    private fun loadSavedDoctors() {
        val patientId = _patientId.value
        if (patientId.isEmpty()) return
        
        viewModelScope.launch {
            try {
                _doctorOperationState.value = DoctorOperationState.Loading
                
                val result = repository.getSavedDoctors(patientId)
                if (result.isSuccess) {
                    _savedDoctors.value = result.getOrDefault(emptyList())
                    _doctorOperationState.value = DoctorOperationState.Success
                } else {
                    _doctorOperationState.value = DoctorOperationState.Error("Failed to load doctors")
                }
            } catch (e: Exception) {
                _doctorOperationState.value = DoctorOperationState.Error("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Save a new doctor
     */
    fun saveDoctor(name: String, email: String, specialty: String = "") {
        val patientId = _patientId.value
        if (patientId.isEmpty() || !isValidEmail(email)) {
            _doctorOperationState.value = DoctorOperationState.Error("Invalid patient ID or email")
            return
        }
        
        val doctorId = UUID.randomUUID().toString()
        val savedDoctor = SavedDoctor(
            doctorId = doctorId,
            patientId = patientId,
            doctorName = name,
            doctorEmail = email,
            doctorSpecialty = specialty
        )
        
        viewModelScope.launch {
            try {
                _doctorOperationState.value = DoctorOperationState.Loading
                
                val result = repository.saveDoctor(savedDoctor)
                if (result.isSuccess) {
                    // Reload saved doctors
                    loadSavedDoctors()
                    _doctorOperationState.value = DoctorOperationState.Success
                } else {
                    _doctorOperationState.value = DoctorOperationState.Error("Failed to save doctor")
                }
            } catch (e: Exception) {
                _doctorOperationState.value = DoctorOperationState.Error("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Remove a saved doctor
     */
    fun removeDoctor(doctorId: String) {
        val patientId = _patientId.value
        if (patientId.isEmpty() || doctorId.isEmpty()) {
            _doctorOperationState.value = DoctorOperationState.Error("Invalid patient or doctor ID")
            return
        }
        
        viewModelScope.launch {
            try {
                _doctorOperationState.value = DoctorOperationState.Loading
                
                val result = repository.removeDoctor(patientId, doctorId)
                if (result.isSuccess) {
                    // Reload saved doctors
                    loadSavedDoctors()
                    _doctorOperationState.value = DoctorOperationState.Success
                } else {
                    _doctorOperationState.value = DoctorOperationState.Error("Failed to remove doctor")
                }
            } catch (e: Exception) {
                _doctorOperationState.value = DoctorOperationState.Error("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Toggle doctor selection for sharing
     */
    fun toggleDoctorSelection(doctor: SavedDoctor) {
        val currentSelection = _selectedDoctors.value.toMutableList()
        
        if (currentSelection.any { it.doctorId == doctor.doctorId }) {
            // Remove if already selected
            currentSelection.removeAll { it.doctorId == doctor.doctorId }
        } else {
            // Add if not selected
            currentSelection.add(doctor)
        }
        
        _selectedDoctors.value = currentSelection
    }
    
    /**
     * Clear doctor selection
     */
    fun clearDoctorSelection() {
        _selectedDoctors.value = emptyList()
    }
    
    /**
     * Send analysis to selected doctors
     */
    fun sendAnalysisToSelectedDoctors() {
        val analysis = _currentAnalysis.value ?: return
        val selectedDoctors = _selectedDoctors.value
        
        if (selectedDoctors.isEmpty()) {
            _analysisState.value = AnalysisState.SendError("No doctors selected")
            return
        }
        
        _analysisState.value = AnalysisState.Sending
        
        viewModelScope.launch {
            try {
                val doctorEmails = selectedDoctors.map { it.doctorEmail }
                
                val result = repository.sendAnalysisToMultipleDoctors(
                    analysisId = analysis.id,
                    doctorEmails = doctorEmails,
                    analysisText = analysis.analysisText
                )
                
                if (result.isSuccess) {
                    // Update share counts for all selected doctors
                    selectedDoctors.forEach { doctor ->
                        repository.updateDoctorShareCount(_patientId.value, doctor.doctorId)
                    }
                    
                    _analysisState.value = AnalysisState.SendSuccess
                } else {
                    _analysisState.value = AnalysisState.SendError("Failed to send to doctors")
                }
            } catch (e: Exception) {
                _analysisState.value = AnalysisState.SendError("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Share analysis via external apps (WhatsApp, Email, etc.)
     */
    fun shareAnalysisViaExternalApp(context: Context, packageName: String? = null) {
        val analysis = _currentAnalysis.value ?: return
        
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_SUBJECT, "Medical Analysis Results")
            putExtra(Intent.EXTRA_TEXT, """
                Medical Analysis Results
                
                ${analysis.analysisText}
                
                Analyzed ${analysis.fileCount} files
            """.trimIndent())
            
            // Set specific package if provided (e.g., WhatsApp)
            packageName?.let {
                setPackage(it)
            }
        }
        
        // Create chooser if no specific package
        val intent = if (packageName == null) {
            Intent.createChooser(shareIntent, "Share Analysis Results")
        } else {
            shareIntent
        }
        
        // Start activity from context
        context.startActivity(intent)
    }
    
    /**
     * Share analysis via WhatsApp
     */
    fun shareViaWhatsApp(context: Context) {
        // WhatsApp package name
        val whatsappPackage = "com.whatsapp"
        
        // Check if WhatsApp is installed
        val isWhatsAppInstalled = try {
            context.packageManager.getPackageInfo(whatsappPackage, 0) != null
        } catch (e: Exception) {
            false
        }
        
        if (isWhatsAppInstalled) {
            shareAnalysisViaExternalApp(context, whatsappPackage)
        } else {
            // If WhatsApp is not installed, show a generic share dialog
            shareAnalysisViaExternalApp(context, null)
        }
    }
    
    /**
     * Share medical analysis to PC via QR code
     */
    fun shareAnalysisToPC(context: Context) {
        val analysis = _currentAnalysis.value ?: return
        val patientId = _patientId.value
        
        if (patientId.isEmpty()) {
            _analysisState.value = AnalysisState.SendError("Patient ID is not set")
            return
        }
        
        viewModelScope.launch {
            try {
                _analysisState.value = AnalysisState.Sending
                
                // Save the analysis text to the extracted_texts collection for PC sync
                val db = FirebaseFirestore.getInstance()
                val userId = FirebaseAuth.getInstance().currentUser?.uid ?: patientId
                
                // Create a document with the same format as extracted texts
                val expirationTime = System.currentTimeMillis() + (60 * 60 * 1000) // 1 hour expiration
                
                // Save each file's original extracted text
                analysis.originalExtractedTexts.forEachIndexed { index, text ->
                    val extractedTextData = hashMapOf(
                        "userId" to userId,
                        "text" to text,
                        "timestamp" to System.currentTimeMillis(),
                        "expirationTime" to expirationTime,
                        "sourceType" to "medical_report",
                        "fileName" to "medical_report_${analysis.id}_${index + 1}.txt"
                    )
                    
                    // Save to extracted_texts collection for PC sync
                    db.collection("extracted_texts").document()
                        .set(extractedTextData)
                        .await()
                }
                
                // Launch QR scanner activity
                val intent = Intent(context, QRScannerActivity::class.java)
                context.startActivity(intent)
                
                _analysisState.value = AnalysisState.SendSuccess
            } catch (e: Exception) {
                _analysisState.value = AnalysisState.SendError("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Set the doctor email
     */
    fun setDoctorEmail(email: String) {
        _doctorEmail.value = email
    }
    
    /**
     * Analyze medical reports
     */
    fun analyzeMedicalReports(extractedTexts: List<String>, fileTypes: List<String>) {
        _analysisState.value = AnalysisState.Analyzing
        
        viewModelScope.launch {
            try {
                // Store original texts for PC sync
                val originalTexts = extractedTexts.toList()
                
                // Combine all extracted text for analysis
                val allExtractedText = extractedTexts.mapIndexed { index, text ->
                    val fileType = if (index < fileTypes.size) fileTypes[index] else "Document"
                    "$fileType ${index + 1}: $text"
                }.joinToString("\n\n")
                
                // Create prompt for analysis
                val prompt = """
                    You are a medical assistant analyzing patient medical reports, lab results, and radiology reports.
                    Analyze the following text extracted from multiple medical files.
                    
                    Provide a comprehensive medical analysis that includes:
                    
                    1. SUMMARY: A brief 2-3 sentence overview of the patient's condition
                    2. CRITICAL FINDINGS: Highlight any abnormal or concerning values that require immediate attention (mark these with 🚨)
                    3. KEY METRICS: List important lab values, vital signs, or measurements with their normal ranges
                    4. DIAGNOSIS: Potential diagnoses based on the findings
                    5. RECOMMENDATIONS: Suggested next steps, additional tests, or treatments
                    
                    Format your response in a clear, structured way that a doctor can quickly review.
                    Be concise but thorough, and prioritize the most clinically significant information.
                    
                    Here is the extracted text from all medical files:
                    
                    $allExtractedText
                """.trimIndent()
                
                // Generate analysis
                val response = geminiModel.generateContent(prompt)
                val analysisText = response.text
                
                if (!analysisText.isNullOrBlank()) {
                    // Extract critical findings
                    val criticalFindings = extractCriticalFindings(analysisText)
                    
                    // Extract recommendations
                    val recommendations = extractRecommendations(analysisText)
                    
                    // Create medical analysis object with original texts
                    val analysis = MedicalAnalysis(
                        id = UUID.randomUUID().toString(),
                        patientId = _patientId.value,
                        createdAt = Date(),
                        analysisText = analysisText,
                        fileCount = extractedTexts.size,
                        fileTypes = fileTypes,
                        criticalFindings = criticalFindings,
                        recommendations = recommendations,
                        originalExtractedTexts = originalTexts // Store original texts
                    )
                    
                    _currentAnalysis.value = analysis
                    _analysisState.value = AnalysisState.Success(analysisText)
                    
                    // Save analysis to repository
                    if (_patientId.value.isNotEmpty()) {
                        repository.saveMedicalAnalysis(analysis)
                    }
                } else {
                    _analysisState.value = AnalysisState.Error("Failed to generate analysis")
                }
            } catch (e: Exception) {
                _analysisState.value = AnalysisState.Error("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Analyze medical reports in Egyptian Arabic
     */
    fun analyzeMedicalReportsInArabic(extractedTexts: List<String>, fileTypes: List<String>) {
        _analysisState.value = AnalysisState.Analyzing
        
        viewModelScope.launch {
            try {
                // Store original texts for PC sync
                val originalTexts = extractedTexts.toList()
                
                // Combine all extracted text for analysis
                val allExtractedText = extractedTexts.mapIndexed { index, text ->
                    val fileType = if (index < fileTypes.size) fileTypes[index] else "Document"
                    "$fileType ${index + 1}: $text"
                }.joinToString("\n\n")
                
                // Create prompt for analysis in Egyptian Arabic
                val prompt = """
                    You are a medical assistant analyzing patient medical reports, lab results, and radiology reports.
                    Analyze the following text extracted from multiple medical files.
                    
                    Provide a comprehensive medical analysis in Egyptian Arabic dialect (using Arabic script) that includes:
                    
                    1. ملخص: A brief 2-3 sentence overview of the patient's condition
                    2. نتائج هامة: Highlight any abnormal or concerning values that require immediate attention (mark these with 🚨)
                    3. قياسات مهمة: List important lab values, vital signs, or measurements with their normal ranges
                    4. التشخيص: Potential diagnoses based on the findings
                    5. توصيات: Suggested next steps, additional tests, or treatments
                    
                    Format your response in a clear, structured way using Egyptian Arabic dialect and terminology that a doctor can quickly review.
                    Be concise but thorough, and prioritize the most clinically significant information.
                    
                    Here is the extracted text from all medical files:
                    
                    $allExtractedText
                """.trimIndent()
                
                // Generate analysis
                val response = geminiModel.generateContent(prompt)
                val analysisText = response.text
                
                if (!analysisText.isNullOrBlank()) {
                    // Extract critical findings
                    val criticalFindings = extractCriticalFindings(analysisText)
                    
                    // Extract recommendations
                    val recommendations = extractRecommendations(analysisText)
                    
                    // Create medical analysis object with original texts
                    val analysis = MedicalAnalysis(
                        id = UUID.randomUUID().toString(),
                        patientId = _patientId.value,
                        createdAt = Date(),
                        analysisText = analysisText,
                        fileCount = extractedTexts.size,
                        fileTypes = fileTypes,
                        criticalFindings = criticalFindings,
                        recommendations = recommendations,
                        originalExtractedTexts = originalTexts, // Store original texts
                        isArabic = true // Mark as Arabic analysis
                    )
                    
                    _currentAnalysis.value = analysis
                    _analysisState.value = AnalysisState.Success(analysisText)
                    
                    // Save analysis to repository
                    if (_patientId.value.isNotEmpty()) {
                        repository.saveMedicalAnalysis(analysis)
                    }
                } else {
                    _analysisState.value = AnalysisState.Error("Failed to generate analysis")
                }
            } catch (e: Exception) {
                _analysisState.value = AnalysisState.Error("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Send analysis to doctor
     */
    fun sendAnalysisToDoctor(doctorEmail: String) {
        if (!isValidEmail(doctorEmail)) {
            _analysisState.value = AnalysisState.SendError("Invalid email address")
            return
        }
        
        val analysis = _currentAnalysis.value ?: return
        
        _analysisState.value = AnalysisState.Sending
        
        viewModelScope.launch {
            try {
                val result = repository.sendAnalysisToDoctor(
                    analysisId = analysis.id,
                    doctorEmail = doctorEmail,
                    analysisText = analysis.analysisText
                )
                
                if (result.isSuccess) {
                    _analysisState.value = AnalysisState.SendSuccess
                    
                    // Update current analysis with doctor email
                    _currentAnalysis.value = analysis.copy(doctorId = doctorEmail)
                } else {
                    _analysisState.value = AnalysisState.SendError("Failed to send to doctor")
                }
            } catch (e: Exception) {
                _analysisState.value = AnalysisState.SendError("Error: ${e.message}")
            }
        }
    }
    
    /**
     * Extract critical findings from analysis text
     */
    private fun extractCriticalFindings(analysisText: String): List<String> {
        val findings = mutableListOf<String>()
        
        // Look for the CRITICAL FINDINGS section
        val criticalPattern = Pattern.compile(
            "CRITICAL FINDINGS:?\\s*([\\s\\S]*?)(?=\\n\\s*\\d+\\.\\s*[A-Z]|$)",
            Pattern.CASE_INSENSITIVE
        )
        
        val matcher = criticalPattern.matcher(analysisText)
        if (matcher.find()) {
            val criticalSection = matcher.group(1)?.trim() ?: ""
            
            // Split by bullet points or new lines
            val items = criticalSection.split("(?=•|\\n-|\\n\\*|\\n\\d+\\.)".toRegex())
                .map { it.trim() }
                .filter { it.isNotEmpty() }
            
            findings.addAll(items)
        }
        
        // Also look for items marked with 🚨
        val alertPattern = Pattern.compile("🚨\\s*([^\\n]+)")
        val alertMatcher = alertPattern.matcher(analysisText)
        
        while (alertMatcher.find()) {
            val alertItem = alertMatcher.group(1)?.trim() ?: ""
            if (alertItem.isNotEmpty() && !findings.contains(alertItem)) {
                findings.add(alertItem)
            }
        }
        
        return findings
    }
    
    /**
     * Extract recommendations from analysis text
     */
    private fun extractRecommendations(analysisText: String): List<String> {
        val recommendations = mutableListOf<String>()
        
        // Look for the RECOMMENDATIONS section
        val recPattern = Pattern.compile(
            "RECOMMENDATIONS:?\\s*([\\s\\S]*?)(?=\\n\\s*\\d+\\.\\s*[A-Z]|$)",
            Pattern.CASE_INSENSITIVE
        )
        
        val matcher = recPattern.matcher(analysisText)
        if (matcher.find()) {
            val recSection = matcher.group(1)?.trim() ?: ""
            
            // Split by bullet points or new lines
            val items = recSection.split("(?=•|\\n-|\\n\\*|\\n\\d+\\.)".toRegex())
                .map { it.trim() }
                .filter { it.isNotEmpty() }
            
            recommendations.addAll(items)
        }
        
        return recommendations
    }
    
    /**
     * Validate email address
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
    
    /**
     * Reset the analysis state
     */
    fun resetAnalysisState() {
        _analysisState.value = AnalysisState.Initial
    }
}

/**
 * States for the medical analysis process
 */
sealed class AnalysisState {
    object Initial : AnalysisState()
    object Analyzing : AnalysisState()
    data class Success(val analysisText: String) : AnalysisState()
    data class Error(val message: String) : AnalysisState()
    object Sending : AnalysisState()
    object SendSuccess : AnalysisState()
    data class SendError(val message: String) : AnalysisState()
}

/**
 * States for doctor operations
 */
sealed class DoctorOperationState {
    object Initial : DoctorOperationState()
    object Loading : DoctorOperationState()
    object Success : DoctorOperationState()
    data class Error(val message: String) : DoctorOperationState()
} 