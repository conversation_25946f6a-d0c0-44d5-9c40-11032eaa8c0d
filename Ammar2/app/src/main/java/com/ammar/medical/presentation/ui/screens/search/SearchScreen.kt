package com.ammar.medical.presentation.ui.screens.search

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.ammar.medical.R
import com.ammar.medical.data.model.Category
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.SearchRepository
import com.ammar.medical.presentation.components.CommonTopAppBar
import com.ammar.medical.presentation.components.LoadingIndicator
import com.ammar.medical.presentation.components.TagInput

@Composable
fun SearchScreen(
    onNavigateBack: () -> Unit,
    onNavigateToDocument: (Long) -> Unit,
    viewModel: SearchViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            CommonTopAppBar(
                title = stringResource(R.string.search),
                navigationIcon = Icons.Default.ArrowBack,
                onNavigationClick = onNavigateBack,
                actions = {
                    IconButton(onClick = viewModel::toggleFilters) {
                        Icon(
                            imageVector = Icons.Default.FilterList,
                            contentDescription = stringResource(R.string.filters)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (uiState) {
                is SearchUiState.Loading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is SearchUiState.Success -> {
                    val state = uiState as SearchUiState.Success
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        SearchBar(
                            query = state.query,
                            onQueryChange = viewModel::updateQuery,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        )

                        AnimatedVisibility(
                            visible = state.showFilters,
                            enter = expandVertically() + fadeIn(),
                            exit = shrinkVertically() + fadeOut()
                        ) {
                            FilterPanel(
                                selectedTypes = state.selectedTypes,
                                selectedTags = state.selectedTags,
                                selectedCategories = state.selectedCategories,
                                availableTags = state.availableTags,
                                availableCategories = state.availableCategories,
                                onTypeSelected = viewModel::toggleDocumentType,
                                onTagSelected = viewModel::toggleTag,
                                onCategorySelected = viewModel::toggleCategory,
                                onClearFilters = viewModel::clearFilters,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }

                        DocumentList(
                            documents = state.documents,
                            onDocumentClick = onNavigateToDocument,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                        )
                    }

                    state.error?.let { error ->
                        ErrorSnackbar(
                            message = error,
                            onDismiss = viewModel::clearError
                        )
                    }
                }
                is SearchUiState.Error -> {
                    Text(
                        text = (uiState as SearchUiState.Error).message,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = { Text(stringResource(R.string.search_hint)) },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = null
            )
        },
        modifier = modifier
    )
}

@Composable
private fun FilterPanel(
    selectedTypes: Set<Document.DocumentType>,
    selectedTags: Set<String>,
    selectedCategories: Set<Long>,
    availableTags: List<String>,
    availableCategories: List<Category>,
    onTypeSelected: (Document.DocumentType) -> Unit,
    onTagSelected: (String) -> Unit,
    onCategorySelected: (Long) -> Unit,
    onClearFilters: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.filters),
                style = MaterialTheme.typography.titleMedium
            )
            TextButton(onClick = onClearFilters) {
                Text(stringResource(R.string.clear))
            }
        }

        // Document Types
        Text(
            text = stringResource(R.string.document_types),
            style = MaterialTheme.typography.titleSmall
        )
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Document.DocumentType.values().forEach { type ->
                FilterChip(
                    selected = type in selectedTypes,
                    onClick = { onTypeSelected(type) },
                    label = { Text(type.name) }
                )
            }
        }

        // Categories
        Text(
            text = stringResource(R.string.categories),
            style = MaterialTheme.typography.titleSmall
        )
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            availableCategories.forEach { category ->
                FilterChip(
                    selected = category.id in selectedCategories,
                    onClick = { onCategorySelected(category.id) },
                    label = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .background(
                                        color = Color(category.color),
                                        shape = MaterialTheme.shapes.small
                                    )
                            )
                            Text(category.name)
                        }
                    }
                )
            }
        }

        // Tags
        Text(
            text = stringResource(R.string.tags),
            style = MaterialTheme.typography.titleSmall
        )
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            availableTags.forEach { tag ->
                FilterChip(
                    selected = tag in selectedTags,
                    onClick = { onTagSelected(tag) },
                    label = { Text(tag) }
                )
            }
        }
    }
}

@Composable
private fun DocumentList(
    documents: List<Document>,
    onDocumentClick: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(documents) { document ->
            DocumentItem(
                document = document,
                onClick = { onDocumentClick(document.id) }
            )
        }
    }
}

@Composable
private fun DocumentItem(
    document: Document,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = document.title,
                style = MaterialTheme.typography.titleMedium
            )
            Text(
                text = document.description,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2
            )
        }
    }
}

@Composable
private fun ErrorSnackbar(
    message: String,
    onDismiss: () -> Unit
) {
    Snackbar(
        modifier = Modifier.padding(16.dp),
        action = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.dismiss))
            }
        }
    ) {
        Text(message)
    }
} 