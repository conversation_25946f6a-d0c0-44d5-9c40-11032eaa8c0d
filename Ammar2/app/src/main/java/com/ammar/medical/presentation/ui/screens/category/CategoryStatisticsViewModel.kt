package com.ammar.medical.presentation.ui.screens.category

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.data.model.Category
import com.ammar.medical.data.model.Document
import com.ammar.medical.data.repository.CategoryRepository
import com.ammar.medical.data.repository.DocumentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class CategoryStats(
    val category: Category,
    val documentCount: Int,
    val totalSize: Long,
    val averageAge: Long
)

sealed class CategoryStatisticsUiState {
    data object Loading : CategoryStatisticsUiState()
    data class Success(
        val categories: List<Category>,
        val stats: List<CategoryStats>,
        val totalDocuments: Int,
        val totalSize: Long,
        val error: String? = null
    ) : CategoryStatisticsUiState()
    data class Error(val message: String) : CategoryStatisticsUiState()
}

@HiltViewModel
class CategoryStatisticsViewModel @Inject constructor(
    private val categoryRepository: CategoryRepository,
    private val documentRepository: DocumentRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<CategoryStatisticsUiState>(CategoryStatisticsUiState.Loading)
    val uiState: StateFlow<CategoryStatisticsUiState> = _uiState.asStateFlow()

    init {
        loadStatistics()
    }

    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                combine(
                    categoryRepository.categories,
                    documentRepository.getAllDocuments()
                ) { categories, documents ->
                    val stats = categories.map { category ->
                        val categoryDocs = documents.filter { it.categoryId == category.id }
                        CategoryStats(
                            category = category,
                            documentCount = categoryDocs.size,
                            totalSize = categoryDocs.sumOf { it.fileSize },
                            averageAge = if (categoryDocs.isNotEmpty()) {
                                categoryDocs.map { it.createdAt.toEpochDay() }.average().toLong()
                            } else 0
                        )
                    }

                    CategoryStatisticsUiState.Success(
                        categories = categories,
                        stats = stats,
                        totalDocuments = documents.size,
                        totalSize = documents.sumOf { it.fileSize }
                    )
                }.collect { state ->
                    _uiState.value = state
                }
            } catch (e: Exception) {
                _uiState.value = CategoryStatisticsUiState.Error(e.message ?: "Failed to load statistics")
            }
        }
    }

    fun refresh() {
        loadStatistics()
    }

    fun clearError() {
        val currentState = _uiState.value as? CategoryStatisticsUiState.Success ?: return
        _uiState.value = currentState.copy(error = null)
    }
} 