# PC Text Receiver for Ammar2 Android App

This tool allows you to receive text directly from the Ammar2 Android app to your PC using QR code synchronization.

## How It Works

1. Your PC generates a QR code containing a URL like `http://*************:5000/send_text/session123`
2. The Android app scans the QR code and extracts the URL
3. The Android app sends text to the PC via an HTTP POST request
4. The PC receives the text and displays it in the terminal

## Requirements

- Python 3.6 or newer
- The following Python packages (automatically installed if missing):
  - Flask
  - qrcode[pil]

## Optional Dependencies
- netifaces (for better IP address detection)

## Installation

1. Ensure you have Python installed on your PC
2. Install the basic required packages:

```bash
pip install flask qrcode[pil]
```

## Usage

1. Run the PC receiver script:

```bash
python pc_receiver.py
```

2. A QR code will be generated and displayed in the terminal and saved as a file

3. On your Android app:
   - Navigate to any screen with the "Connect to PC" option (ShareReceiveScreen, TextRephraserScreen, or MultiImageAnalyzerScreen)
   - Tap the QR code scanner button
   - Scan the QR code displayed on your PC
   - The app will show "Connected to PC" with the server URL

4. Now you can send text from the app to your PC:
   - In the app, extract text from an image, rephrase text, or analyze multiple images
   - Once text is available, tap "Send to PC" button
   - The text will be sent to your PC and displayed in the terminal

## Advanced Options

You can customize several aspects of the server:

```bash
python pc_receiver.py --port 8000 --session user123 --ip *************
```

Available options:
- `--port` or `-p`: Specify the port to run the server on (default: 5000)
- `--session` or `-s`: Set a custom session ID (default: 'default')
- `--ip` or `-i`: Manually specify the IP address to use (useful if auto-detection fails)

## Troubleshooting

- **Cannot connect to PC**: Make sure your PC and Android device are on the same network
- **QR code scan fails**: Try moving your camera closer to the QR code or improve lighting
- **Text not received**: Check if the server is running and verify the network connection
- **IP detection failure**: Use the `--ip` option to manually specify your PC's IP address
- **QR code not visible in terminal**: Check the generated image file at `server/qrcode.png`

## Notes

- The connection is only valid while the Python script is running
- If you restart the script, you'll need to scan the QR code again to reconnect
- The text is not stored permanently unless you modify the script to save it to a file 