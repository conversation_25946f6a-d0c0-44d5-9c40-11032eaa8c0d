#!/bin/bash

# Directory paths
OLD_DIR="app/src/main/java/com/example/ammar2"
OLD_BACKUP="app/src/main/java/com/example/ammar2_backup"

# Check if the old directory exists
if [ -d "$OLD_DIR" ]; then
    # Move the old directory to a backup location
    echo "Moving $OLD_DIR to $OLD_BACKUP..."
    mv "$OLD_DIR" "$OLD_BACKUP"
    echo "Old package moved. You can now build with the new package structure."
else
    echo "Old package directory not found at $OLD_DIR"
fi 