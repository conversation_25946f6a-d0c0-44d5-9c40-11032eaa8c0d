package io.ammar.medical.ui.components

import android.app.AlertDialog
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.ammar.medical.data.Contact
import io.ammar.medical.viewmodel.ContactViewModel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Dialog for displaying and managing contacts
 */
@Composable
fun ContactsDialog(
    onDismiss: () -> Unit,
    onSelectContact: (Contact) -> Unit,
    contactViewModel: ContactViewModel = viewModel()
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    val contacts by contactViewModel.contacts.collectAsState()
    val isLoading by contactViewModel.isLoading.collectAsState()
    val error by contactViewModel.error.collectAsState()
    val successMessage by contactViewModel.successMessage.collectAsState()
    
    var showAddContactDialog by remember { mutableStateOf(false) }
    
    // Load contacts when dialog is shown
    LaunchedEffect(Unit) {
        contactViewModel.loadContacts()
    }
    
    // Show success message as toast
    LaunchedEffect(successMessage) {
        successMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            contactViewModel.clearSuccessMessage()
        }
    }
    
    // Show error message as toast
    LaunchedEffect(error) {
        error?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            contactViewModel.clearError()
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Your Contacts") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    Button(
                        onClick = { showAddContactDialog = true }
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Add Contact",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Add Contact")
                    }
                }
                
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else if (contacts.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No contacts found. Add some contacts to easily message them.",
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp)
                    ) {
                        items(contacts) { contact ->
                            ContactItem(
                                contact = contact,
                                onMessageClick = {
                                    onSelectContact(contact)
                                    onDismiss()
                                },
                                onDeleteClick = {
                                    // Show confirmation dialog
                                    scope.launch {
                                        val confirmed = withContext(kotlinx.coroutines.Dispatchers.Main) {
                                            val result = CompletableDeferred<Boolean>()
                                            
                                            val dialog = AlertDialog.Builder(context)
                                                .setTitle("Delete Contact")
                                                .setMessage("Are you sure you want to delete ${contact.name}?")
                                                .setPositiveButton("Delete") { _, _ -> result.complete(true) }
                                                .setNegativeButton("Cancel") { _, _ -> result.complete(false) }
                                                .create()
                                            
                                            dialog.show()
                                            result.await()
                                        }
                                        
                                        if (confirmed) {
                                            contactViewModel.deleteContact(contact)
                                        }
                                    }
                                }
                            )
                            Divider()
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("Close")
            }
        }
    )
    
    // Show add contact dialog when requested
    if (showAddContactDialog) {
        AddContactDialog(
            onDismiss = { showAddContactDialog = false },
            onAddContact = { uid, name ->
                contactViewModel.addContact(uid, name)
                showAddContactDialog = false
            }
        )
    }
}

/**
 * Dialog for adding a new contact
 */
@Composable
fun AddContactDialog(
    onDismiss: () -> Unit,
    onAddContact: (uid: String, name: String) -> Unit
) {
    var uid by remember { mutableStateOf("") }
    var name by remember { mutableStateOf("") }
    var isAddingContact by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = { 
            if (!isAddingContact) {
                onDismiss()
            }
        },
        title = { Text("Add New Contact") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedTextField(
                    value = uid,
                    onValueChange = { uid = it },
                    label = { Text("Contact User ID") },
                    placeholder = { Text("Enter contact's User ID") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isAddingContact
                )
                
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Contact Name (Optional)") },
                    placeholder = { Text("Enter a name for this contact") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isAddingContact
                )
                
                if (isAddingContact) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    isAddingContact = true
                    onAddContact(uid, name)
                },
                enabled = !isAddingContact && uid.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isAddingContact
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Composable for displaying a single contact
 */
@Composable
fun ContactItem(
    contact: Contact,
    onMessageClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Contact icon
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = "Contact",
            modifier = Modifier
                .size(40.dp)
                .padding(end = 8.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        // Contact details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = contact.name,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "ID: ${contact.uid}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        // Action buttons
        IconButton(onClick = onMessageClick) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = "Message",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        IconButton(onClick = onDeleteClick) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = "Delete",
                tint = MaterialTheme.colorScheme.error
            )
        }
    }
} 