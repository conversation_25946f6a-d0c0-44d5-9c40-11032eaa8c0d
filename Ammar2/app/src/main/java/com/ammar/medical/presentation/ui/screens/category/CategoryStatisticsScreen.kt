package com.ammar.medical.presentation.ui.screens.category

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.ammar.medical.R
import com.ammar.medical.presentation.ui.components.ErrorSnackbar
import com.ammar.medical.presentation.ui.components.LoadingIndicator
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryStatisticsScreen(
    viewModel: CategoryStatisticsViewModel,
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.category_statistics)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.navigate_back))
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refresh() }) {
                        Icon(Icons.Default.Refresh, contentDescription = stringResource(R.string.refresh))
                    }
                }
            )
        }
    ) { padding ->
        when (uiState) {
            is CategoryStatisticsUiState.Loading -> {
                LoadingIndicator(modifier = Modifier.fillMaxSize())
            }
            is CategoryStatisticsUiState.Success -> {
                val state = uiState as CategoryStatisticsUiState.Success
                CategoryStatisticsContent(
                    modifier = Modifier.padding(padding),
                    stats = state.stats,
                    totalDocuments = state.totalDocuments,
                    totalSize = state.totalSize
                )
            }
            is CategoryStatisticsUiState.Error -> {
                val error = (uiState as CategoryStatisticsUiState.Error).message
                ErrorSnackbar(
                    error = error,
                    onDismiss = { viewModel.clearError() }
                )
            }
        }
    }
}

@Composable
private fun CategoryStatisticsContent(
    modifier: Modifier = Modifier,
    stats: List<CategoryStats>,
    totalDocuments: Int,
    totalSize: Long
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            StatisticsSummary(
                totalDocuments = totalDocuments,
                totalSize = totalSize
            )
        }

        items(stats) { categoryStats ->
            CategoryStatsCard(stats = categoryStats)
        }
    }
}

@Composable
private fun StatisticsSummary(
    totalDocuments: Int,
    totalSize: Long
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(R.string.total_statistics),
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = stringResource(R.string.total_documents_count, totalDocuments),
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = stringResource(R.string.total_size, formatFileSize(totalSize)),
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
private fun CategoryStatsCard(stats: CategoryStats) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = stats.category.icon,
                        contentDescription = null,
                        tint = stats.category.color
                    )
                    Text(
                        text = stats.category.name,
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                Text(
                    text = stringResource(R.string.document_count, stats.documentCount),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = stringResource(R.string.total_size, formatFileSize(stats.totalSize)),
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (stats.averageAge > 0) {
                Text(
                    text = stringResource(
                        R.string.average_age,
                        ChronoUnit.DAYS.between(
                            LocalDate.ofEpochDay(stats.averageAge),
                            LocalDate.now()
                        )
                    ),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

private fun formatFileSize(size: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB")
    var fileSize = size.toDouble()
    var unitIndex = 0
    
    while (fileSize >= 1024 && unitIndex < units.size - 1) {
        fileSize /= 1024
        unitIndex++
    }
    
    return "%.1f %s".format(fileSize, units[unitIndex])
} 