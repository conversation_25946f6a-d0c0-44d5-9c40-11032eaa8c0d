/**
 * Example Firebase Cloud Function to manage activation codes
 * 
 * This function can be deployed to Firebase Cloud Functions and used
 * to manage activation codes programmatically.
 * 
 * To deploy:
 * 1. Install Firebase CLI
 * 2. Initialize Firebase Functions in your project
 * 3. Add this code to your functions/index.js file
 * 4. Deploy with `firebase deploy --only functions`
 */

const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

// Collection and document constants
const ACTIVATION_COLLECTION = 'configuration';
const ACTIVATION_DOCUMENT = 'activation_codes';

/**
 * HTTP function to add a new activation code
 * 
 * Example request:
 * POST /addActivationCode
 * Body: { "code": "NEWCODE2024", "key": "your_secret_api_key" }
 */
exports.addActivationCode = functions.https.onRequest(async (req, res) => {
  // Implement API key validation for security
  const apiKey = req.body.key;
  const secretKey = functions.config().activation.key; // Set with: firebase functions:config:set activation.key="your_secret"
  
  if (apiKey !== secretKey) {
    res.status(403).send({ error: 'Unauthorized' });
    return;
  }
  
  // Get the new code to add
  const newCode = req.body.code;
  if (!newCode || typeof newCode !== 'string' || newCode.length < 5) {
    res.status(400).send({ error: 'Invalid code format. Code must be at least 5 characters.' });
    return;
  }
  
  try {
    // Get current codes
    const db = admin.firestore();
    const docRef = db.collection(ACTIVATION_COLLECTION).doc(ACTIVATION_DOCUMENT);
    const doc = await docRef.get();
    
    if (!doc.exists) {
      // Create document if it doesn't exist
      await docRef.set({
        codes: [newCode],
        last_updated: admin.firestore.FieldValue.serverTimestamp()
      });
      res.status(201).send({ success: true, message: 'Code added and document created' });
      return;
    }
    
    // Get existing codes
    const data = doc.data();
    const codes = data.codes || [];
    
    // Check if code already exists
    if (codes.includes(newCode)) {
      res.status(409).send({ error: 'Code already exists' });
      return;
    }
    
    // Add new code
    codes.push(newCode);
    
    // Update document
    await docRef.update({
      codes: codes,
      last_updated: admin.firestore.FieldValue.serverTimestamp()
    });
    
    res.status(200).send({ success: true, message: 'Code added successfully' });
  } catch (error) {
    console.error('Error adding activation code:', error);
    res.status(500).send({ error: 'Server error', details: error.message });
  }
});

/**
 * HTTP function to remove an activation code
 * 
 * Example request:
 * POST /removeActivationCode
 * Body: { "code": "OLDCODE2024", "key": "your_secret_api_key" }
 */
exports.removeActivationCode = functions.https.onRequest(async (req, res) => {
  // Implement API key validation for security
  const apiKey = req.body.key;
  const secretKey = functions.config().activation.key;
  
  if (apiKey !== secretKey) {
    res.status(403).send({ error: 'Unauthorized' });
    return;
  }
  
  // Get the code to remove
  const codeToRemove = req.body.code;
  if (!codeToRemove) {
    res.status(400).send({ error: 'Code is required' });
    return;
  }
  
  try {
    // Get current codes
    const db = admin.firestore();
    const docRef = db.collection(ACTIVATION_COLLECTION).doc(ACTIVATION_DOCUMENT);
    const doc = await docRef.get();
    
    if (!doc.exists) {
      res.status(404).send({ error: 'Activation codes document not found' });
      return;
    }
    
    // Get existing codes
    const data = doc.data();
    const codes = data.codes || [];
    
    // Check if code exists
    if (!codes.includes(codeToRemove)) {
      res.status(404).send({ error: 'Code not found' });
      return;
    }
    
    // Remove code
    const updatedCodes = codes.filter(code => code !== codeToRemove);
    
    // Update document
    await docRef.update({
      codes: updatedCodes,
      last_updated: admin.firestore.FieldValue.serverTimestamp()
    });
    
    res.status(200).send({ success: true, message: 'Code removed successfully' });
  } catch (error) {
    console.error('Error removing activation code:', error);
    res.status(500).send({ error: 'Server error', details: error.message });
  }
});

/**
 * HTTP function to list all activation codes
 * 
 * Example request:
 * GET /listActivationCodes?key=your_secret_api_key
 */
exports.listActivationCodes = functions.https.onRequest(async (req, res) => {
  // Implement API key validation for security
  const apiKey = req.query.key;
  const secretKey = functions.config().activation.key;
  
  if (apiKey !== secretKey) {
    res.status(403).send({ error: 'Unauthorized' });
    return;
  }
  
  try {
    // Get codes
    const db = admin.firestore();
    const docRef = db.collection(ACTIVATION_COLLECTION).doc(ACTIVATION_DOCUMENT);
    const doc = await docRef.get();
    
    if (!doc.exists) {
      res.status(404).send({ error: 'Activation codes document not found' });
      return;
    }
    
    // Get data
    const data = doc.data();
    
    res.status(200).send({
      codes: data.codes || [],
      last_updated: data.last_updated
    });
  } catch (error) {
    console.error('Error listing activation codes:', error);
    res.status(500).send({ error: 'Server error', details: error.message });
  }
});