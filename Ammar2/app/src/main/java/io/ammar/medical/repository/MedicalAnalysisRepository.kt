package io.ammar.medical.repository

import android.net.Uri
import android.util.Log
import io.ammar.medical.data.MedicalAnalysis
import io.ammar.medical.data.SavedDoctor
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID

/**
 * Repository for handling medical analysis data
 */
class MedicalAnalysisRepository {
    private val firestore = FirebaseFirestore.getInstance()
    private val storage = FirebaseStorage.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    private val analysisCollection = firestore.collection("medical_analyses")
    private val patientCollection = firestore.collection("patients")
    private val doctorCollection = firestore.collection("doctors")
    private val savedDoctorsCollection = firestore.collection("saved_doctors")
    
    // Get current user ID
    private val currentUserId: String
        get() = auth.currentUser?.uid ?: ""
    
    /**
     * Save a medical analysis to Firestore
     */
    suspend fun saveMedicalAnalysis(analysis: MedicalAnalysis): Result<String> = withContext(Dispatchers.IO) {
        try {
            val analysisId = analysis.id.ifEmpty { UUID.randomUUID().toString() }
            val analysisWithId = analysis.copy(id = analysisId)
            
            analysisCollection.document(analysisId).set(analysisWithId).await()
            
            // Update patient's last analysis
            if (analysis.patientId.isNotEmpty()) {
                patientCollection.document(analysis.patientId)
                    .update("lastAnalysisId", analysisId, "lastAnalysisDate", Date())
                    .await()
            }
            
            // If doctor is specified, add to their queue
            if (analysis.doctorId.isNotEmpty()) {
                doctorCollection.document(analysis.doctorId)
                    .update("pendingReviews", com.google.firebase.firestore.FieldValue.arrayUnion(analysisId))
                    .await()
            }
            
            Result.success(analysisId)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error saving analysis", e)
            Result.failure(e)
        }
    }
    
    /**
     * Upload medical report files to Firebase Storage
     */
    suspend fun uploadMedicalFiles(
        patientId: String, 
        fileUris: List<Uri>, 
        fileTypes: List<String>
    ): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val uploadedUrls = mutableListOf<String>()
            
            fileUris.forEachIndexed { index, uri ->
                val fileType = if (index < fileTypes.size) fileTypes[index] else "unknown"
                val fileExtension = when (fileType) {
                    "pdf" -> ".pdf"
                    "image" -> ".jpg"
                    else -> ".dat"
                }
                
                val fileName = "patient_$patientId/${UUID.randomUUID()}$fileExtension"
                val storageRef = storage.reference.child("medical_reports/$fileName")
                
                // Upload file
                val uploadTask = storageRef.putFile(uri).await()
                
                // Get download URL
                val downloadUrl = storageRef.downloadUrl.await().toString()
                uploadedUrls.add(downloadUrl)
            }
            
            Result.success(uploadedUrls)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error uploading files", e)
            Result.failure(e)
        }
    }
    
    /**
     * Send analysis to doctor by email
     */
    suspend fun sendAnalysisToDoctor(
        analysisId: String,
        doctorEmail: String,
        patientName: String = "Patient",
        analysisText: String
    ): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // In a real app, you would use Firebase Cloud Functions or your backend
            // to send an email to the doctor
            
            // For now, we'll just simulate success and store the doctor email
            analysisCollection.document(analysisId)
                .update(
                    "sentToDoctorEmail", doctorEmail,
                    "sentToDoctorAt", Date()
                )
                .await()
            
            Result.success(true)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error sending to doctor", e)
            Result.failure(e)
        }
    }
    
    /**
     * Get analyses for a patient
     */
    suspend fun getPatientAnalyses(patientId: String): Result<List<MedicalAnalysis>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = analysisCollection
                .whereEqualTo("patientId", patientId)
                .orderBy("createdAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
                .get()
                .await()
            
            val analyses = snapshot.documents.mapNotNull { doc ->
                doc.toObject(MedicalAnalysis::class.java)
            }
            
            Result.success(analyses)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error getting patient analyses", e)
            Result.failure(e)
        }
    }
    
    /**
     * Get analyses for a doctor to review
     */
    suspend fun getDoctorPendingAnalyses(doctorId: String): Result<List<MedicalAnalysis>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = analysisCollection
                .whereEqualTo("doctorId", doctorId)
                .whereEqualTo("isReviewed", false)
                .orderBy("createdAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
                .get()
                .await()
            
            val analyses = snapshot.documents.mapNotNull { doc ->
                doc.toObject(MedicalAnalysis::class.java)
            }
            
            Result.success(analyses)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error getting doctor analyses", e)
            Result.failure(e)
        }
    }
    
    /**
     * Save a doctor to the patient's list
     */
    suspend fun saveDoctor(savedDoctor: SavedDoctor): Result<String> = withContext(Dispatchers.IO) {
        try {
            // Use a composite ID to ensure uniqueness
            val compositeId = "${savedDoctor.patientId}_${savedDoctor.doctorId}"
            
            savedDoctorsCollection.document(compositeId).set(savedDoctor).await()
            
            Result.success(compositeId)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error saving doctor", e)
            Result.failure(e)
        }
    }
    
    /**
     * Get all saved doctors for a patient
     */
    suspend fun getSavedDoctors(patientId: String): Result<List<SavedDoctor>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = savedDoctorsCollection
                .whereEqualTo("patientId", patientId)
                .get()
                .await()
            
            val savedDoctors = snapshot.documents.mapNotNull { doc ->
                doc.toObject(SavedDoctor::class.java)
            }
            
            Result.success(savedDoctors)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error getting saved doctors", e)
            Result.failure(e)
        }
    }
    
    /**
     * Remove a doctor from the patient's list
     */
    suspend fun removeDoctor(patientId: String, doctorId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val compositeId = "${patientId}_${doctorId}"
            
            savedDoctorsCollection.document(compositeId).delete().await()
            
            Result.success(true)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error removing doctor", e)
            Result.failure(e)
        }
    }
    
    /**
     * Update share count for a doctor
     */
    suspend fun updateDoctorShareCount(patientId: String, doctorId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val compositeId = "${patientId}_${doctorId}"
            
            savedDoctorsCollection.document(compositeId)
                .update(
                    "lastShared", System.currentTimeMillis(),
                    "shareCount", com.google.firebase.firestore.FieldValue.increment(1)
                )
                .await()
            
            Result.success(true)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error updating share count", e)
            Result.failure(e)
        }
    }
    
    /**
     * Send analysis to multiple doctors
     */
    suspend fun sendAnalysisToMultipleDoctors(
        analysisId: String,
        doctorEmails: List<String>,
        analysisText: String
    ): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Update the analysis with the list of doctors it was sent to
            analysisCollection.document(analysisId)
                .update(
                    "sentToDoctorEmails", doctorEmails,
                    "sentAt", Date()
                )
                .await()
            
            // In a real app, you would send emails to all doctors
            // For now, we'll just simulate success
            
            Result.success(true)
        } catch (e: Exception) {
            Log.e("MedicalAnalysisRepo", "Error sending to multiple doctors", e)
            Result.failure(e)
        }
    }
} 