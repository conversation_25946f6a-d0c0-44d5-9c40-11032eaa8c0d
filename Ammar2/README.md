# Ammar2 - Multi-purpose Text & Image Processing App

## Overview

Ammar2 is a versatile Android application that combines messaging capabilities with powerful text and image processing features. The app leverages Firebase for backend services and Google's Gemini AI for advanced text analysis and image processing.

## Features

### 1. Messaging System

#### User Authentication
- **Anonymous Sign-in**: Users can access the app without creating an account
- **User ID Display**: Each user has a unique ID displayed on the main screen
- **Online/Offline Status**: Banner indicates connection status with retry option

#### Messaging
- **Send Messages**: Send text messages to other users by their User ID
- **Inbox**: View all received messages with read/unread status
- **Recent Messages**: Quick view of latest unread messages on the main screen
- **Contacts**: Save and manage frequently messaged users

### 2. Text Rephraser

The Text Rephraser uses Gemini AI to improve and formalize your text.

#### How to Use:
1. Navigate to the Text Rephraser screen from the main menu
2. Enter your text in the input field
3. Click "Rephrase Text" to process
4. View the rephrased result
5. Share the result via:
   - Other apps (Whats<PERSON><PERSON>, Email, etc.)
   - PC via QR code sync

#### Features:
- Maintains original meaning while improving language and structure
- Makes text more formal, clear, and professional
- Preserves important information while enhancing readability

### 3. Image Analysis

The Image Analysis feature extracts and analyzes text from images.

#### How to Use:
1. Navigate to the Image Analysis screen from the main menu
2. Upload images (supported formats: JPG, PNG, PDF)
3. Wait for the AI to extract and analyze text
4. View the extracted text and analysis
5. Share the results via:
   - Other apps
   - PC via QR code sync

#### Features:
- Multi-image support
- PDF document support
- AI-powered text extraction
- Comprehensive text analysis

### 4. Medical Report Analyzer

A specialized version of the image analyzer focused on medical documents.

#### How to Use:
1. Upload medical reports, lab results, or radiology images
2. The AI extracts text and provides medical analysis including:
   - Summary of patient's condition
   - Critical findings (highlighted with 🚨)
   - Key metrics with normal ranges
   - Potential diagnoses
   - Recommendations for next steps

#### Features:
- Medical-specific AI analysis
- Critical findings highlighting
- Structured medical report format
- Doctor sharing capabilities

### 5. Image Extractor

Extract text from images shared from other apps or captured with the camera.

#### How to Use:
1. Click "Image Extractor" from the main menu, or
2. Share an image from another app to Ammar2
3. Take a photo or select an existing image
4. View the extracted text
5. Share the text to PC or other apps

#### Features:
- Camera integration
- Share intent handling from other apps
- Quick text extraction
- Multiple sharing options

### 6. PC Sync Tool

Sync extracted text and analyses to your PC using a QR code-based authentication system.

#### How to Use:
1. Run the PC sync tool on your computer
2. Click "Share to PC" or "Connect to PC" in the app
3. Scan the QR code displayed on your PC
4. Your text will automatically sync to the PC

#### Features:
- Secure QR code authentication
- Automatic text synchronization
- Organized file storage on PC
- Expiration time management for temporary texts

## Technical Details

### Firebase Integration
- **Authentication**: User management and authentication
- **Firestore**: Database for messages, extracted texts, and analyses
- **Storage**: Image and file storage

### AI Capabilities
- **Gemini AI**: Powers text rephrasing and image analysis
- **Text Extraction**: OCR capabilities for images and documents
- **Medical Analysis**: Specialized AI for medical document interpretation

### Data Management
- **Temporary Storage**: Regular extracted texts expire after one hour
- **Permanent Storage**: Medical analyses are stored permanently
- **User Data**: Messages and contacts are tied to user accounts

## Privacy and Security

- **Anonymous Usage**: No personal information required
- **Data Expiration**: Temporary data automatically expires
- **Secure Sync**: QR code-based authentication for PC sync
- **Firebase Security Rules**: Strict access controls for user data

## Getting Started

1. Install the app on your Android device
2. For PC sync, install the sync tool on your computer
3. Sign in (anonymous sign-in is available)
4. Explore the features from the main menu

## System Requirements

- Android 8.0 (API level 26) or higher
- Internet connection for most features
- Camera access for image capture and QR scanning
- Storage permissions for file access 