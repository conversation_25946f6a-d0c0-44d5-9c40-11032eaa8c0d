package io.ammar.medical

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import com.google.android.gms.tasks.Tasks
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.messaging.FirebaseMessaging
import io.ammar.medical.utils.FirebaseUtils
import io.ammar.medical.utils.NetworkUtils
import io.ammar.medical.utils.OfflineModeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.withTimeoutOrNull

class AmmarApplication : Application() {
    private val TAG = "AmmarApplication"
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Network state
    private val _isNetworkConnected = MutableStateFlow(false)
    val isNetworkConnected: StateFlow<Boolean> = _isNetworkConnected
    
    // Firebase state
    private val _isFirebaseReady = MutableStateFlow(false)
    val isFirebaseReady: StateFlow<Boolean> = _isFirebaseReady
    
    // Offline mode manager
    lateinit var offlineModeManager: OfflineModeManager
        private set
    
    // Retry counter to prevent infinite retries
    private val retryCounter = AtomicInteger(0)
    private val MAX_RETRIES = 5
    
    // Retry delay in milliseconds (with exponential backoff)
    private val baseRetryDelay = 1000L

    override fun onCreate() {
        super.onCreate()
        
        // Initialize the offline mode manager
        offlineModeManager = OfflineModeManager(this, applicationScope)
        
        setupNetworkCallback()
        initializeFirebase()
    }
    
    private fun setupNetworkCallback() {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        // Check current network state with a more reliable method
        val isConnected = NetworkUtils.isNetworkAvailable(this)
        _isNetworkConnected.value = isConnected
        Log.d(TAG, "Initial network state: ${if (isConnected) "Connected" else "Disconnected"}")
        
        // Register for network callbacks
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                val wasConnected = _isNetworkConnected.value
                _isNetworkConnected.value = true
                Log.d(TAG, "Network became available")
                
                // Only retry connections if we were previously disconnected
                if (!wasConnected) {
                    Log.d(TAG, "Network state changed from disconnected to connected, retrying Firebase connections")
                    applicationScope.launch {
                        // Reset retry counter when network becomes available
                        retryCounter.set(0)
                        retryFirebaseConnections()
                    }
                }
            }
            
            override fun onLost(network: Network) {
                _isNetworkConnected.value = false
                Log.d(TAG, "Network connection lost")
            }
            
            override fun onCapabilitiesChanged(network: Network, capabilities: NetworkCapabilities) {
                val hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                val previousState = _isNetworkConnected.value
                _isNetworkConnected.value = hasInternet
                
                if (hasInternet && !previousState) {
                    Log.d(TAG, "Internet capability became available, retrying Firebase connections")
                    applicationScope.launch {
                        // Reset retry counter when network becomes available
                        retryCounter.set(0)
                        retryFirebaseConnections()
                    }
                } else if (!hasInternet && previousState) {
                    Log.d(TAG, "Internet capability lost")
                    // Don't immediately set Firebase as not ready - it might still work offline
                }
            }
        }
        
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }
    
    private suspend fun retryFirebaseConnections() {
        if (!_isNetworkConnected.value) {
            Log.d(TAG, "Cannot retry Firebase connections - no network")
            return
        }
        
        // Check if we've exceeded max retries
        val currentRetry = retryCounter.incrementAndGet()
        if (currentRetry > MAX_RETRIES) {
            Log.w(TAG, "Maximum retry attempts ($MAX_RETRIES) reached, giving up")
            return
        }
        
        // Calculate exponential backoff delay
        val delayMs = baseRetryDelay * (1 shl (currentRetry - 1))
        Log.d(TAG, "Retrying Firebase connections (attempt $currentRetry) after $delayMs ms delay")
        
        // Add delay with exponential backoff
        delay(delayMs)
        
        Log.d(TAG, "Executing Firebase connection retry")
        
        // Run a full Firebase connectivity check
        try {
            val connectivityResult = FirebaseUtils.checkFirebaseConnectivity(this@AmmarApplication)
            Log.d(TAG, "Firebase connectivity check results:")
            Log.d(TAG, connectivityResult.getDetailedReport())
            
            if (!connectivityResult.isFullyConnected) {
                Log.w(TAG, "Firebase is not fully connected. Attempting to reinitialize...")
                // Reinitialize Firebase if needed
                if (FirebaseApp.getApps(this@AmmarApplication).isEmpty()) {
                    FirebaseApp.initializeApp(this@AmmarApplication)
                    Log.d(TAG, "Firebase core reinitialized")
                }
                
                // Configure Firestore again
                configureFirestore()
                
                // If we can at least operate offline, consider Firebase ready enough
                _isFirebaseReady.value = connectivityResult.canOperateOffline
                
                // If we have Auth but not Firestore, we can still consider the app partially ready
                if (connectivityResult.isAuthConnected && !connectivityResult.isFirestoreConnected) {
                    Log.d(TAG, "Auth is connected but Firestore is not. App can operate with limited functionality.")
                    _isFirebaseReady.value = true
                }
                
                // Let the offline mode manager know about the connection status
                offlineModeManager.retryConnection()
            } else {
                _isFirebaseReady.value = true
                Log.d(TAG, "Firebase is fully connected")
                
                // Let the offline mode manager know about the connection status
                offlineModeManager.retryConnection()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Firebase connectivity", e)
            _isFirebaseReady.value = false
            
            // Schedule another retry if we haven't exceeded max retries
            if (currentRetry < MAX_RETRIES) {
                Log.d(TAG, "Scheduling another retry attempt")
                applicationScope.launch {
                    retryFirebaseConnections()
                }
            }
        }
        
        // Test Firestore connection
        try {
            val db = FirebaseFirestore.getInstance()
            withContext(Dispatchers.IO) {
                try {
                    val result = db.collection("users").document("test")
                        .get()
                        .addOnSuccessListener { 
                            Log.d(TAG, "Firestore connection retry successful")
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Firestore connection retry failed", e)
                        }
                } catch (e: Exception) {
                    Log.e(TAG, "Error testing Firestore connection", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Firestore instance", e)
        }
        
        // Retry FCM token retrieval
        applicationScope.launch(Dispatchers.IO) {
            try {
                withTimeoutOrNull(10000) { // 10 seconds timeout
                    val token = Tasks.await(FirebaseMessaging.getInstance().token)
                    Log.d(TAG, "FCM Token retrieved successfully")
                    // Store the token in the user's document
                    storeTokenInFirestore(token)
                } ?: Log.w(TAG, "FCM token retrieval timed out")
            } catch (e: Exception) {
                // FCM token retrieval failure is not critical for app functionality
                // Don't show error to user for this
                Log.w(TAG, "FCM token retrieval failed", e)
                Log.d(TAG, "Network connected: ${_isNetworkConnected.value}")
            }
        }
    }
    
    private fun configureFirestore() {
        try {
            // Configure Firestore for offline persistence with more aggressive settings
            val settings = FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(true)
                .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                .build()
            
            FirebaseFirestore.getInstance().firestoreSettings = settings
            Log.d(TAG, "Firestore offline persistence configured with unlimited cache size")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure Firestore offline persistence", e)
        }
    }
    
    private fun initializeFirebase() {
        try {
            // Initialize Firebase
            if (FirebaseApp.getApps(this).isEmpty()) {
                FirebaseApp.initializeApp(this)
                Log.d(TAG, "Firebase core initialized")
            }
            
            // Check network connectivity first
            if (!_isNetworkConnected.value) {
                Log.w(TAG, "Network unavailable - Firebase services may not connect properly")
            }
            
            // Configure Firestore for offline persistence
            configureFirestore()
            
            // Set Firebase as ready for offline operations by default
            // This prevents showing unnecessary error banners during startup
            _isFirebaseReady.value = true
            
            // Run a full Firebase connectivity check
            applicationScope.launch {
                try {
                    val connectivityResult = FirebaseUtils.checkFirebaseConnectivity(this@AmmarApplication)
                    Log.d(TAG, "Initial Firebase connectivity check results:")
                    Log.d(TAG, connectivityResult.getDetailedReport())
                    
                    _isFirebaseReady.value = connectivityResult.isFullyConnected || connectivityResult.canOperateOffline
                    
                    // If we're not fully connected, try to retrieve FCM token anyway
                    if (!connectivityResult.isFullyConnected && _isNetworkConnected.value) {
                        Log.d(TAG, "Firebase is not fully connected but network is available. Scheduling retry...")
                        // Reset retry counter
                        retryCounter.set(0)
                        // Schedule retry
                        retryFirebaseConnections()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error during initial Firebase connectivity check", e)
                    // Keep Firebase as ready for offline operations
                    _isFirebaseReady.value = true
                }
            }
            
            // Try to retrieve FCM token
            applicationScope.launch(Dispatchers.IO) {
                try {
                    withTimeoutOrNull(10000) { // 10 seconds timeout
                        val token = Tasks.await(FirebaseMessaging.getInstance().token)
                        Log.d(TAG, "FCM Token retrieved successfully")
                        // Store the token in the user's document
                        storeTokenInFirestore(token)
                    } ?: Log.w(TAG, "FCM token retrieval timed out")
                } catch (e: Exception) {
                    // FCM token retrieval failure is not critical for app functionality
                    Log.w(TAG, "FCM token retrieval failed", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase", e)
            _isFirebaseReady.value = false
        }
    }
    
    private suspend fun storeTokenInFirestore(token: String) {
        try {
            val auth = FirebaseAuth.getInstance()
            val currentUser = auth.currentUser
            
            if (currentUser != null) {
                val db = FirebaseFirestore.getInstance()
                withContext(Dispatchers.IO) {
                    try {
                        db.collection("users").document(currentUser.uid)
                            .update("fcmToken", token)
                            .addOnSuccessListener {
                                Log.d(TAG, "FCM token stored in Firestore")
                            }
                            .addOnFailureListener { e ->
                                Log.e(TAG, "Failed to store FCM token in Firestore", e)
                                
                                // If update fails, try to create the document
                                if (e.message?.contains("NOT_FOUND") == true) {
                                    Log.d(TAG, "User document not found, creating new document")
                                    val userData = hashMapOf(
                                        "fcmToken" to token,
                                        "lastLogin" to System.currentTimeMillis()
                                    )
                                    
                                    db.collection("users").document(currentUser.uid)
                                        .set(userData)
                                        .addOnSuccessListener {
                                            Log.d(TAG, "Created new user document with FCM token")
                                        }
                                        .addOnFailureListener { innerE ->
                                            Log.e(TAG, "Failed to create user document", innerE)
                                        }
                                }
                            }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error storing FCM token", e)
                    }
                }
            } else {
                Log.w(TAG, "Cannot store FCM token - no authenticated user")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in storeTokenInFirestore", e)
        }
    }
    
    companion object {
        // Helper function to get the OfflineModeManager from any context
        fun getOfflineModeManager(context: Context): OfflineModeManager {
            return (context.applicationContext as AmmarApplication).offlineModeManager
        }
    }
} 