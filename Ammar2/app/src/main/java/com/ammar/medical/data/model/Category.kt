package com.ammar.medical.data.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

data class Category(
    val id: Long,
    val name: String,
    val color: Long,
    val icon: CategoryIcon = CategoryIcon.FOLDER
) {
    enum class CategoryIcon(val icon: ImageVector) {
        FOLDER(Icons.Default.Folder),
        MEDICAL(Icons.Default.LocalHospital),
        LAB(Icons.Default.Science),
        PRESCRIPTION(Icons.Default.Receipt),
        RADIOLOGY(Icons.Default.Image),
        DENTAL(Icons.Default.Person),
        VISION(Icons.Default.RemoveRedEye),
        VACCINATION(Icons.Default.Vaccines),
        EMERGENCY(Icons.Default.Emergency),
        INSURANCE(Icons.Default.AccountBalance),
        OTHER(Icons.Default.FolderOpen)
    }

    companion object {
        val DEFAULT = Category(
            id = 0,
            name = "Uncategorized",
            color = 0xFF9E9E9E,
            icon = CategoryIcon.FOLDER
        )

        val PREDEFINED = listOf(
            Category(1, "Medical Reports", 0xFF2196F3, CategoryIcon.MEDICAL),
            Category(2, "Lab Results", 0xFF4CAF50, CategoryIcon.LAB),
            Category(3, "Prescriptions", 0xFFFFC107, CategoryIcon.PRESCRIPTION),
            Category(4, "Radiology", 0xFF9C27B0, CategoryIcon.RADIOLOGY),
            Category(5, "Dental Records", 0xFF00BCD4, CategoryIcon.DENTAL),
            Category(6, "Vision Records", 0xFF3F51B5, CategoryIcon.VISION),
            Category(7, "Vaccinations", 0xFF8BC34A, CategoryIcon.VACCINATION),
            Category(8, "Emergency Care", 0xFFF44336, CategoryIcon.EMERGENCY),
            Category(9, "Insurance", 0xFF795548, CategoryIcon.INSURANCE)
        )
    }
} 