package com.ammar.medical.presentation.ui.screens.scanner

import android.graphics.ImageFormat
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.view.PreviewView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ammar.medical.camera.CameraManager
import com.ammar.medical.camera.CameraState
import com.ammar.medical.domain.service.QrCodeService
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class QrScannerViewModel @Inject constructor(
    private val cameraManager: CameraManager,
    private val qrCodeService: QrCodeService
) : ViewModel() {

    private val scanner: BarcodeScanner = BarcodeScanning.getClient(
        BarcodeScannerOptions.Builder()
            .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
            .build()
    )

    val cameraState: StateFlow<CameraState> = cameraManager.cameraState

    private val _scanState = MutableStateFlow<QrScanState>(QrScanState.Scanning)
    val scanState: StateFlow<QrScanState> = _scanState.asStateFlow()

    fun startCamera(
        lifecycleOwner: LifecycleOwner,
        preview: PreviewView
    ) {
        cameraManager.startCamera(
            lifecycleOwner = lifecycleOwner,
            preview = preview,
            onImageCaptured = { /* Not used for QR scanning */ },
            onError = { /* Handle in cameraState */ }
        )

        setupImageAnalysis()
    }

    private fun setupImageAnalysis() {
        val imageAnalysis = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()

        imageAnalysis.setAnalyzer(
            cameraManager.cameraExecutor,
            QrCodeAnalyzer { barcodes ->
                processQrCodes(barcodes)
            }
        )
    }

    private fun processQrCodes(barcodes: List<Barcode>) {
        viewModelScope.launch {
            if (barcodes.isEmpty()) {
                _scanState.value = QrScanState.Scanning
                return@launch
            }

            val qrCode = barcodes.firstOrNull { barcode ->
                barcode.valueType == Barcode.TYPE_TEXT
            }

            qrCode?.rawValue?.let { content ->
                qrCodeService.parseDocumentQrCode(content)?.let { documentId ->
                    _scanState.value = QrScanState.Success(documentId)
                } ?: run {
                    _scanState.value = QrScanState.Error("Invalid QR code format")
                }
            }
        }
    }

    private inner class QrCodeAnalyzer(
        private val onQrCodesDetected: (List<Barcode>) -> Unit
    ) : ImageAnalysis.Analyzer {
        override fun analyze(image: ImageProxy) {
            if (image.format != ImageFormat.YUV_420_888) {
                image.close()
                return
            }

            val mediaImage = image.image
            if (mediaImage != null) {
                val inputImage = InputImage.fromMediaImage(
                    mediaImage,
                    image.imageInfo.rotationDegrees
                )

                scanner.process(inputImage)
                    .addOnSuccessListener { barcodes ->
                        onQrCodesDetected(barcodes)
                    }
                    .addOnFailureListener { exception ->
                        _scanState.value = QrScanState.Error(exception.message ?: "Failed to scan QR code")
                    }
                    .addOnCompleteListener {
                        image.close()
                    }
            } else {
                image.close()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        scanner.close()
    }
}

sealed class QrScanState {
    object Scanning : QrScanState()
    data class Success(val documentId: Long) : QrScanState()
    data class Error(val message: String) : QrScanState()
} 