const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.sendMessageNotification = functions.firestore
    .document('messages/{messageId}')
    .onCreate(async (snapshot, context) => {
        const messageData = snapshot.data();
        const recipientUid = messageData.recipientUid;
        const senderUid = messageData.senderUid;
        const messageContent = messageData.message;
        
        // Don't send notifications for messages to yourself
        if (recipientUid === senderUid) {
            console.log('Skipping notification for self-message');
            return null;
        }
        
        try {
            // Get recipient's FCM token
            const recipientDoc = await admin.firestore()
                .collection('users')
                .doc(recipientUid)
                .get();
                
            if (!recipientDoc.exists) {
                console.log('Recipient does not exist');
                return null;
            }
            
            const recipientData = recipientDoc.data();
            const fcmToken = recipientData.fcmToken;
            
            if (!fcmToken) {
                console.log('Recipient has no FCM token');
                return null;
            }
            
            // Get sender's username
            const senderDoc = await admin.firestore()
                .collection('users')
                .doc(senderUid)
                .get();
                
            let senderName = "Someone";
            if (senderDoc.exists) {
                senderName = senderDoc.data().username || "Someone";
            }
            
            // Truncate message if too long
            const shortMessage = messageContent.length > 100 
                ? messageContent.substring(0, 97) + '...' 
                : messageContent;
            
            // Create notification
            const payload = {
                notification: {
                    title: `New Message from ${senderName}`,
                    body: shortMessage,
                    clickAction: 'OPEN_INBOX',
                    sound: 'default'
                },
                data: {
                    messageId: context.params.messageId,
                    senderUid: senderUid,
                    senderName: senderName,
                    messageContent: shortMessage,
                    type: 'new_message'
                }
            };
            
            // Send notification
            const response = await admin.messaging().sendToDevice(fcmToken, payload);
            
            console.log('Notification sent successfully:', response);
            return { success: true };
        } catch (error) {
            console.error('Error sending notification:', error);
            return { success: false, error: error.message };
        }
    }); 