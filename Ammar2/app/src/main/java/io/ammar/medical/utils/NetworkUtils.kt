package io.ammar.medical.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.withContext
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.TimeUnit

/**
 * Utility class for network-related operations
 */
object NetworkUtils {
    private const val TAG = "NetworkUtils"
    
    /**
     * Checks if the device currently has an active internet connection
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
    
    /**
     * Returns a Flow that emits network connectivity status changes
     */
    fun observeNetworkStatus(context: Context): Flow<Boolean> = callbackFlow {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        // Check initial state
        val initialState = isNetworkAvailable(context)
        trySend(initialState)
        
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                Log.d(TAG, "Network became available")
                trySend(true)
            }
            
            override fun onLost(network: Network) {
                Log.d(TAG, "Network connection lost")
                trySend(false)
            }
            
            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities
            ) {
                val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                Log.d(TAG, "Network capabilities changed, has internet: $hasInternet")
                trySend(hasInternet)
            }
        }
        
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, callback)
        
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.distinctUntilChanged()
    
    /**
     * Checks if the device can reach a specific host
     * Note: This should be called from a background thread
     */
    fun isHostReachable(host: String): Boolean {
        return try {
            val runtime = Runtime.getRuntime()
            val ipProcess = runtime.exec("/system/bin/ping -c 1 $host")
            val exitValue = ipProcess.waitFor()
            exitValue == 0
        } catch (e: Exception) {
            Log.e(TAG, "Error checking host reachability: $host", e)
            false
        }
    }
    
    /**
     * Checks if Firebase hosts are reachable
     * Returns a pair of (firestore reachable, auth reachable)
     */
    suspend fun checkFirebaseHostsReachable(): Pair<Boolean, Boolean> = withContext(Dispatchers.IO) {
        val firestoreReachable = isFirebaseHostReachable("firestore.googleapis.com")
        val authReachable = isFirebaseHostReachable("www.googleapis.com")
        return@withContext Pair(firestoreReachable, authReachable)
    }
    
    /**
     * Checks if a Firebase host is reachable with a HTTP request
     */
    private fun isFirebaseHostReachable(host: String): Boolean {
        return try {
            val url = URL("https://$host")
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = TimeUnit.SECONDS.toMillis(5).toInt()
            connection.readTimeout = TimeUnit.SECONDS.toMillis(5).toInt()
            connection.requestMethod = "HEAD"
            
            val responseCode = connection.responseCode
            Log.d(TAG, "Host $host response code: $responseCode")
            
            responseCode in 200..399
        } catch (e: IOException) {
            Log.e(TAG, "Error checking Firebase host reachability: $host", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error checking Firebase host: $host", e)
            false
        }
    }
} 