import base64
import io
import json
import tkinter as tk
from tkinter import ttk
import pyautogui
from PIL import Image, ImageTk
import requests
from datetime import datetime
import telebot
import threading
import re
import webbrowser
import tkinter.filedialog as filedialog
from tkinter import messagebox
import os

class ResponseWindow:
    def __init__(self, parent, response_text):
        self.window = tk.Toplevel(parent)
        self.window.title("Gemini Response")
        self.window.geometry("800x600")
        
        self.window.resizable(True, True)
        
        self.text_frame = ttk.Frame(self.window)
        self.text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.text_widget = tk.Text(self.text_frame, wrap=tk.WORD, font=('TkDefaultFont', 11))
        
        y_scrollbar = ttk.Scrollbar(self.text_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        x_scrollbar = ttk.Scrollbar(self.text_frame, orient=tk.HORIZONTAL, command=self.text_widget.xview)
        
        self.text_widget.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)
        
        self.text_widget.grid(row=0, column=0, sticky="nsew")
        y_scrollbar.grid(row=0, column=1, sticky="ns")
        x_scrollbar.grid(row=1, column=0, sticky="ew")
        
        self.text_frame.grid_rowconfigure(0, weight=1)
        self.text_frame.grid_columnconfigure(0, weight=1)
        
        self.text_widget.insert('1.0', response_text)
        
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="Copy to Clipboard", 
                  command=self.copy_to_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def copy_to_clipboard(self):
        text = self.text_widget.get('1.0', tk.END)
        self.window.clipboard_clear()
        self.window.clipboard_append(text)

class PreviewImage:
    def __init__(self, parent, screenshot, index):
        self.window = tk.Toplevel(parent)
        self.window.title(f"Screenshot Preview {index + 1}")
        
        self.canvas = tk.Canvas(self.window)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        h_scroll = ttk.Scrollbar(self.window, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scroll = ttk.Scrollbar(self.window, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)
        
        h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.image = ImageTk.PhotoImage(screenshot)
        
        self.canvas.create_image(0, 0, image=self.image, anchor="nw")
        
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

class ScreenshotApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Screenshot to Gemini")
        
        self.root.resizable(True, True)
        self.root.geometry("800x800")
        
        # Initialize variables first
        self.default_save_dir = os.path.expanduser("~/Documents")
        self.last_used_file = None
        self.include_screenshots = tk.BooleanVar(value=False)
        
        # Initialize with empty values - will be set from chat description
        self.GEMINI_API_KEY = ''
        self.GEMINI_MODEL = ''
        self.GEMINI_ENDPOINT = ''
        self.TELEGRAM_BOT_TOKEN = '**********************************************'
        
        print("\n=== Initial Gemini Configuration ===")
        print("Waiting for configuration from chat description...")
        print("=================================\n")
        
        self.screenshots = []
        self.selection_window = None
        self.start_x = None
        self.start_y = None
        self.current_rectangle = None
        self.preview_images = []
        
        self.activation_passwords = set()
        self.bot = telebot.TeleBot(self.TELEGRAM_BOT_TOKEN)
        self.setup_telegram_bot()
        
        self.bot_thread = threading.Thread(target=self.run_telegram_bot, daemon=True)
        self.bot_thread.start()
        
        self.setup_ui()
        
        self.updating_passwords = True
        self.update_passwords()
        
        # Print configuration after setup is complete
        print("\n=== Final Gemini Configuration after Setup ===")
        print(f"Final API Key: {self.GEMINI_API_KEY}")
        print(f"Final Model: {self.GEMINI_MODEL}")
        print(f"Final Endpoint: {self.GEMINI_ENDPOINT}")
        print("=========================================\n")

    def setup_telegram_bot(self):
        @self.bot.message_handler(commands=['start'])
        def handle_start(message):
            self.bot.reply_to(message, "Bot is running. Password will be fetched from chat description.")
            self.fetch_bot_info()

    def fetch_bot_info(self):
        try:
            bot_info = self.bot.get_me()
            print("\n=== Bot Information ===")
            print("Bot ID:", bot_info.id)
            print("Bot Name:", bot_info.first_name)
            print("Bot Username:", bot_info.username)

            chat_id = '@geminipassword'
            chat_info = self.bot.get_chat(chat_id)

            print("\n=== Chat Information ===")
            print("Chat Title:", chat_info.title)
            print("Chat Description Raw:", chat_info.description)

            if chat_info.description:
                # Get passwords
                passwords = re.findall(r'\[PASS:(.*?)\]', chat_info.description)
                self.activation_passwords.update(set(passwords))
                print("\n=== Retrieved Data ===")
                print(f"Passwords found: {passwords}")
                
                # Get model name and construct endpoint
                model_match = re.search(r'\[MODEL:(.*?)\]', chat_info.description)
                if model_match:
                    self.GEMINI_MODEL = model_match.group(1).strip()
                    self.GEMINI_ENDPOINT = f'https://generativelanguage.googleapis.com/v1beta/models/{self.GEMINI_MODEL}:generateContent'
                    print(f"Model string found: '{self.GEMINI_MODEL}'")
                
                # Get API key
                key_match = re.search(r'\[GKEY:(.*?)\]', chat_info.description)
                if key_match:
                    self.GEMINI_API_KEY = key_match.group(1).strip()
                    print("API key found")
                
                # Print final configuration
                print("\n=== Final Gemini Configuration ===")
                print(f"API Key: {self.GEMINI_API_KEY}")
                print(f"Model: {self.GEMINI_MODEL}")
                print(f"Endpoint: {self.GEMINI_ENDPOINT}")
                print("================================\n")

            else:
                print("Warning: Chat description is empty")
                raise Exception("Required Gemini configuration not found in chat description")

        except Exception as e:
            print(f"\nError fetching bot info: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            print("Full traceback:")
            print(traceback.format_exc())
            raise  # Re-raise the exception to handle it in the calling code

    def run_telegram_bot(self):
        try:
            self.bot.polling(none_stop=True)
        except Exception as e:
            print(f"Telegram bot error: {str(e)}")

    def update_passwords(self):
        if not self.updating_passwords:
            return

        try:
            chat_id = '@geminipassword'
            chat_info = self.bot.get_chat(chat_id)

            if chat_info.description:
                # Update passwords
                passwords = re.findall(r'\[PASS:(.*?)\]', chat_info.description)
                self.activation_passwords.update(set(passwords))
                
                # Update model name
                model_match = re.search(r'\[MODEL:(.*?)\]', chat_info.description)
                if model_match:
                    new_model = model_match.group(1).strip()
                    if new_model != self.GEMINI_MODEL:
                        self.GEMINI_MODEL = new_model
                        self.GEMINI_ENDPOINT = f'https://generativelanguage.googleapis.com/v1beta/models/{self.GEMINI_MODEL}:generateContent'
                        print(f"Model updated to: {self.GEMINI_MODEL}")
                        print(f"New endpoint: {self.GEMINI_ENDPOINT}")
                
                # Update API key
                key_match = re.search(r'\[GKEY:(.*?)\]', chat_info.description)
                if key_match:
                    new_key = key_match.group(1).strip()
                    if new_key != self.GEMINI_API_KEY:
                        self.GEMINI_API_KEY = new_key
                        print("API key updated")

            self.root.after(30000, self.update_passwords)

        except Exception as e:
            print(f"Error updating info: {str(e)}")
            self.root.after(60000, self.update_passwords)

    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        top_frame = ttk.Frame(main_frame)
        top_frame.grid(row=0, column=0, sticky="ew", pady=5)

        # Vertical button arrangement
        button_container = ttk.Frame(top_frame)
        button_container.pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # Password Entry Section
        ttk.Label(top_frame, text="Password:").pack(anchor="w", padx=5)
        self.activation_entry = ttk.Entry(top_frame, width=30, show="*")
        self.activation_entry.pack(anchor="w", padx=5)

        self.activate_button = ttk.Button(top_frame, text="Activate", command=self.check_activation)
        self.activate_button.pack(anchor="w", padx=5)

        # Screenshot Buttons in Vertical Layout
        self.screenshot_buttons = []

        full_screenshot_btn = ttk.Button(button_container, text="Full Screenshot", command=self.take_full_screenshot, state='disabled')
        full_screenshot_btn.pack(anchor="w", pady=2, fill=tk.X)
        self.screenshot_buttons.append(full_screenshot_btn)

        select_area_btn = ttk.Button(button_container, text="Select Area", command=self.start_area_selection, state='disabled')
        select_area_btn.pack(anchor="w", pady=2, fill=tk.X)
        self.screenshot_buttons.append(select_area_btn)

        send_btn = ttk.Button(button_container, text="Send to Gemini", command=self.send_to_gemini, state='disabled')
        send_btn.pack(anchor="w", pady=2, fill=tk.X)
        self.screenshot_buttons.append(send_btn)

        clear_btn = ttk.Button(button_container, text="Clear Screenshots", command=self.clear_screenshots, state='disabled')
        clear_btn.pack(anchor="w", pady=2, fill=tk.X)
        self.screenshot_buttons.append(clear_btn)

        # Add the update button after other buttons in the button_container
        update_btn = ttk.Button(button_container, text="Update", command=self.open_update_website)
        update_btn.pack(anchor="w", pady=2, fill=tk.X)
        self.screenshot_buttons.append(update_btn)

        # Always on Top checkbox and status label
        self.always_on_top = tk.BooleanVar(value=False)
        ttk.Checkbutton(top_frame, text="Always on Top", variable=self.always_on_top, command=self.toggle_always_on_top).pack(anchor="w", padx=5)

        self.status_var = tk.StringVar(value="Please activate with password")
        ttk.Label(top_frame, textvariable=self.status_var).pack(anchor="e", padx=5)
        
        preview_frame = ttk.LabelFrame(main_frame, text="Screenshots Preview (Click to Enlarge)", padding="5")
        preview_frame.grid(row=1, column=0, sticky="ew", pady=5)
        
        self.preview_canvas = tk.Canvas(preview_frame, height=150)
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, 
                                command=self.preview_canvas.xview)
        self.preview_canvas.configure(xscrollcommand=scrollbar.set)
        
        self.preview_canvas.grid(row=0, column=0, sticky="ew")
        scrollbar.grid(row=1, column=0, sticky="ew")
        
        preview_frame.grid_columnconfigure(0, weight=1)
        
        self.preview_canvas.bind("<Button-1>", self.on_preview_click)
        
        response_frame = ttk.LabelFrame(main_frame, text="Gemini Response", padding="5")
        response_frame.grid(row=2, column=0, sticky="nsew", pady=5)
        
        response_frame.grid_columnconfigure(0, weight=1)
        response_frame.grid_rowconfigure(0, weight=1)
        
        self.response_text = tk.Text(response_frame, wrap=tk.WORD, font=('TkDefaultFont', 11))
        y_scrollbar = ttk.Scrollbar(response_frame, orient=tk.VERTICAL, command=self.response_text.yview)
        x_scrollbar = ttk.Scrollbar(response_frame, orient=tk.HORIZONTAL, command=self.response_text.xview)
        
        self.response_text.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)
        
        self.response_text.grid(row=0, column=0, sticky="nsew")
        y_scrollbar.grid(row=0, column=1, sticky="ns")
        x_scrollbar.grid(row=1, column=0, sticky="ew")
        
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # Add buttons frame below the response text
        response_buttons_frame = ttk.Frame(response_frame)
        response_buttons_frame.grid(row=2, column=0, columnspan=2, pady=5, sticky="ew")
        
        ttk.Button(response_buttons_frame, 
                  text="Copy Response", 
                  command=self.copy_response).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(response_buttons_frame, 
                  text="Save to RTF", 
                  command=self.save_to_rtf).pack(side=tk.LEFT, padx=5)
        
        self.quick_save_btn = ttk.Button(response_buttons_frame, 
                                       text="Quick Save", 
                                       command=self.quick_save_to_rtf,
                                       state='disabled')
        self.quick_save_btn.pack(side=tk.LEFT, padx=5)
        
        # Add checkbox for including screenshots
        ttk.Checkbutton(response_buttons_frame, 
                       text="Include Screenshots", 
                       variable=self.include_screenshots).pack(side=tk.LEFT, padx=5)

    def check_activation(self):
        entered_password = self.activation_entry.get().strip()
        if entered_password in self.activation_passwords:
            self.status_var.set("Activation successful!")
            self.activation_entry.config(state='disabled')
            self.activate_button.config(state='disabled')
            
            for button in self.screenshot_buttons:
                button.config(state='normal')
        else:
            self.status_var.set("Invalid activation password")

    def start_area_selection(self):
        self.root.withdraw()
        
        self.selection_window = tk.Toplevel(self.root)
        self.selection_window.attributes('-fullscreen', True, '-alpha', 0.3)
        
        self.canvas = tk.Canvas(self.selection_window, highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)
        
        self.selection_window.bind("<Escape>", lambda e: self.cancel_selection())

    def start_selection(self, event):
        self.start_x = event.x
        self.start_y = event.y
        self.current_rectangle = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline="red", width=2
        )

    def update_selection(self, event):
        if self.current_rectangle:
            self.canvas.coords(self.current_rectangle,
                             self.start_x, self.start_y,
                             event.x, event.y)

    def end_selection(self, event):
        if self.current_rectangle:
            x1 = min(self.start_x, event.x)
            y1 = min(self.start_y, event.y)
            x2 = max(self.start_x, event.x)
            y2 = max(self.start_y, event.y)
            
            if x2 - x1 > 1 and y2 - y1 > 1:
                try:
                    screenshot = pyautogui.screenshot(region=(x1, y1, x2 - x1, y2 - y1))
                    self.screenshots.append(screenshot)
                    self.update_preview()
                except Exception as e:
                    self.status_var.set(f"Screenshot error: {str(e)}")
            
        self.selection_window.destroy()
        self.selection_window = None
        self.root.deiconify()

    def cancel_selection(self):
        if self.selection_window:
            self.selection_window.destroy()
            self.selection_window = None
        self.root.deiconify()

    def take_full_screenshot(self):
        try:
            self.root.withdraw()
            self.root.update()
            
            self.root.after(100)
            
            screenshot = pyautogui.screenshot()
            self.screenshots.append(screenshot)
            self.update_preview()
            
            self.status_var.set("Screenshot captured successfully")
        except Exception as e:
            self.status_var.set(f"Screenshot error: {str(e)}")
        finally:
            self.root.deiconify()

    def update_preview(self):
        self.preview_canvas.delete("all")
        self.preview_images.clear()
        
        total_width = len(self.screenshots) * 160
        self.preview_canvas.configure(scrollregion=(0, 0, total_width, 150))
        
        for index, screenshot in enumerate(self.screenshots):
            thumbnail = screenshot.copy()
            thumbnail.thumbnail((150, 100))
            
            photo = ImageTk.PhotoImage(thumbnail)
            self.preview_images.append(photo)
            
            self.preview_canvas.create_image(
                index * 160 + 75,
                50,
                image=photo,
                anchor="center",
                tags=(str(index),)
            )

    def on_preview_click(self, event):
        item = self.preview_canvas.find_closest(event.x, event.y)
        tags = self.preview_canvas.gettags(item)
        if tags:
            index = int(tags[0])
            PreviewImage(self.root, self.screenshots[index], index)

    def send_to_gemini(self):
        if not self.screenshots:
            self.status_var.set("No screenshots to send!")
            return

        try:
            # Prepare the request
            parts = []
            
            # Add text content if needed
            parts.append({
                "text": "Please analyze this screenshot and analyze the given medical file of patient"
            })
            
            # Add images
            for screenshot in self.screenshots:
                image_base64 = self.image_to_base64(screenshot)
                parts.append({
                    "inlineData": {
                        "mimeType": "image/png",
                        "data": image_base64
                    }   
                })  
            
            # Prepare the complete request body
            request_body = {
                "contents": [{
                    "parts": parts
                }],
                "generationConfig": {
                    "temperature": 0.4,
                    "topK": 32,
                    "topP": 1,
                    "maxOutputTokens": 4096,
                }
            }

            # Send request to Gemini
            headers = {
                'Content-Type': 'application/json',
            }
            
            # Use API key in the URL
            url = f"{self.GEMINI_ENDPOINT}?key={self.GEMINI_API_KEY}"
            response = requests.post(url, headers=headers, json=request_body)
            
            if response.status_code == 200:
                result = response.json()
                
                # Extract only the response text
                try:
                    response_text = result['candidates'][0]['content']['parts'][0]['text']
                    
                    # Update response area with only the text
                    self.response_text.delete('1.0', tk.END)
                    self.response_text.insert('1.0', response_text)
                    self.status_var.set("Successfully sent to Gemini!")
                except (KeyError, IndexError) as e:
                    self.status_var.set("Error: Could not parse Gemini response")
                    self.response_text.delete('1.0', tk.END)
                    self.response_text.insert('1.0', "Error: Could not extract response text from Gemini")
            else:
                self.status_var.set(f"Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            self.status_var.set(f"Error sending to Gemini: {str(e)}")

    def clear_screenshots(self):
        self.screenshots.clear()
        self.update_preview()
        self.status_var.set("Screenshots cleared")

    def toggle_always_on_top(self):
        self.root.attributes('-topmost', self.always_on_top.get())

    def image_to_base64(self, image):
        """Convert a PIL image to a base64 encoded string."""
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode()

    def open_update_website(self):
        """Opens the update website in the default web browser"""
        webbrowser.open("https://resverus.com/windowsai/")

    def copy_response(self):
        """Copy the Gemini response to clipboard"""
        response_text = self.response_text.get('1.0', tk.END).strip()
        if response_text:
            self.root.clipboard_clear()
            self.root.clipboard_append(response_text)
            self.status_var.set("Response copied to clipboard!")
        else:
            self.status_var.set("No response to copy!")

    def save_to_rtf(self):
        """Save the Gemini response to an RTF file"""
        response_text = self.response_text.get('1.0', tk.END).strip()
        if not response_text:
            messagebox.showwarning("No Content", "There is no response to save!")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"gemini_response_{timestamp}.rtf"
        
        filepath = filedialog.asksaveasfilename(
            initialdir=self.default_save_dir,
            initialfile=default_filename,
            defaultextension=".rtf",
            filetypes=[("Rich Text File", "*.rtf"), ("All Files", "*.*")]
        )
        
        if not filepath:
            return
        
        try:
            self._save_to_rtf_file(filepath, response_text)
            self.last_used_file = filepath  # Store the last used file
            self.quick_save_btn.config(state='normal')  # Enable quick save button
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Error saving file: {str(e)}")
            self.status_var.set("Error saving response!")

    def quick_save_to_rtf(self):
        """Quickly append the response to the last used RTF file"""
        if not self.last_used_file:
            messagebox.showwarning("No File", "Please use 'Save to RTF' first to create a file!")
            return
            
        response_text = self.response_text.get('1.0', tk.END).strip()
        if not response_text:
            messagebox.showwarning("No Content", "There is no response to save!")
            return
            
        try:
            self._save_to_rtf_file(self.last_used_file, response_text, append=True)
        except Exception as e:
            messagebox.showerror("Quick Save Error", f"Error saving to file: {str(e)}")
            self.status_var.set("Error during quick save!")

    def _save_to_rtf_file(self, filepath, content, append=False):
        """Helper method to handle RTF file saving logic"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_exists = os.path.exists(filepath)
        
        # Basic RTF header and footer
        rtf_header = "{\\rtf1\\ansi\\deff0 {\\fonttbl{\\f0 Times New Roman;}}\n"
        rtf_footer = "}"
        
        # Prepare the text content
        formatted_content = content.replace('\n', '\\par\n')
        
        # Prepare screenshots if enabled
        screenshots_content = ""
        if self.include_screenshots.get() and self.screenshots:
            screenshots_content = "\n\\par\\par --- Screenshots ---\\par\n"
            for i, screenshot in enumerate(self.screenshots, 1):
                try:
                    # Save screenshot to temporary file
                    temp_img_path = os.path.join(
                        os.path.dirname(filepath),
                        f"screenshot_{timestamp}_{i}.png"
                    )
                    screenshot.save(temp_img_path)
                    
                    # Add reference to the image in RTF
                    screenshots_content += f"\\par Screenshot {i}: {temp_img_path}\\par\n"
                    
                except Exception as e:
                    print(f"Error saving screenshot {i}: {str(e)}")
        
        if append and file_exists:
            # Read existing content
            with open(filepath, 'r', encoding='utf-8') as f:
                existing_content = f.read().rstrip(rtf_footer)
            
            # Append new content
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(existing_content)
                f.write('\n\\par\\par\n')  # Add two line breaks
                f.write(f'--- Response from {timestamp} ---\\par\n')
                f.write(formatted_content)
                f.write(screenshots_content)  # Add screenshots if enabled
                f.write(rtf_footer)
        else:
            # Create new file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(rtf_header)
                f.write(formatted_content)
                f.write(screenshots_content)  # Add screenshots if enabled
                f.write(rtf_footer)
        
        self.status_var.set(f"Response saved to {os.path.basename(filepath)}")
        self.default_save_dir = os.path.dirname(filepath)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = ScreenshotApp()
    app.run()