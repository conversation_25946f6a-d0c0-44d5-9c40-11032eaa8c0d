#!/usr/bin/env python3
"""
PC Text Receiver for SmartMed Android App

This script runs a Flask server with a tkinter GUI that:
1. Displays a QR code for the Android app to scan
2. Shows received text messages in a nice interface
3. Allows saving messages to a file
4. Provides server status and connection information

Requirements:
- Flask
- qrcode
- Pillow
- tkinter (included with most Python installations)

Optional:
- netifaces (for better network interface detection)

Install with: pip install flask qrcode[pil]
"""

import os
import sys
import socket
import json
import argparse
import threading
import webbrowser
from datetime import datetime
from io import BytesIO

try:
    import qrcode
    from flask import Flask, request, jsonify
    import tkinter as tk
    from tkinter import ttk, scrolledtext, messagebox, filedialog
    from PIL import Image, ImageTk
except ImportError:
    print("Required packages not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "flask", "qrcode[pil]"])
    import qrcode
    from flask import Flask, request, jsonify
    import tkinter as tk
    from tkinter import ttk, scrolledtext, messagebox, filedialog
    from PIL import Image, ImageTk

# Flask application
app = Flask(__name__)

# Storage for received texts
received_texts = []
last_message = None

def get_ip_address():
    """Get the local IP address of the machine"""
    # Try to use netifaces if available (better method)
    try:
        import netifaces
        interfaces = netifaces.interfaces()
        for interface in interfaces:
            # Skip loopback interface
            if interface.startswith('lo'):
                continue
            
            # Get addresses for this interface
            addresses = netifaces.ifaddresses(interface)
            if netifaces.AF_INET in addresses:
                for address in addresses[netifaces.AF_INET]:
                    ip = address['addr']
                    # Skip localhost and Docker IPs
                    if not ip.startswith('127.') and not ip.startswith('172.'):
                        return ip
    except ImportError:
        print("Note: 'netifaces' module not available. Using fallback method to detect IP.")
    except Exception as e:
        print(f"Error using netifaces: {e}")
    
    # Fallback method 1: Using socket
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Connect to a public DNS server to determine the local IP
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        print(f"Error using socket fallback: {e}")
    
    # Fallback method 2: Get all interfaces
    try:
        hostname = socket.gethostname()
        ip_list = socket.gethostbyname_ex(hostname)[2]
        for ip in ip_list:
            if not ip.startswith("127."):
                return ip
    except Exception as e:
        print(f"Error using hostname fallback: {e}")
    
    # Final fallback
    return "127.0.0.1"

@app.route('/send_text/<session_id>', methods=['POST'])
def receive_text(session_id):
    """Receive text from the Android app"""
    global last_message
    try:
        data = request.json
        if not data or 'text' not in data:
            return jsonify({"status": "error", "message": "No text provided"}), 400
        
        text = data['text']
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        message = {
            "text": text,
            "timestamp": timestamp,
            "session_id": session_id
        }
        
        # Store the received text
        received_texts.append(message)
        last_message = message
        
        # Signal the UI thread to update
        if hasattr(app, 'ui_callback'):
            app.ui_callback()
        
        return jsonify({"status": "success", "message": "Text received successfully"}), 200
    
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

def generate_qr_code(url, size=10):
    """Generate a QR code for the given URL, returning both a PIL Image and data for display"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=size,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)
    
    # Create an image from the QR Code
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Save the image
    img_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "qrcode.png")
    img.save(img_path)
    
    return img, img_path

class SmartMedReceiverApp:
    def __init__(self, root, ip_address, port, session_id):
        self.root = root
        self.root.title("SmartMed PC Receiver")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # Set app icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.ip_address = ip_address
        self.port = port
        self.session_id = session_id
        self.url = f"http://{ip_address}:{port}/send_text/{session_id}"
        
        # Create the server URL
        self.url_frame = ttk.LabelFrame(root, text="Server Connection")
        self.url_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.url_var = tk.StringVar(value=self.url)
        ttk.Label(self.url_frame, text="Server URL:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.url_entry = ttk.Entry(self.url_frame, textvariable=self.url_var, width=50, state='readonly')
        self.url_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Button to copy URL
        self.copy_btn = ttk.Button(self.url_frame, text="Copy URL", command=self.copy_url)
        self.copy_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Status indicator
        self.status_var = tk.StringVar(value="Server running")
        self.status_label = ttk.Label(self.url_frame, textvariable=self.status_var, 
                                      foreground='green', font=('Helvetica', 10, 'bold'))
        self.status_label.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky=tk.W)
        
        # Main content frame - split into QR and Message areas
        self.content_frame = ttk.Frame(root)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Left side - QR Code
        self.qr_frame = ttk.LabelFrame(self.content_frame, text="QR Code")
        self.qr_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # Generate and display QR code
        self.qr_img, self.qr_path = generate_qr_code(self.url, size=10)
        self.tk_img = ImageTk.PhotoImage(self.qr_img)
        
        self.qr_display = ttk.Label(self.qr_frame, image=self.tk_img)
        self.qr_display.pack(padx=20, pady=20)
        
        ttk.Label(self.qr_frame, text="Scan with SmartMed app", font=('Helvetica', 10)).pack(pady=5)
        
        # Right side - Messages
        self.msg_frame = ttk.LabelFrame(self.content_frame, text="Received Messages")
        self.msg_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Message display area
        self.msg_display = scrolledtext.ScrolledText(self.msg_frame, wrap=tk.WORD, width=60, height=20)
        self.msg_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.msg_display.config(state=tk.DISABLED)
        
        # Bottom action buttons
        self.action_frame = ttk.Frame(root)
        self.action_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.clear_btn = ttk.Button(self.action_frame, text="Clear Messages", command=self.clear_messages)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        self.save_btn = ttk.Button(self.action_frame, text="Save Messages", command=self.save_messages)
        self.save_btn.pack(side=tk.LEFT, padx=5)
        
        # Application controls
        self.control_frame = ttk.Frame(root)
        self.control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.quit_btn = ttk.Button(self.control_frame, text="Quit", command=self.close_app)
        self.quit_btn.pack(side=tk.RIGHT, padx=5)
        
        # Connect Flask to this UI
        app.ui_callback = self.update_messages
        
        # Style configuration
        self.style = ttk.Style()
        self.style.configure('TLabelframe', font=('Helvetica', 11, 'bold'))
        
        # Update messages initially
        self.update_messages()
        
        # Set up protocol for app closure
        self.root.protocol("WM_DELETE_WINDOW", self.close_app)

    def copy_url(self):
        """Copy the server URL to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(self.url)
        self.status_var.set("URL copied to clipboard")
        self.root.after(2000, lambda: self.status_var.set("Server running"))

    def update_messages(self):
        """Update the message display with received texts"""
        global last_message, received_texts
        
        # Update status to show connection
        if last_message:
            self.status_var.set(f"Last message received at {last_message['timestamp']}")
        
        # Update message display
        self.msg_display.config(state=tk.NORMAL)
        self.msg_display.delete(1.0, tk.END)
        
        if not received_texts:
            self.msg_display.insert(tk.END, "No messages received yet.\n")
        else:
            for i, message in enumerate(received_texts, 1):
                self.msg_display.insert(
                    tk.END, 
                    f"--- Message {i} ---\n"
                    f"Time: {message['timestamp']}\n"
                    f"Session: {message['session_id']}\n\n"
                    f"{message['text']}\n\n"
                    f"{'-' * 50}\n\n"
                )
        
        # Scroll to bottom to show latest message
        self.msg_display.see(tk.END)
        self.msg_display.config(state=tk.DISABLED)

    def clear_messages(self):
        """Clear all received messages"""
        global received_texts
        if messagebox.askyesno("Clear Messages", "Are you sure you want to clear all messages?"):
            received_texts = []
            self.update_messages()

    def save_messages(self):
        """Save received messages to a file"""
        if not received_texts:
            messagebox.showinfo("No Messages", "There are no messages to save.")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Save Messages"
        )
        
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as file:
                    file.write(f"SmartMed Messages - Exported {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    for i, message in enumerate(received_texts, 1):
                        file.write(
                            f"--- Message {i} ---\n"
                            f"Time: {message['timestamp']}\n"
                            f"Session: {message['session_id']}\n\n"
                            f"{message['text']}\n\n"
                            f"{'-' * 50}\n\n"
                        )
                        
                messagebox.showinfo("Success", f"Messages saved to {file_path}")
    except Exception as e:
                messagebox.showerror("Error", f"Error saving messages: {str(e)}")

    def close_app(self):
        """Handle application closure"""
        if messagebox.askyesno("Quit", "Are you sure you want to quit? The server will be stopped."):
            # Stop the Flask server
            os._exit(0)

def flask_thread(host, port):
    """Run Flask in a separate thread"""
    app.run(host=host, port=port, use_reloader=False)

def main():
    parser = argparse.ArgumentParser(description='SmartMed PC Text Receiver')
    parser.add_argument('-p', '--port', type=int, default=5000, help='Port to run the server on')
    parser.add_argument('-s', '--session', type=str, default='default', help='Session ID')
    parser.add_argument('-i', '--ip', type=str, help='Manually specify the IP address to use')
    args = parser.parse_args()
    
    port = args.port
    session_id = args.session
    
    # Get IP address (either manually specified or auto-detected)
    ip_address = args.ip if args.ip else get_ip_address()
    
    # Start Flask in a separate thread
    server_thread = threading.Thread(target=flask_thread, args=('0.0.0.0', port), daemon=True)
    server_thread.start()
    
    # Create and start the tkinter app
    root = tk.Tk()
    app = SmartMedReceiverApp(root, ip_address, port, session_id)
    root.mainloop()

if __name__ == '__main__':
    main()
