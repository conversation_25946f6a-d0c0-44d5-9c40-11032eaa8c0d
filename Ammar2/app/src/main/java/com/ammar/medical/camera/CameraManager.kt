package com.ammar.medical.camera

import android.content.Context
import android.net.Uri
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.*
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.ammar.medical.R
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CameraManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private var camera: Camera? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private var imageAnalyzer: ImageAnalysis? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var recording: Recording? = null

    val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    private val _cameraState = MutableStateFlow<CameraState>(CameraState.Initializing)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()

    init {
        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    fun startCamera(
        lifecycleOwner: LifecycleOwner,
        preview: PreviewView,
        onImageCaptured: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases(lifecycleOwner, preview, onImageCaptured, onError)
                _cameraState.value = CameraState.Preview
            } catch (e: Exception) {
                _cameraState.value = CameraState.Error(e.message ?: "Failed to start camera")
                onError(e.message ?: "Failed to start camera")
            }
        }, ContextCompat.getMainExecutor(context))
    }

    private fun bindCameraUseCases(
        lifecycleOwner: LifecycleOwner,
        preview: PreviewView,
        onImageCaptured: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        val previewUseCase = Preview.Builder()
            .build()
            .also {
                it.setSurfaceProvider(preview.surfaceProvider)
            }

        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .build()

        // Image analysis use case
        imageAnalyzer = ImageAnalysis.Builder()
            .setTargetRotation(preview.display.rotation)
            .build()
            .also {
                it.setAnalyzer(cameraExecutor) { image ->
                    // Handle image analysis here
                }
            }

        try {
            cameraProvider?.unbindAll()
            camera = cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                previewUseCase,
                imageCapture,
                imageAnalyzer
            )
        } catch (e: Exception) {
            _cameraState.value = CameraState.Error(e.message ?: "Failed to bind camera use cases")
            onError(e.message ?: "Failed to bind camera use cases")
        }
    }

    fun captureImage(
        onImageCaptured: (Uri) -> Unit,
        onError: (Exception) -> Unit
    ) {
        val imageCapture = imageCapture ?: return

        val photoFile = File(
            context.cacheDir,
            SimpleDateFormat("yyyy-MM-dd-HH-mm-ss", Locale.US)
                .format(System.currentTimeMillis()) + ".jpg"
        )

        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    output.savedUri?.let { uri ->
                        onImageCaptured(uri)
                    }
                }

                override fun onError(exc: ImageCaptureException) {
                    _cameraState.value = CameraState.Error(exc.message ?: "Failed to capture image")
                    onError(exc)
                }
            }
        )
    }

    fun startVideoRecording(
        onVideoCaptured: (Uri) -> Unit,
        onError: (Exception) -> Unit
    ) {
        val videoCapture = videoCapture ?: return

        val videoFile = File(
            context.cacheDir,
            SimpleDateFormat("yyyy-MM-dd-HH-mm-ss", Locale.US)
                .format(System.currentTimeMillis()) + ".mp4"
        )

        val outputOptions = VideoCapture.OutputFileOptions.Builder(videoFile).build()

        recording = videoCapture.output
            .prepareRecording(context, outputOptions)
            .start(ContextCompat.getMainExecutor(context)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        _cameraState.value = CameraState.Recording
                    }
                    is VideoRecordEvent.Finalize -> {
                        if (recordEvent.hasError()) {
                            _cameraState.value = CameraState.Error(recordEvent.cause?.message ?: "Failed to record video")
                            onError(recordEvent.cause)
                        } else {
                            recordEvent.outputResults.outputUri?.let { uri ->
                                onVideoCaptured(uri)
                            }
                        }
                    }
                }
            }
    }

    fun stopVideoRecording() {
        recording?.stop()
        recording = null
        _cameraState.value = CameraState.Preview
    }

    fun switchCamera() {
        // Implement camera switching logic
    }

    fun toggleTorch() {
        camera?.let {
            if (it.cameraInfo.hasFlashUnit()) {
                it.cameraControl.enableTorch(!(it.cameraInfo.torchState.value == TorchState.ON))
            }
        }
    }

    fun cleanup() {
        cameraProvider?.unbindAll()
        cameraExecutor.shutdown()
    }
}

sealed class CameraState {
    object Initializing : CameraState()
    object Preview : CameraState()
    object Recording : CameraState()
    data class Error(val message: String) : CameraState()
}

enum class FlashMode {
    ON, OFF, AUTO
} 