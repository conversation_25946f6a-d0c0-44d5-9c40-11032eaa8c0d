Nora Mosa
Optional tone and style instructions for the model
---------------------------- PROCESS STARTED (8295) for package com.example.ammar2 ----------------------------
2025-03-05 04:05:48.530  8295-8295  CompatChangeReporter    com.example.ammar2                   D  Compat change id reported: 242716250; UID 10209; state: ENABLED
2025-03-05 04:05:48.571  8295-8295  ziparchive              com.example.ammar2                   W  Unable to open '/data/data/com.example.ammar2/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-03-05 04:05:48.573  8295-8295  ziparchive              com.example.ammar2                   W  Unable to open '/data/data/com.example.ammar2/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-03-05 04:05:48.576  8295-8295  ziparchive              com.example.ammar2                   W  Unable to open '/data/app/~~JIPm4R_yvOjHek5opyLnrw==/com.example.ammar2-2-QBNk_xyOlWgR7jtSiRWg==/base.dm': No such file or directory
2025-03-05 04:05:48.576  8295-8295  ziparchive              com.example.ammar2                   W  Unable to open '/data/app/~~JIPm4R_yvOjHek5opyLnrw==/com.example.ammar2-2-QBNk_xyOlWgR7jtSiRWg==/base.dm': No such file or directory
2025-03-05 04:05:48.926  8295-8295  nativeloader            com.example.ammar2                   D  Configuring clns-7 for other apk /data/app/~~JIPm4R_yvOjHek5opyLnrw==/com.example.ammar2-2-QBNk_xyOlWgR7jtSiRWg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~JIPm4R_yvOjHek5opyLnrw==/com.example.ammar2-2-QBNk_xyOlWgR7jtSiRWg==/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.example.ammar2
2025-03-05 04:05:48.973  8295-8295  GraphicsEnvironment     com.example.ammar2                   V  Currently set values for:
2025-03-05 04:05:48.973  8295-8295  GraphicsEnvironment     com.example.ammar2                   V    angle_gl_driver_selection_pkgs=[]
2025-03-05 04:05:48.973  8295-8295  GraphicsEnvironment     com.example.ammar2                   V    angle_gl_driver_selection_values=[]
2025-03-05 04:05:48.973  8295-8295  GraphicsEnvironment     com.example.ammar2                   V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-03-05 04:05:48.974  8295-8295  GraphicsEnvironment     com.example.ammar2                   V  Neither updatable production driver nor prerelease driver is supported.
2025-03-05 04:05:49.167  8295-8295  FirebaseApp             com.example.ammar2                   I  Device unlocked: initializing all Firebase APIs for app [DEFAULT]
2025-03-05 04:05:49.326  8295-8295  CompatChangeReporter    com.example.ammar2                   D  Compat change id reported: 3400644; UID 10209; state: ENABLED
2025-03-05 04:05:49.339  8295-8295  FirebaseInitProvider    com.example.ammar2                   I  FirebaseApp initialization successful
2025-03-05 04:05:49.384  8295-8316  ziparchive              com.example.ammar2                   W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000002/DynamiteLoader.uncompressed.dm': No such file or directory
2025-03-05 04:05:49.385  8295-8316  ziparchive              com.example.ammar2                   W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000002/DynamiteLoader.uncompressed.dm': No such file or directory
2025-03-05 04:05:49.388  8295-8295  AmmarApplication        com.example.ammar2                   D  No active network found, assuming disconnected
2025-03-05 04:05:49.397  8295-8295  AmmarApplication        com.example.ammar2                   W  Network unavailable - Firebase services may not connect properly
2025-03-05 04:05:49.403  8295-8316  DynamiteModule          com.example.ammar2                   I  Considering local module com.google.android.gms.measurement.dynamite:100 and remote module com.google.android.gms.measurement.dynamite:115
2025-03-05 04:05:49.404  8295-8316  DynamiteModule          com.example.ammar2                   I  Selected remote version of com.google.android.gms.measurement.dynamite, version >= 115
2025-03-05 04:05:49.404  8295-8316  DynamiteModule          com.example.ammar2                   V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
2025-03-05 04:05:49.505  8295-8316  ziparchive              com.example.ammar2                   W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000007/MeasurementDynamite.dm': No such file or directory
2025-03-05 04:05:49.505  8295-8316  ziparchive              com.example.ammar2                   W  Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000007/MeasurementDynamite.dm': No such file or directory
2025-03-05 04:05:49.822  8295-8295  AmmarApplication        com.example.ammar2                   D  Firestore offline persistence enabled
2025-03-05 04:05:49.843  8295-8322  FA                      com.example.ammar2                   I  App measurement initialized, version: 95014
2025-03-05 04:05:49.843  8295-8322  FA                      com.example.ammar2                   I  To enable debug logging run: adb shell setprop log.tag.FA VERBOSE
2025-03-05 04:05:49.843  8295-8322  FA                      com.example.ammar2                   I  To enable faster debug mode event logging run:
adb shell setprop debug.firebase.analytics.app com.example.ammar2
2025-03-05 04:05:49.981  8295-8328  DynamiteModule          com.example.ammar2                   W  Local module descriptor class for com.google.android.gms.providerinstaller.dynamite not found.
2025-03-05 04:05:49.987  8295-8295  AmmarApplication        com.example.ammar2                   D  Firebase initialization completed successfully
2025-03-05 04:05:49.996  8295-8328  DynamiteModule          com.example.ammar2                   I  Considering local module com.google.android.gms.providerinstaller.dynamite:0 and remote module com.google.android.gms.providerinstaller.dynamite:0
2025-03-05 04:05:50.001  8295-8328  ProviderInstaller       com.example.ammar2                   W  Failed to load providerinstaller module: No acceptable module com.google.android.gms.providerinstaller.dynamite found. Local version is 0 and remote version is 0.
2025-03-05 04:05:50.005  8295-8295  Choreographer           com.example.ammar2                   I  Skipped 36 frames!  The application may be doing too much work on its main thread.
2025-03-05 04:05:50.015  8295-8295  AmmarApplication        com.example.ammar2                   D  Auth state changed. User: none
2025-03-05 04:05:50.018  8295-8332  FirebaseUtils           com.example.ammar2                   W  Network is not available, skipping host reachability checks
2025-03-05 04:05:50.037  8295-8328  ApplicationLoaders      com.example.ammar2                   D  Returning zygote-cached class loader: /system/framework/org.apache.http.legacy.jar
2025-03-05 04:05:50.065  8295-8328  nativeloader            com.example.ammar2                   D  Configuring clns-8 for other apk /system/framework/com.android.media.remotedisplay.jar. target_sdk_version=34, uses_libraries=ALL, library_path=/product/priv-app/PrebuiltGmsCore/lib/x86_64:/product/priv-app/PrebuiltGmsCore/PrebuiltGmsCore.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-03-05 04:05:50.126  8295-8295  MainActivity            com.example.ammar2                   D  onCreate called
2025-03-05 04:05:50.176  8295-8328  nativeloader            com.example.ammar2                   D  Configuring clns-9 for other apk /system/framework/com.android.location.provider.jar. target_sdk_version=34, uses_libraries=ALL, library_path=/product/priv-app/PrebuiltGmsCore/lib/x86_64:/product/priv-app/PrebuiltGmsCore/PrebuiltGmsCore.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-03-05 04:05:50.227  8295-8328  ApplicationLoaders      com.example.ammar2                   D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
2025-03-05 04:05:50.232  8295-8328  ApplicationLoaders      com.example.ammar2                   D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.sidecar.jar
2025-03-05 04:05:50.310  8295-8312  FirebaseMessaging       com.example.ammar2                   E  Failed to get FIS auth token
java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
at com.google.android.gms.tasks.Tasks.zza(com.google.android.gms: play-services-tasks@ @18.1.0:5)
at com.google.android.gms.tasks.Tasks.await(com.google.android.gms: play-services-tasks@ @18.1.0:9)
at com.google.firebase.messaging.GmsRpc.setDefaultAttributesToBundle(GmsRpc.java:260)
at com.google.firebase.messaging.GmsRpc.startRpc(GmsRpc.java:222)
at com.google.firebase.messaging.GmsRpc.getToken(GmsRpc.java:180)
at com.google.firebase.messaging.FirebaseMessaging.lambda
b
l
o
c
k
i
n
g
G
e
t
T
o
k
e
n
blockingGetToken
10
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
m
e
s
s
a
g
i
n
g
−
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
611
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
com−google−firebase−messaging−FirebaseMessaging(FirebaseMessaging.java:611)atcom.google.firebase.messaging.FirebaseMessaging
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
7.
s
t
a
r
t
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
6
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
R
e
q
u
e
s
t
D
e
d
u
p
l
i
c
a
t
o
r
.
g
e
t
O
r
S
t
a
r
t
G
e
t
T
o
k
e
n
R
e
q
u
e
s
t
(
R
e
q
u
e
s
t
D
e
d
u
p
l
i
c
a
t
o
r
.
j
a
v
a
:
67
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
b
l
o
c
k
i
n
g
G
e
t
T
o
k
e
n
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
607
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
l
a
m
b
d
a
ExternalSyntheticLambda7.start(UnknownSource:6)atcom.google.firebase.messaging.RequestDeduplicator.getOrStartGetTokenRequest(RequestDeduplicator.java:67)atcom.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:607)atcom.google.firebase.messaging.FirebaseMessaging.lambda
getToken
4
4
com-google-firebase-messaging-FirebaseMessaging(FirebaseMessaging.java:382)
at com.google.firebase.messaging.FirebaseMessaging
ExternalSyntheticLambda10.run(Unknown Source:4)
at java.util.concurrent.Executors
R
u
n
n
a
b
l
e
A
d
a
p
t
e
r
.
c
a
l
l
(
E
x
e
c
u
t
o
r
s
.
j
a
v
a
:
487
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
F
u
t
u
r
e
T
a
s
k
.
j
a
v
a
:
264
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
RunnableAdapter.call(Executors.java:487)atjava.util.concurrent.FutureTask.run(FutureTask.java:264)atjava.util.concurrent.ScheduledThreadPoolExecutor
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
o
m
m
o
n
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
z
z
a
.
r
u
n
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
b
a
s
e
m
e
n
t
@
@
18.3.0
:
2
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
C
a
u
s
e
d
b
y
:
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
E
x
c
e
p
t
i
o
n
:
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
S
e
r
v
i
c
e
i
s
u
n
a
v
a
i
l
a
b
l
e
.
P
l
e
a
s
e
t
r
y
a
g
a
i
n
l
a
t
e
r
.
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
r
e
m
o
t
e
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
S
e
r
v
i
c
e
C
l
i
e
n
t
.
c
r
e
a
t
e
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
(
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
S
e
r
v
i
c
e
C
l
i
e
n
t
.
j
a
v
a
:
208
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
r
e
g
i
s
t
e
r
F
i
d
W
i
t
h
S
e
r
v
e
r
(
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
j
a
v
a
:
533
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
d
o
N
e
t
w
o
r
k
C
a
l
l
I
f
N
e
c
e
s
s
a
r
y
(
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
j
a
v
a
:
387
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
l
a
m
b
d
a
Worker.run(ThreadPoolExecutor.java:644)atcom.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms:play−services−basement@@18.3.0:2)atjava.lang.Thread.run(Thread.java:1012)Causedby:com.google.firebase.installations.FirebaseInstallationsException:FirebaseInstallationsServiceisunavailable.Pleasetryagainlater.atcom.google.firebase.installations.remote.FirebaseInstallationServiceClient.createFirebaseInstallation(FirebaseInstallationServiceClient.java:208)atcom.google.firebase.installations.FirebaseInstallations.registerFidWithServer(FirebaseInstallations.java:533)atcom.google.firebase.installations.FirebaseInstallations.doNetworkCallIfNecessary(FirebaseInstallations.java:387)atcom.google.firebase.installations.FirebaseInstallations.lambda
doRegistrationOrRefresh
3
3
com-google-firebase-installations-FirebaseInstallations(FirebaseInstallations.java:372)
at com.google.firebase.installations.FirebaseInstallations
ExternalSyntheticLambda0.run(Unknown Source:4)
at com.google.firebase.concurrent.SequentialExecutor
1.
r
u
n
(
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
117
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
c
o
n
c
u
r
r
e
n
t
.
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
1.run(SequentialExecutor.java:117)atcom.google.firebase.concurrent.SequentialExecutor
QueueWorker.workOnQueue(SequentialExecutor.java:229)
at com.google.firebase.concurrent.SequentialExecutor
Q
u
e
u
e
W
o
r
k
e
r
.
r
u
n
(
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
174
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
QueueWorker.run(SequentialExecutor.java:174)atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.concurrent.CustomThreadFactory.lambda
n
e
w
T
h
r
e
a
d
newThread
0
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
c
o
n
c
u
r
r
e
n
t
−
C
u
s
t
o
m
T
h
r
e
a
d
F
a
c
t
o
r
y
(
C
u
s
t
o
m
T
h
r
e
a
d
F
a
c
t
o
r
y
.
j
a
v
a
:
47
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
c
o
n
c
u
r
r
e
n
t
.
C
u
s
t
o
m
T
h
r
e
a
d
F
a
c
t
o
r
y
com−google−firebase−concurrent−CustomThreadFactory(CustomThreadFactory.java:47)atcom.google.firebase.concurrent.CustomThreadFactory
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
 
2025
−
03
−
0504
:
05
:
50.3108295
−
8328
n
a
t
i
v
e
l
o
a
d
e
r
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
I
n
i
t
L
l
n
d
k
L
i
b
r
a
r
i
e
s
P
r
o
d
u
c
t
:
l
i
b
E
G
L
.
s
o
:
l
i
b
G
L
E
S
v
1
C
M
.
s
o
:
l
i
b
G
L
E
S
v
2.
s
o
:
l
i
b
G
L
E
S
v
3.
s
o
:
l
i
b
R
S
.
s
o
:
l
i
b
a
n
d
r
o
i
d
n
e
t
.
s
o
:
l
i
b
a
p
e
x
s
u
p
p
o
r
t
.
s
o
:
l
i
b
b
i
n
d
e
r
n
d
k
.
s
o
:
l
i
b
c
.
s
o
:
l
i
b
c
g
r
o
u
p
r
c
.
s
o
:
l
i
b
c
l
a
n
g
r
t
.
a
s
a
n
−
x
8
6
6
4
−
a
n
d
r
o
i
d
.
s
o
:
l
i
b
c
o
m
.
a
n
d
r
o
i
d
.
t
e
t
h
e
r
i
n
g
.
c
o
n
n
e
c
t
i
v
i
t
y
n
a
t
i
v
e
.
s
o
:
l
i
b
d
l
.
s
o
:
l
i
b
f
t
2.
s
o
:
l
i
b
l
o
g
.
s
o
:
l
i
b
m
.
s
o
:
l
i
b
m
e
d
i
a
n
d
k
.
s
o
:
l
i
b
n
a
t
i
v
e
w
i
n
d
o
w
.
s
o
:
l
i
b
n
e
u
r
a
l
n
e
t
w
o
r
k
s
.
s
o
:
l
i
b
s
e
l
i
n
u
x
.
s
o
:
l
i
b
s
y
n
c
.
s
o
:
l
i
b
v
e
n
d
o
r
s
u
p
p
o
r
t
.
s
o
:
l
i
b
v
n
d
k
s
u
p
p
o
r
t
.
s
o
:
l
i
b
v
u
l
k
a
n
.
s
o
2025
−
03
−
0504
:
05
:
50.3128295
−
8328
n
a
t
i
v
e
l
o
a
d
e
r
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
C
o
n
f
i
g
u
r
i
n
g
p
r
o
d
u
c
t
−
c
l
n
s
−
10
f
o
r
u
n
b
u
n
d
l
e
d
p
r
o
d
u
c
t
a
p
k
/
p
r
o
d
u
c
t
/
p
r
i
v
−
a
p
p
/
P
r
e
b
u
i
l
t
G
m
s
C
o
r
e
/
P
r
e
b
u
i
l
t
G
m
s
C
o
r
e
.
a
p
k
.
t
a
r
g
e
t
s
d
k
v
e
r
s
i
o
n
=
34
,
u
s
e
s
l
i
b
r
a
r
i
e
s
=
,
l
i
b
r
a
r
y
p
a
t
h
=
/
p
r
o
d
u
c
t
/
p
r
i
v
−
a
p
p
/
P
r
e
b
u
i
l
t
G
m
s
C
o
r
e
/
l
i
b
/
x
8
6
6
4
:
/
p
r
o
d
u
c
t
/
p
r
i
v
−
a
p
p
/
P
r
e
b
u
i
l
t
G
m
s
C
o
r
e
/
P
r
e
b
u
i
l
t
G
m
s
C
o
r
e
.
a
p
k
!
/
l
i
b
/
x
8
6
6
4
:
/
p
r
o
d
u
c
t
/
l
i
b
64
:
/
s
y
s
t
e
m
/
p
r
o
d
u
c
t
/
l
i
b
64
,
p
e
r
m
i
t
t
e
d
p
a
t
h
=
/
d
a
t
a
:
/
m
n
t
/
e
x
p
a
n
d
:
/
d
a
t
a
/
u
s
e
r
/
0
/
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
/
p
r
o
d
u
c
t
/
l
i
b
64
:
/
s
y
s
t
e
m
/
p
r
o
d
u
c
t
/
l
i
b
642025
−
03
−
0504
:
05
:
50.3308295
−
8328
n
a
t
i
v
e
l
o
a
d
e
r
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
I
n
i
t
V
n
d
k
s
p
L
i
b
r
a
r
i
e
s
P
r
o
d
u
c
t
:
V
N
D
K
i
s
d
e
p
r
e
c
a
t
e
d
w
i
t
h
p
r
o
d
u
c
t
2025
−
03
−
0504
:
05
:
50.3388295
−
8328
P
r
o
v
i
d
e
r
I
n
s
t
a
l
l
e
r
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
W
F
a
i
l
e
d
t
o
r
e
p
o
r
t
r
e
q
u
e
s
t
s
t
a
t
s
:
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
o
m
m
o
n
.
s
e
c
u
r
i
t
y
.
P
r
o
v
i
d
e
r
I
n
s
t
a
l
l
e
r
I
m
p
l
.
r
e
p
o
r
t
R
e
q
u
e
s
t
S
t
a
t
s
[
c
l
a
s
s
a
n
d
r
o
i
d
.
c
o
n
t
e
n
t
.
C
o
n
t
e
x
t
,
l
o
n
g
,
l
o
n
g
]
2025
−
03
−
0504
:
05
:
50.3978295
−
8322
F
A
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
I
T
a
g
M
a
n
a
g
e
r
i
s
n
o
t
f
o
u
n
d
a
n
d
t
h
u
s
w
i
l
l
n
o
t
b
e
u
s
e
d
2025
−
03
−
0504
:
05
:
50.4098295
−
8295
H
W
U
I
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
W
U
n
k
n
o
w
n
d
a
t
a
s
p
a
c
e
02025
−
03
−
0504
:
05
:
51.2628295
−
8295
G
o
o
g
l
e
S
i
g
n
I
n
H
e
l
p
e
r
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
G
o
o
g
l
e
S
i
g
n
I
n
c
l
i
e
n
t
i
n
i
t
i
a
l
i
z
e
d
s
u
c
c
e
s
s
f
u
l
l
y
2025
−
03
−
0504
:
05
:
51.2628295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
I
n
i
t
i
a
l
i
z
i
n
g
L
o
g
i
n
V
i
e
w
M
o
d
e
l
2025
−
03
−
0504
:
05
:
51.2648295
−
8295
A
u
t
h
S
e
r
v
i
c
e
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
R
e
f
r
e
s
h
i
n
g
u
s
e
r
s
t
a
t
e
2025
−
03
−
0504
:
05
:
51.2658295
−
8295
A
u
t
h
S
e
r
v
i
c
e
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
U
p
d
a
t
i
n
g
u
s
e
r
s
t
a
t
e
−
N
o
F
i
r
e
b
a
s
e
u
s
e
r
2025
−
03
−
0504
:
05
:
51.2658295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
y
n
c
i
n
g
s
t
a
t
e
s
w
i
t
h
A
u
t
h
S
e
r
v
i
c
e
2025
−
03
−
0504
:
05
:
51.2658295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
t
a
t
e
s
s
y
n
c
e
d
−
N
o
u
s
e
r
l
o
g
g
e
d
i
n
2025
−
03
−
0504
:
05
:
51.3148295
−
8295.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
W
M
e
t
h
o
d
b
o
o
l
e
a
n
a
n
d
r
o
i
d
x
.
c
o
m
p
o
s
e
.
r
u
n
t
i
m
e
.
s
n
a
p
s
h
o
t
s
.
S
n
a
p
s
h
o
t
S
t
a
t
e
L
i
s
t
.
c
o
n
d
i
t
i
o
n
a
l
U
p
d
a
t
e
(
b
o
o
l
e
a
n
,
k
o
t
l
i
n
.
j
v
m
.
f
u
n
c
t
i
o
n
s
.
F
u
n
c
t
i
o
n
1
)
f
a
i
l
e
d
l
o
c
k
v
e
r
i
f
i
c
a
t
i
o
n
a
n
d
w
i
l
l
r
u
n
s
l
o
w
e
r
.
C
o
m
m
o
n
c
a
u
s
e
s
f
o
r
l
o
c
k
v
e
r
i
f
i
c
a
t
i
o
n
i
s
s
u
e
s
a
r
e
n
o
n
−
o
p
t
i
m
i
z
e
d
d
e
x
c
o
d
e
a
n
d
i
n
c
o
r
r
e
c
t
p
r
o
g
u
a
r
d
o
p
t
i
m
i
z
a
t
i
o
n
s
.
2025
−
03
−
0504
:
05
:
51.3148295
−
8295.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
W
M
e
t
h
o
d
b
o
o
l
e
a
n
a
n
d
r
o
i
d
x
.
c
o
m
p
o
s
e
.
r
u
n
t
i
m
e
.
s
n
a
p
s
h
o
t
s
.
S
n
a
p
s
h
o
t
S
t
a
t
e
L
i
s
t
.
c
o
n
d
i
t
i
o
n
a
l
U
p
d
a
t
e
ExternalSyntheticLambda0.run(UnknownSource:4)atjava.lang.Thread.run(Thread.java:1012) 2025−03−0504:05:50.3108295−8328nativeloadercom.example.ammar2DInitLlndkLibrariesProduct:libEGL.so:libGLESv1 
C
​
 M.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid 
n
​
 et.so:libapexsupport.so:libbinder 
n
​
 dk.so:libc.so:libcgrouprc.so:libclang 
r
​
 t.asan−x86 
6
​
 4−android.so:libcom.android.tethering.connectivity 
n
​
 ative.so:libdl.so:libft2.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libselinux.so:libsync.so:libvendorsupport.so:libvndksupport.so:libvulkan.so2025−03−0504:05:50.3128295−8328nativeloadercom.example.ammar2DConfiguringproduct−clns−10forunbundledproductapk/product/priv−app/PrebuiltGmsCore/PrebuiltGmsCore.apk.target 
s
​
 dk 
v
​
 ersion=34,uses 
l
​
 ibraries=,library 
p
​
 ath=/product/priv−app/PrebuiltGmsCore/lib/x86 
6
​
 4:/product/priv−app/PrebuiltGmsCore/PrebuiltGmsCore.apk!/lib/x86 
6
​
 4:/product/lib64:/system/product/lib64,permitted 
p
​
 ath=/data:/mnt/expand:/data/user/0/com.google.android.gms:/product/lib64:/system/product/lib642025−03−0504:05:50.3308295−8328nativeloadercom.example.ammar2DInitVndkspLibrariesProduct:VNDKisdeprecatedwithproduct2025−03−0504:05:50.3388295−8328ProviderInstallercom.example.ammar2WFailedtoreportrequeststats:com.google.android.gms.common.security.ProviderInstallerImpl.reportRequestStats[classandroid.content.Context,long,long]2025−03−0504:05:50.3978295−8322FAcom.example.ammar2ITagManagerisnotfoundandthuswillnotbeused2025−03−0504:05:50.4098295−8295HWUIcom.example.ammar2WUnknowndataspace02025−03−0504:05:51.2628295−8295GoogleSignInHelpercom.example.ammar2DGoogleSignInclientinitializedsuccessfully2025−03−0504:05:51.2628295−8295LoginViewModelcom.example.ammar2DInitializingLoginViewModel2025−03−0504:05:51.2648295−8295AuthServicecom.example.ammar2DRefreshinguserstate2025−03−0504:05:51.2658295−8295AuthServicecom.example.ammar2DUpdatinguserstate−NoFirebaseuser2025−03−0504:05:51.2658295−8295LoginViewModelcom.example.ammar2DSyncingstateswithAuthService2025−03−0504:05:51.2658295−8295LoginViewModelcom.example.ammar2DStatessynced−Nouserloggedin2025−03−0504:05:51.3148295−8295.example.ammar2com.example.ammar2WMethodbooleanandroidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean,kotlin.jvm.functions.Function1)failedlockverificationandwillrunslower.Commoncausesforlockverificationissuesarenon−optimizeddexcodeandincorrectproguardoptimizations.2025−03−0504:05:51.3148295−8295.example.ammar2com.example.ammar2WMethodbooleanandroidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate
default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-03-05 04:05:51.315  8295-8295  .example.ammar2         com.example.ammar2                   W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-03-05 04:05:51.315  8295-8295  .example.ammar2         com.example.ammar2                   W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-03-05 04:05:51.315  8295-8295  .example.ammar2         com.example.ammar2                   W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update
Resolve.run(DnsNameResolver.java:318)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
C
a
u
s
e
d
b
y
:
j
a
v
a
.
n
e
t
.
U
n
k
n
o
w
n
H
o
s
t
E
x
c
e
p
t
i
o
n
:
U
n
a
b
l
e
t
o
r
e
s
o
l
v
e
h
o
s
t
"
f
i
r
e
s
t
o
r
e
.
g
o
o
g
l
e
a
p
i
s
.
c
o
m
"
:
N
o
a
d
d
r
e
s
s
a
s
s
o
c
i
a
t
e
d
w
i
t
h
h
o
s
t
n
a
m
e
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
H
o
s
t
B
y
N
a
m
e
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
156
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
A
l
l
H
o
s
t
A
d
d
r
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
103
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
A
d
d
r
e
s
s
.
g
e
t
A
l
l
B
y
N
a
m
e
(
I
n
e
t
A
d
d
r
e
s
s
.
j
a
v
a
:
1152
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
Worker.run(ThreadPoolExecutor.java:644)atjava.lang.Thread.run(Thread.java:1012)Causedby:java.net.UnknownHostException:Unabletoresolvehost"firestore.googleapis.com":Noaddressassociatedwithhostnameatjava.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)atjava.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)atjava.net.InetAddress.getAllByName(InetAddress.java:1152)atio.grpc.internal.DnsNameResolver
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632)
at io.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219)
at io.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) 
at io.grpc.internal.DnsNameResolver
R
e
s
o
l
v
e
.
r
u
n
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
318
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
Resolve.run(DnsNameResolver.java:318) atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644) 
at java.lang.Thread.run(Thread.java:1012) 
Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
at libcore.io.Linux.android_getaddrinfo(Native Method)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:222)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103) 
at java.net.InetAddress.getAllByName(InetAddress.java:1152) 
at io.grpc.internal.DnsNameResolver
J
d
k
A
d
d
r
e
s
s
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
632
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
e
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
219
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
d
o
R
e
s
o
l
v
e
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
282
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632) atio.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219) atio.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) atio.grpc.internal.DnsNameResolver
Resolve.run(DnsNameResolver.java:318) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor
Resolve.run(DnsNameResolver.java:318)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
C
a
u
s
e
d
b
y
:
j
a
v
a
.
n
e
t
.
U
n
k
n
o
w
n
H
o
s
t
E
x
c
e
p
t
i
o
n
:
U
n
a
b
l
e
t
o
r
e
s
o
l
v
e
h
o
s
t
"
f
i
r
e
s
t
o
r
e
.
g
o
o
g
l
e
a
p
i
s
.
c
o
m
"
:
N
o
a
d
d
r
e
s
s
a
s
s
o
c
i
a
t
e
d
w
i
t
h
h
o
s
t
n
a
m
e
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
H
o
s
t
B
y
N
a
m
e
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
156
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
A
l
l
H
o
s
t
A
d
d
r
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
103
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
A
d
d
r
e
s
s
.
g
e
t
A
l
l
B
y
N
a
m
e
(
I
n
e
t
A
d
d
r
e
s
s
.
j
a
v
a
:
1152
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
Worker.run(ThreadPoolExecutor.java:644)atjava.lang.Thread.run(Thread.java:1012)Causedby:java.net.UnknownHostException:Unabletoresolvehost"firestore.googleapis.com":Noaddressassociatedwithhostnameatjava.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)atjava.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)atjava.net.InetAddress.getAllByName(InetAddress.java:1152)atio.grpc.internal.DnsNameResolver
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632)
at io.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219)
at io.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) 
at io.grpc.internal.DnsNameResolver
R
e
s
o
l
v
e
.
r
u
n
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
318
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
Resolve.run(DnsNameResolver.java:318) atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644) 
at java.lang.Thread.run(Thread.java:1012) 
Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
at libcore.io.Linux.android_getaddrinfo(Native Method)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:222)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103) 
at java.net.InetAddress.getAllByName(InetAddress.java:1152) 
at io.grpc.internal.DnsNameResolver
J
d
k
A
d
d
r
e
s
s
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
632
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
e
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
219
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
d
o
R
e
s
o
l
v
e
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
282
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632) atio.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219) atio.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) atio.grpc.internal.DnsNameResolver
Resolve.run(DnsNameResolver.java:318) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor
getViaSnapshotListener
1
(
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
.
j
a
v
a
:
331
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
1(DocumentReference.java:331)atcom.google.firebase.firestore.DocumentReference
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
1.
o
n
E
v
e
n
t
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
8
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
.
l
a
m
b
d
a
ExternalSyntheticLambda1.onEvent(UnknownSource:8)atcom.google.firebase.firestore.DocumentReference.lambda
addSnapshotListenerInternal
2
2
com-google-firebase-firestore-DocumentReference(DocumentReference.java:504)
at com.google.firebase.firestore.DocumentReference
ExternalSyntheticLambda2.onEvent(Unknown Source:6)
at com.google.firebase.firestore.core.AsyncEventListener.lambda
o
n
E
v
e
n
t
onEvent
0
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
f
i
r
e
s
t
o
r
e
−
c
o
r
e
−
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
(
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
.
j
a
v
a
:
42
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
com−google−firebase−firestore−core−AsyncEventListener(AsyncEventListener.java:42)atcom.google.firebase.firestore.core.AsyncEventListener
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
6
)
a
t
a
n
d
r
o
i
d
x
.
b
i
o
m
e
t
r
i
c
.
a
u
t
h
.
C
l
a
s
s
2
B
i
o
m
e
t
r
i
c
A
u
t
h
E
x
t
e
n
s
i
o
n
s
K
t
ExternalSyntheticLambda0.run(UnknownSource:6)atandroidx.biometric.auth.Class2BiometricAuthExtensionsKt
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
e
x
e
c
u
t
e
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
0
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
.
o
n
E
v
e
n
t
(
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
.
j
a
v
a
:
39
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
Q
u
e
r
y
L
i
s
t
e
n
e
r
.
r
a
i
s
e
I
n
i
t
i
a
l
E
v
e
n
t
(
Q
u
e
r
y
L
i
s
t
e
n
e
r
.
j
a
v
a
:
181
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
Q
u
e
r
y
L
i
s
t
e
n
e
r
.
o
n
O
n
l
i
n
e
S
t
a
t
e
C
h
a
n
g
e
d
(
Q
u
e
r
y
L
i
s
t
e
n
e
r
.
j
a
v
a
:
117
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
E
v
e
n
t
M
a
n
a
g
e
r
.
h
a
n
d
l
e
O
n
l
i
n
e
S
t
a
t
e
C
h
a
n
g
e
(
E
v
e
n
t
M
a
n
a
g
e
r
.
j
a
v
a
:
178
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
S
y
n
c
E
n
g
i
n
e
.
h
a
n
d
l
e
O
n
l
i
n
e
S
t
a
t
e
C
h
a
n
g
e
(
S
y
n
c
E
n
g
i
n
e
.
j
a
v
a
:
384
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
M
e
m
o
r
y
C
o
m
p
o
n
e
n
t
P
r
o
v
i
d
e
r
ExternalSyntheticLambda0.execute(UnknownSource:0)atcom.google.firebase.firestore.core.AsyncEventListener.onEvent(AsyncEventListener.java:39)atcom.google.firebase.firestore.core.QueryListener.raiseInitialEvent(QueryListener.java:181)atcom.google.firebase.firestore.core.QueryListener.onOnlineStateChanged(QueryListener.java:117)atcom.google.firebase.firestore.core.EventManager.handleOnlineStateChange(EventManager.java:178)atcom.google.firebase.firestore.core.SyncEngine.handleOnlineStateChange(SyncEngine.java:384)atcom.google.firebase.firestore.core.MemoryComponentProvider
RemoteStoreCallback.handleOnlineStateChange(MemoryComponentProvider.java:145)
at com.google.firebase.firestore.remote.RemoteStore
ExternalSyntheticLambda1.handleOnlineStateChange(Unknown Source:2)
at com.google.firebase.firestore.remote.OnlineStateTracker.setAndBroadcastState(OnlineStateTracker.java:181)
at com.google.firebase.firestore.remote.OnlineStateTracker.handleWatchStreamFailure(OnlineStateTracker.java:154)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchStreamClose(RemoteStore.java:509)
at com.google.firebase.firestore.remote.RemoteStore.access
200
(
R
e
m
o
t
e
S
t
o
r
e
.
j
a
v
a
:
60
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
R
e
m
o
t
e
S
t
o
r
e
200(RemoteStore.java:60)atcom.google.firebase.firestore.remote.RemoteStore
1.onClose(RemoteStore.java:188)
at com.google.firebase.firestore.remote.AbstractStream.close(AbstractStream.java:356)
at com.google.firebase.firestore.remote.AbstractStream.handleServerClose(AbstractStream.java:410)
at com.google.firebase.firestore.remote.AbstractStream
S
t
r
e
a
m
O
b
s
e
r
v
e
r
.
l
a
m
b
d
a
StreamObserver.lambda
onClose
3
3
com-google-firebase-firestore-remote-AbstractStream
S
t
r
e
a
m
O
b
s
e
r
v
e
r
(
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
j
a
v
a
:
151
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
StreamObserver(AbstractStream.java:151)atcom.google.firebase.firestore.remote.AbstractStream
StreamObserver
ExternalSyntheticLambda1.run(Unknown Source:4)
at com.google.firebase.firestore.remote.AbstractStream
C
l
o
s
e
G
u
a
r
d
e
d
R
u
n
n
e
r
.
r
u
n
(
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
j
a
v
a
:
67
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
CloseGuardedRunner.run(AbstractStream.java:67)atcom.google.firebase.firestore.remote.AbstractStream
StreamObserver.onClose(AbstractStream.java:137)
at com.google.firebase.firestore.remote.FirestoreChannel
1.
o
n
C
l
o
s
e
(
F
i
r
e
s
t
o
r
e
C
h
a
n
n
e
l
.
j
a
v
a
:
151
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
c
l
o
s
e
O
b
s
e
r
v
e
r
(
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
j
a
v
a
:
567
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
a
c
c
e
s
s
1.onClose(FirestoreChannel.java:151)atio.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:567)atio.grpc.internal.ClientCallImpl.access
300(ClientCallImpl.java:71)
at io.grpc.internal.ClientCallImpl
C
l
i
e
n
t
S
t
r
e
a
m
L
i
s
t
e
n
e
r
I
m
p
l
ClientStreamListenerImpl
1StreamClosed.runInternal(ClientCallImpl.java:735)
at io.grpc.internal.ClientCallImpl
C
l
i
e
n
t
S
t
r
e
a
m
L
i
s
t
e
n
e
r
I
m
p
l
ClientStreamListenerImpl
1StreamClosed.runInContext(ClientCallImpl.java:716)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors
R
u
n
n
a
b
l
e
A
d
a
p
t
e
r
.
c
a
l
l
(
E
x
e
c
u
t
o
r
s
.
j
a
v
a
:
487
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
F
u
t
u
r
e
T
a
s
k
.
j
a
v
a
:
264
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
RunnableAdapter.call(Executors.java:487)atjava.util.concurrent.FutureTask.run(FutureTask.java:264)atjava.util.concurrent.ScheduledThreadPoolExecutor
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
2025
−
03
−
0504
:
05
:
51.7878295
−
8332
F
i
r
e
b
a
s
e
U
t
i
l
s
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
E
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
u
t
i
l
.
A
s
y
n
c
Q
u
e
u
e
Worker.run(ThreadPoolExecutor.java:644)2025−03−0504:05:51.7878295−8332FirebaseUtilscom.example.ammar2Eatcom.google.firebase.firestore.util.AsyncQueue
SynchronizedShutdownAwareExecutor
Resolve.run(DnsNameResolver.java:318)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
C
a
u
s
e
d
b
y
:
j
a
v
a
.
n
e
t
.
U
n
k
n
o
w
n
H
o
s
t
E
x
c
e
p
t
i
o
n
:
U
n
a
b
l
e
t
o
r
e
s
o
l
v
e
h
o
s
t
"
f
i
r
e
s
t
o
r
e
.
g
o
o
g
l
e
a
p
i
s
.
c
o
m
"
:
N
o
a
d
d
r
e
s
s
a
s
s
o
c
i
a
t
e
d
w
i
t
h
h
o
s
t
n
a
m
e
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
H
o
s
t
B
y
N
a
m
e
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
156
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
l
o
o
k
u
p
A
l
l
H
o
s
t
A
d
d
r
(
I
n
e
t
6
A
d
d
r
e
s
s
I
m
p
l
.
j
a
v
a
:
103
)
a
t
j
a
v
a
.
n
e
t
.
I
n
e
t
A
d
d
r
e
s
s
.
g
e
t
A
l
l
B
y
N
a
m
e
(
I
n
e
t
A
d
d
r
e
s
s
.
j
a
v
a
:
1152
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
Worker.run(ThreadPoolExecutor.java:644)atjava.lang.Thread.run(Thread.java:1012)Causedby:java.net.UnknownHostException:Unabletoresolvehost"firestore.googleapis.com":Noaddressassociatedwithhostnameatjava.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)atjava.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)atjava.net.InetAddress.getAllByName(InetAddress.java:1152)atio.grpc.internal.DnsNameResolver
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632)
at io.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219)
at io.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) 
at io.grpc.internal.DnsNameResolver
R
e
s
o
l
v
e
.
r
u
n
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
318
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
Resolve.run(DnsNameResolver.java:318) atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644) 
at java.lang.Thread.run(Thread.java:1012) 
Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
at libcore.io.Linux.android_getaddrinfo(Native Method)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:222)
at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:133)
at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103) 
at java.net.InetAddress.getAllByName(InetAddress.java:1152) 
at io.grpc.internal.DnsNameResolver
J
d
k
A
d
d
r
e
s
s
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
632
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
r
e
s
o
l
v
e
A
d
d
r
e
s
s
e
s
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
219
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
d
o
R
e
s
o
l
v
e
(
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
.
j
a
v
a
:
282
)
 
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
D
n
s
N
a
m
e
R
e
s
o
l
v
e
r
JdkAddressResolver.resolveAddress(DnsNameResolver.java:632) atio.grpc.internal.DnsNameResolver.resolveAddresses(DnsNameResolver.java:219) atio.grpc.internal.DnsNameResolver.doResolve(DnsNameResolver.java:282) atio.grpc.internal.DnsNameResolver
Resolve.run(DnsNameResolver.java:318) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor
initializeFirebase
5.
i
n
v
o
k
e
S
u
s
p
e
n
d
(
A
m
m
a
r
A
p
p
l
i
c
a
t
i
o
n
.
k
t
:
198
)
a
t
k
o
t
l
i
n
.
c
o
r
o
u
t
i
n
e
s
.
j
v
m
.
i
n
t
e
r
n
a
l
.
B
a
s
e
C
o
n
t
i
n
u
a
t
i
o
n
I
m
p
l
.
r
e
s
u
m
e
W
i
t
h
(
C
o
n
t
i
n
u
a
t
i
o
n
I
m
p
l
.
k
t
:
33
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
D
i
s
p
a
t
c
h
e
d
T
a
s
k
.
r
u
n
(
D
i
s
p
a
t
c
h
e
d
T
a
s
k
.
k
t
:
108
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
i
n
t
e
r
n
a
l
.
L
i
m
i
t
e
d
D
i
s
p
a
t
c
h
e
r
5.invokeSuspend(AmmarApplication.kt:198)atkotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)atkotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)atkotlinx.coroutines.internal.LimitedDispatcher
Worker.run(LimitedDispatcher.kt:115)
at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
at kotlinx.coroutines.scheduling.CoroutineScheduler
W
o
r
k
e
r
.
e
x
e
c
u
t
e
T
a
s
k
(
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
.
k
t
:
793
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
s
c
h
e
d
u
l
i
n
g
.
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
Worker.executeTask(CoroutineScheduler.kt:793)atkotlinx.coroutines.scheduling.CoroutineScheduler
Worker.runWorker(CoroutineScheduler.kt:697)
at kotlinx.coroutines.scheduling.CoroutineScheduler
W
o
r
k
e
r
.
r
u
n
(
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
.
k
t
:
684
)
C
a
u
s
e
d
b
y
:
j
a
v
a
.
i
o
.
I
O
E
x
c
e
p
t
i
o
n
:
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
i
o
n
E
x
c
e
p
t
i
o
n
:
j
a
v
a
.
i
o
.
I
O
E
x
c
e
p
t
i
o
n
:
A
U
T
H
E
N
T
I
C
A
T
I
O
N
F
A
I
L
E
D
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
b
l
o
c
k
i
n
g
G
e
t
T
o
k
e
n
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
626
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
l
a
m
b
d
a
Worker.run(CoroutineScheduler.kt:684)Causedby:java.io.IOException:java.util.concurrent.ExecutionException:java.io.IOException:AUTHENTICATION 
F
​
 AILEDatcom.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:626)atcom.google.firebase.messaging.FirebaseMessaging.lambda
getToken
4
4
com-google-firebase-messaging-FirebaseMessaging(FirebaseMessaging.java:382)
at com.google.firebase.messaging.FirebaseMessaging
ExternalSyntheticLambda10.run(Unknown Source:4)
at java.util.concurrent.Executors
R
u
n
n
a
b
l
e
A
d
a
p
t
e
r
.
c
a
l
l
(
E
x
e
c
u
t
o
r
s
.
j
a
v
a
:
487
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
F
u
t
u
r
e
T
a
s
k
.
j
a
v
a
:
264
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
RunnableAdapter.call(Executors.java:487)atjava.util.concurrent.FutureTask.run(FutureTask.java:264)atjava.util.concurrent.ScheduledThreadPoolExecutor
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
o
m
m
o
n
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
z
z
a
.
r
u
n
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
b
a
s
e
m
e
n
t
@
@
18.3.0
:
2
)
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
C
a
u
s
e
d
b
y
:
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
i
o
n
E
x
c
e
p
t
i
o
n
:
j
a
v
a
.
i
o
.
I
O
E
x
c
e
p
t
i
o
n
:
A
U
T
H
E
N
T
I
C
A
T
I
O
N
F
A
I
L
E
D
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
T
a
s
k
s
.
z
z
a
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
5
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
T
a
s
k
s
.
a
w
a
i
t
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
9
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
b
l
o
c
k
i
n
g
G
e
t
T
o
k
e
n
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
624
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
l
a
m
b
d
a
Worker.run(ThreadPoolExecutor.java:644)atcom.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms:play−services−basement@@18.3.0:2)atjava.lang.Thread.run(Thread.java:1012)Causedby:java.util.concurrent.ExecutionException:java.io.IOException:AUTHENTICATION 
F
​
 AILEDatcom.google.android.gms.tasks.Tasks.zza(com.google.android.gms:play−services−tasks@@18.1.0:5)atcom.google.android.gms.tasks.Tasks.await(com.google.android.gms:play−services−tasks@@18.1.0:9)atcom.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:624)atcom.google.firebase.messaging.FirebaseMessaging.lambda
getToken
4
4
com-google-firebase-messaging-FirebaseMessaging(FirebaseMessaging.java:382) 
at com.google.firebase.messaging.FirebaseMessaging
ExternalSyntheticLambda10.run(Unknown Source:4) 
at java.util.concurrent.Executors
R
u
n
n
a
b
l
e
A
d
a
p
t
e
r
.
c
a
l
l
(
E
x
e
c
u
t
o
r
s
.
j
a
v
a
:
487
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
F
u
t
u
r
e
T
a
s
k
.
j
a
v
a
:
264
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
RunnableAdapter.call(Executors.java:487) atjava.util.concurrent.FutureTask.run(FutureTask.java:264) atjava.util.concurrent.ScheduledThreadPoolExecutor
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
 
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
o
m
m
o
n
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
z
z
a
.
r
u
n
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
b
a
s
e
m
e
n
t
@
@
18.3.0
:
2
)
 
a
t
j
a
v
a
.
l
a
n
g
.
T
h
r
e
a
d
.
r
u
n
(
T
h
r
e
a
d
.
j
a
v
a
:
1012
)
 
C
a
u
s
e
d
b
y
:
j
a
v
a
.
i
o
.
I
O
E
x
c
e
p
t
i
o
n
:
A
U
T
H
E
N
T
I
C
A
T
I
O
N
F
A
I
L
E
D
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
.
h
a
n
d
l
e
R
e
s
p
o
n
s
e
(
G
m
s
R
p
c
.
j
a
v
a
:
309
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
.
l
a
m
b
d
a
Worker.run(ThreadPoolExecutor.java:644) atcom.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms:play−services−basement@@18.3.0:2) atjava.lang.Thread.run(Thread.java:1012) Causedby:java.io.IOException:AUTHENTICATION 
F
​
 AILEDatcom.google.firebase.messaging.GmsRpc.handleResponse(GmsRpc.java:309)atcom.google.firebase.messaging.GmsRpc.lambda
extractResponseWhenComplete
0
0
com-google-firebase-messaging-GmsRpc(GmsRpc.java:320)
at com.google.firebase.messaging.GmsRpc
ExternalSyntheticLambda0.then(Unknown Source:2)
at com.google.android.gms.tasks.zzc.run(com.google.android.gms: play-services-tasks@ @18.1.0:3)
at androidx.biometric.auth.Class2BiometricAuthExtensionsKt
ExternalSyntheticLambda0.execute(Unknown Source:0)
at com.google.android.gms.tasks.zzd.zzd(com.google.android.gms: play-services-tasks@ @18.1.0:1)
at com.google.android.gms.tasks.zzr.zzb(com.google.android.gms: play-services-tasks@ @18.1.0:5)
at com.google.android.gms.tasks.zzw.zzb(com.google.android.gms: play-services-tasks@ @18.1.0:3)
at com.google.android.gms.tasks.zzc.run(com.google.android.gms: play-services-tasks@ @18.1.0:8)
at com.google.android.gms.cloudmessaging.zzy.execute(Unknown Source:0)
at com.google.android.gms.tasks.zzd.zzd(com.google.android.gms: play-services-tasks@ @18.1.0:1)
at com.google.android.gms.tasks.zzr.zzb(com.google.android.gms: play-services-tasks@ @18.1.0:5)
at com.google.android.gms.tasks.zzw.zzb(com.google.android.gms: play-services-tasks@ @18.1.0:3)
at com.google.android.gms.tasks.TaskCompletionSource.setResult(com.google.android.gms: play-services-tasks@ @18.1.0:1)
2025-03-05 04:05:52.505  8295-8330  AmmarApplication        com.example.ammar2                   W  	at com.google.android.gms.cloudmessaging.zzr.zzd(com.google.android.gms: play-services-cloud-messaging@ @17.1.0:3)
at com.google.android.gms.cloudmessaging.zzt.zza(com.google.android.gms: play-services-cloud-messaging@ @17.1.0:2)
at com.google.android.gms.cloudmessaging.zzk.handleMessage(com.google.android.gms: play-services-cloud-messaging@ @17.1.0:14)
at android.os.Handler.dispatchMessage(Handler.java:103)
at android.os.Looper.loopOnce(Looper.java:232)
at android.os.Looper.loop(Looper.java:317)
at android.app.ActivityThread.main(ActivityThread.java:8705)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit
M
e
t
h
o
d
A
n
d
A
r
g
s
C
a
l
l
e
r
.
r
u
n
(
R
u
n
t
i
m
e
I
n
i
t
.
j
a
v
a
:
580
)
a
t
c
o
m
.
a
n
d
r
o
i
d
.
i
n
t
e
r
n
a
l
.
o
s
.
Z
y
g
o
t
e
I
n
i
t
.
m
a
i
n
(
Z
y
g
o
t
e
I
n
i
t
.
j
a
v
a
:
886
)
2025
−
03
−
0504
:
05
:
52.5078295
−
8330
A
m
m
a
r
A
p
p
l
i
c
a
t
i
o
n
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
N
e
t
w
o
r
k
c
o
n
n
e
c
t
e
d
:
f
a
l
s
e
2025
−
03
−
0504
:
05
:
52.5208295
−
8295
M
a
i
n
A
c
t
i
v
i
t
y
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
t
a
t
e
u
p
d
a
t
e
d
e
t
e
c
t
e
d
:
i
s
L
o
g
g
e
d
I
n
:
f
a
l
s
e
c
u
r
r
e
n
t
U
s
e
r
:
n
u
l
l
r
e
g
i
s
t
r
a
t
i
o
n
S
t
a
t
e
:
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2.
a
u
t
h
.
R
e
g
i
s
t
r
a
t
i
o
n
S
t
a
t
e
MethodAndArgsCaller.run(RuntimeInit.java:580)atcom.android.internal.os.ZygoteInit.main(ZygoteInit.java:886)2025−03−0504:05:52.5078295−8330AmmarApplicationcom.example.ammar2DNetworkconnected:false2025−03−0504:05:52.5208295−8295MainActivitycom.example.ammar2DStateupdatedetected:isLoggedIn:falsecurrentUser:nullregistrationState:com.example.ammar2.auth.RegistrationState
Initial@46aab2b
uiState: com.example.ammar2.auth.LoginUiState
I
n
i
t
i
a
l
@
f
d
42
b
882025
−
03
−
0504
:
05
:
52.5218295
−
8295
M
a
i
n
A
c
t
i
v
i
t
y
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
N
e
t
w
o
r
k
i
s
u
n
a
v
a
i
l
a
b
l
e
2025
−
03
−
0504
:
05
:
52.5368295
−
8295
A
u
t
h
S
c
r
e
e
n
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
I
n
i
t
i
a
l
l
a
u
n
c
h
−
r
e
f
r
e
s
h
i
n
g
u
s
e
r
s
t
a
t
e
2025
−
03
−
0504
:
05
:
52.5368295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
M
a
n
u
a
l
r
e
f
r
e
s
h
o
f
u
s
e
r
s
t
a
t
e
r
e
q
u
e
s
t
e
d
2025
−
03
−
0504
:
05
:
52.5388295
−
8295
A
u
t
h
S
e
r
v
i
c
e
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
R
e
f
r
e
s
h
i
n
g
u
s
e
r
s
t
a
t
e
2025
−
03
−
0504
:
05
:
52.5388295
−
8295
A
u
t
h
S
e
r
v
i
c
e
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
U
p
d
a
t
i
n
g
u
s
e
r
s
t
a
t
e
−
N
o
F
i
r
e
b
a
s
e
u
s
e
r
2025
−
03
−
0504
:
05
:
52.5388295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
y
n
c
i
n
g
s
t
a
t
e
s
w
i
t
h
A
u
t
h
S
e
r
v
i
c
e
2025
−
03
−
0504
:
05
:
52.5388295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
t
a
t
e
s
s
y
n
c
e
d
−
N
o
u
s
e
r
l
o
g
g
e
d
i
n
2025
−
03
−
0504
:
05
:
52.5388295
−
8295
L
o
g
i
n
V
i
e
w
M
o
d
e
l
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
M
a
n
u
a
l
u
s
e
r
s
t
a
t
e
r
e
f
r
e
s
h
c
o
m
p
l
e
t
e
d
2025
−
03
−
0504
:
05
:
52.5418295
−
8295
A
u
t
h
S
c
r
e
e
n
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
L
o
a
d
i
n
g
s
t
a
t
e
c
h
a
n
g
e
d
:
f
a
l
s
e
2025
−
03
−
0504
:
05
:
52.5438295
−
8295
A
u
t
h
S
c
r
e
e
n
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
D
S
c
r
e
e
n
c
h
a
n
g
e
d
t
o
:
l
o
g
i
n
2025
−
03
−
0504
:
05
:
52.5818295
−
8312
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
E
F
a
i
l
e
d
t
o
g
e
t
F
I
S
a
u
t
h
t
o
k
e
n
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
i
o
n
E
x
c
e
p
t
i
o
n
:
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
E
x
c
e
p
t
i
o
n
:
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
S
e
r
v
i
c
e
i
s
u
n
a
v
a
i
l
a
b
l
e
.
P
l
e
a
s
e
t
r
y
a
g
a
i
n
l
a
t
e
r
.
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
T
a
s
k
s
.
z
z
a
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
5
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
T
a
s
k
s
.
a
w
a
i
t
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
9
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
.
s
e
t
D
e
f
a
u
l
t
A
t
t
r
i
b
u
t
e
s
T
o
B
u
n
d
l
e
(
G
m
s
R
p
c
.
j
a
v
a
:
260
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
.
s
t
a
r
t
R
p
c
(
G
m
s
R
p
c
.
j
a
v
a
:
222
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
.
g
e
t
T
o
k
e
n
(
G
m
s
R
p
c
.
j
a
v
a
:
180
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
l
a
m
b
d
a
Initial@fd42b882025−03−0504:05:52.5218295−8295MainActivitycom.example.ammar2DNetworkisunavailable2025−03−0504:05:52.5368295−8295AuthScreencom.example.ammar2DInitiallaunch−refreshinguserstate2025−03−0504:05:52.5368295−8295LoginViewModelcom.example.ammar2DManualrefreshofuserstaterequested2025−03−0504:05:52.5388295−8295AuthServicecom.example.ammar2DRefreshinguserstate2025−03−0504:05:52.5388295−8295AuthServicecom.example.ammar2DUpdatinguserstate−NoFirebaseuser2025−03−0504:05:52.5388295−8295LoginViewModelcom.example.ammar2DSyncingstateswithAuthService2025−03−0504:05:52.5388295−8295LoginViewModelcom.example.ammar2DStatessynced−Nouserloggedin2025−03−0504:05:52.5388295−8295LoginViewModelcom.example.ammar2DManualuserstaterefreshcompleted2025−03−0504:05:52.5418295−8295AuthScreencom.example.ammar2DLoadingstatechanged:false2025−03−0504:05:52.5438295−8295AuthScreencom.example.ammar2DScreenchangedto:login2025−03−0504:05:52.5818295−8312FirebaseMessagingcom.example.ammar2EFailedtogetFISauthtokenjava.util.concurrent.ExecutionException:com.google.firebase.installations.FirebaseInstallationsException:FirebaseInstallationsServiceisunavailable.Pleasetryagainlater.atcom.google.android.gms.tasks.Tasks.zza(com.google.android.gms:play−services−tasks@@18.1.0:5)atcom.google.android.gms.tasks.Tasks.await(com.google.android.gms:play−services−tasks@@18.1.0:9)atcom.google.firebase.messaging.GmsRpc.setDefaultAttributesToBundle(GmsRpc.java:260)atcom.google.firebase.messaging.GmsRpc.startRpc(GmsRpc.java:222)atcom.google.firebase.messaging.GmsRpc.getToken(GmsRpc.java:180)atcom.google.firebase.messaging.FirebaseMessaging.lambda
blockingGetToken
10
10
com-google-firebase-messaging-FirebaseMessaging(FirebaseMessaging.java:611)
at com.google.firebase.messaging.FirebaseMessaging
ExternalSyntheticLambda7.start(Unknown Source:6)
at com.google.firebase.messaging.RequestDeduplicator.getOrStartGetTokenRequest(RequestDeduplicator.java:67)
at com.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:607)
at com.google.firebase.messaging.FirebaseMessaging.lambda
g
e
t
T
o
k
e
n
getToken
4
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
m
e
s
s
a
g
i
n
g
−
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
382
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
com−google−firebase−messaging−FirebaseMessaging(FirebaseMessaging.java:382)atcom.google.firebase.messaging.FirebaseMessaging
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
10.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
o
r
s
ExternalSyntheticLambda10.run(UnknownSource:4)atjava.util.concurrent.Executors
RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor
S
c
h
e
d
u
l
e
d
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
307
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644)
at com.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms: play-services-basement@ @18.3.0:2)
at java.lang.Thread.run(Thread.java:1012)
Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
at com.google.firebase.installations.remote.FirebaseInstallationServiceClient.createFirebaseInstallation(FirebaseInstallationServiceClient.java:208)
at com.google.firebase.installations.FirebaseInstallations.registerFidWithServer(FirebaseInstallations.java:533)
at com.google.firebase.installations.FirebaseInstallations.doNetworkCallIfNecessary(FirebaseInstallations.java:387)
at com.google.firebase.installations.FirebaseInstallations.lambda
d
o
R
e
g
i
s
t
r
a
t
i
o
n
O
r
R
e
f
r
e
s
h
doRegistrationOrRefresh
3
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
i
n
s
t
a
l
l
a
t
i
o
n
s
−
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
(
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
.
j
a
v
a
:
372
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
i
n
s
t
a
l
l
a
t
i
o
n
s
.
F
i
r
e
b
a
s
e
I
n
s
t
a
l
l
a
t
i
o
n
s
com−google−firebase−installations−FirebaseInstallations(FirebaseInstallations.java:372)atcom.google.firebase.installations.FirebaseInstallations
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
c
o
n
c
u
r
r
e
n
t
.
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
ExternalSyntheticLambda0.run(UnknownSource:4)atcom.google.firebase.concurrent.SequentialExecutor
1.run(SequentialExecutor.java:117)
at com.google.firebase.concurrent.SequentialExecutor
Q
u
e
u
e
W
o
r
k
e
r
.
w
o
r
k
O
n
Q
u
e
u
e
(
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
229
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
c
o
n
c
u
r
r
e
n
t
.
S
e
q
u
e
n
t
i
a
l
E
x
e
c
u
t
o
r
QueueWorker.workOnQueue(SequentialExecutor.java:229)atcom.google.firebase.concurrent.SequentialExecutor
QueueWorker.run(SequentialExecutor.java:174)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor
W
o
r
k
e
r
.
r
u
n
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
644
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
c
o
n
c
u
r
r
e
n
t
.
C
u
s
t
o
m
T
h
r
e
a
d
F
a
c
t
o
r
y
.
l
a
m
b
d
a
Worker.run(ThreadPoolExecutor.java:644)atcom.google.firebase.concurrent.CustomThreadFactory.lambda
newThread
0
0
com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
at com.google.firebase.concurrent.CustomThreadFactory
ExternalSyntheticLambda0.run(Unknown Source:4)
at java.lang.Thread.run(Thread.java:1012) 
2025-03-05 04:05:52.626  8295-8295  Choreographer           com.example.ammar2                   I  Skipped 124 frames!  The application may be doing too much work on its main thread.
2025-03-05 04:05:52.771  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:05:52.928  8295-8295  AuthService             com.example.ammar2                   D  Firebase Auth state changed - User logged out
2025-03-05 04:05:52.932  8295-8295  AmmarApplication        com.example.ammar2                   E  Firestore connection test failed
com.google.firebase.firestore.FirebaseFirestoreException: Failed to get document because the client is offline.
at com.google.firebase.firestore.DocumentReference.lambda
g
e
t
V
i
a
S
n
a
p
s
h
o
t
L
i
s
t
e
n
e
r
getViaSnapshotListener
1(DocumentReference.java:331)
at com.google.firebase.firestore.DocumentReference
ExternalSyntheticLambda1.onEvent(Unknown Source:8)
at com.google.firebase.firestore.DocumentReference.lambda
a
d
d
S
n
a
p
s
h
o
t
L
i
s
t
e
n
e
r
I
n
t
e
r
n
a
l
addSnapshotListenerInternal
2
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
f
i
r
e
s
t
o
r
e
−
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
(
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
.
j
a
v
a
:
504
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
D
o
c
u
m
e
n
t
R
e
f
e
r
e
n
c
e
com−google−firebase−firestore−DocumentReference(DocumentReference.java:504)atcom.google.firebase.firestore.DocumentReference
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
2.
o
n
E
v
e
n
t
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
6
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
c
o
r
e
.
A
s
y
n
c
E
v
e
n
t
L
i
s
t
e
n
e
r
.
l
a
m
b
d
a
ExternalSyntheticLambda2.onEvent(UnknownSource:6)atcom.google.firebase.firestore.core.AsyncEventListener.lambda
onEvent
0
0
com-google-firebase-firestore-core-AsyncEventListener(AsyncEventListener.java:42)
at com.google.firebase.firestore.core.AsyncEventListener
ExternalSyntheticLambda0.run(Unknown Source:6)
at androidx.biometric.auth.Class2BiometricAuthExtensionsKt
ExternalSyntheticLambda0.execute(Unknown Source:0)
at com.google.firebase.firestore.core.AsyncEventListener.onEvent(AsyncEventListener.java:39)
at com.google.firebase.firestore.core.QueryListener.raiseInitialEvent(QueryListener.java:181)
at com.google.firebase.firestore.core.QueryListener.onOnlineStateChanged(QueryListener.java:117)
at com.google.firebase.firestore.core.EventManager.handleOnlineStateChange(EventManager.java:178)
at com.google.firebase.firestore.core.SyncEngine.handleOnlineStateChange(SyncEngine.java:384)
at com.google.firebase.firestore.core.MemoryComponentProvider
R
e
m
o
t
e
S
t
o
r
e
C
a
l
l
b
a
c
k
.
h
a
n
d
l
e
O
n
l
i
n
e
S
t
a
t
e
C
h
a
n
g
e
(
M
e
m
o
r
y
C
o
m
p
o
n
e
n
t
P
r
o
v
i
d
e
r
.
j
a
v
a
:
145
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
R
e
m
o
t
e
S
t
o
r
e
RemoteStoreCallback.handleOnlineStateChange(MemoryComponentProvider.java:145)atcom.google.firebase.firestore.remote.RemoteStore
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
1.
h
a
n
d
l
e
O
n
l
i
n
e
S
t
a
t
e
C
h
a
n
g
e
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
2
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
O
n
l
i
n
e
S
t
a
t
e
T
r
a
c
k
e
r
.
s
e
t
A
n
d
B
r
o
a
d
c
a
s
t
S
t
a
t
e
(
O
n
l
i
n
e
S
t
a
t
e
T
r
a
c
k
e
r
.
j
a
v
a
:
181
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
O
n
l
i
n
e
S
t
a
t
e
T
r
a
c
k
e
r
.
h
a
n
d
l
e
W
a
t
c
h
S
t
r
e
a
m
F
a
i
l
u
r
e
(
O
n
l
i
n
e
S
t
a
t
e
T
r
a
c
k
e
r
.
j
a
v
a
:
154
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
R
e
m
o
t
e
S
t
o
r
e
.
h
a
n
d
l
e
W
a
t
c
h
S
t
r
e
a
m
C
l
o
s
e
(
R
e
m
o
t
e
S
t
o
r
e
.
j
a
v
a
:
509
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
R
e
m
o
t
e
S
t
o
r
e
.
a
c
c
e
s
s
ExternalSyntheticLambda1.handleOnlineStateChange(UnknownSource:2)atcom.google.firebase.firestore.remote.OnlineStateTracker.setAndBroadcastState(OnlineStateTracker.java:181)atcom.google.firebase.firestore.remote.OnlineStateTracker.handleWatchStreamFailure(OnlineStateTracker.java:154)atcom.google.firebase.firestore.remote.RemoteStore.handleWatchStreamClose(RemoteStore.java:509)atcom.google.firebase.firestore.remote.RemoteStore.access
200(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore
1.
o
n
C
l
o
s
e
(
R
e
m
o
t
e
S
t
o
r
e
.
j
a
v
a
:
188
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
c
l
o
s
e
(
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
j
a
v
a
:
356
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
h
a
n
d
l
e
S
e
r
v
e
r
C
l
o
s
e
(
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
j
a
v
a
:
410
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
1.onClose(RemoteStore.java:188)atcom.google.firebase.firestore.remote.AbstractStream.close(AbstractStream.java:356)atcom.google.firebase.firestore.remote.AbstractStream.handleServerClose(AbstractStream.java:410)atcom.google.firebase.firestore.remote.AbstractStream
StreamObserver.lambda
o
n
C
l
o
s
e
onClose
3
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
f
i
r
e
s
t
o
r
e
−
r
e
m
o
t
e
−
A
b
s
t
r
a
c
t
S
t
r
e
a
m
com−google−firebase−firestore−remote−AbstractStream
StreamObserver(AbstractStream.java:151)
at com.google.firebase.firestore.remote.AbstractStream
S
t
r
e
a
m
O
b
s
e
r
v
e
r
StreamObserver
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
1.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
A
b
s
t
r
a
c
t
S
t
r
e
a
m
ExternalSyntheticLambda1.run(UnknownSource:4)atcom.google.firebase.firestore.remote.AbstractStream
CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream
S
t
r
e
a
m
O
b
s
e
r
v
e
r
.
o
n
C
l
o
s
e
(
A
b
s
t
r
a
c
t
S
t
r
e
a
m
.
j
a
v
a
:
137
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
f
i
r
e
s
t
o
r
e
.
r
e
m
o
t
e
.
F
i
r
e
s
t
o
r
e
C
h
a
n
n
e
l
StreamObserver.onClose(AbstractStream.java:137)atcom.google.firebase.firestore.remote.FirestoreChannel
1.onClose(FirestoreChannel.java:151)
at io.grpc.internal.ClientCallImpl.closeObserver(ClientCallImpl.java:567)
at io.grpc.internal.ClientCallImpl.access
300
(
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
j
a
v
a
:
71
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
C
l
i
e
n
t
C
a
l
l
I
m
p
l
300(ClientCallImpl.java:71)atio.grpc.internal.ClientCallImpl
ClientStreamListenerImpl
1
S
t
r
e
a
m
C
l
o
s
e
d
.
r
u
n
I
n
t
e
r
n
a
l
(
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
j
a
v
a
:
735
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
C
l
i
e
n
t
C
a
l
l
I
m
p
l
1StreamClosed.runInternal(ClientCallImpl.java:735)atio.grpc.internal.ClientCallImpl
ClientStreamListenerImpl
1
S
t
r
e
a
m
C
l
o
s
e
d
.
r
u
n
I
n
C
o
n
t
e
x
t
(
C
l
i
e
n
t
C
a
l
l
I
m
p
l
.
j
a
v
a
:
716
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
C
o
n
t
e
x
t
R
u
n
n
a
b
l
e
.
r
u
n
(
C
o
n
t
e
x
t
R
u
n
n
a
b
l
e
.
j
a
v
a
:
37
)
a
t
i
o
.
g
r
p
c
.
i
n
t
e
r
n
a
l
.
S
e
r
i
a
l
i
z
i
n
g
E
x
e
c
u
t
o
r
.
r
u
n
(
S
e
r
i
a
l
i
z
i
n
g
E
x
e
c
u
t
o
r
.
j
a
v
a
:
133
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
o
r
s
1StreamClosed.runInContext(ClientCallImpl.java:716)atio.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)atio.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)atjava.util.concurrent.Executors
RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor
S
c
h
e
d
u
l
e
d
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
307
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644)
2025-03-05 04:05:52.932  8295-8295  AmmarApplication        com.example.ammar2                   E  	at com.google.firebase.firestore.util.AsyncQueue
S
y
n
c
h
r
o
n
i
z
e
d
S
h
u
t
d
o
w
n
A
w
a
r
e
E
x
e
c
u
t
o
r
SynchronizedShutdownAwareExecutor
DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
2025-03-05 04:05:52.945  8295-8358  HWUI                    com.example.ammar2                   I  Davey! duration=2383ms; Flags=0, FrameTimelineVsyncId=139509, IntendedVsync=9788556946641, Vsync=9790623613225, InputEventId=0, HandleInputStart=9790629213617, AnimationStart=9790629215231, PerformTraversalsStart=9790896568417, DrawStart=9790897851271, FrameDeadline=9790456946565, FrameInterval=9790626626841, FrameStartTime=16666666, SyncQueued=9790927719279, SyncStart=9790927857243, IssueDrawCommandsStart=9790927978257, SwapBuffers=9790929418924, FrameCompleted=9790941021395, DequeueBufferDuration=3568, QueueBufferDuration=367216, GpuCompleted=9790941021395, SwapBuffersCompleted=9790930788404, DisplayPresentTime=144115222452437076, CommandSubmissionCompleted=9790929418924,
2025-03-05 04:05:52.946  8295-8300  .example.ammar2         com.example.ammar2                   I  Compiler allocated 5174KB to compile void android.view.ViewRootImpl.performTraversals()
2025-03-05 04:05:53.064  8295-8332  FirebaseUtils           com.example.ammar2                   E  FCM connectivity check failed
java.util.concurrent.ExecutionException: java.io.IOException: java.util.concurrent.ExecutionException: java.io.IOException: AUTHENTICATION_FAILED
at com.google.android.gms.tasks.Tasks.zza(com.google.android.gms: play-services-tasks@ @18.1.0:5)
at com.google.android.gms.tasks.Tasks.await(com.google.android.gms: play-services-tasks@ @18.1.0:20)
at com.example.ammar2.utils.FirebaseUtils
c
h
e
c
k
F
i
r
e
b
a
s
e
C
o
n
n
e
c
t
i
v
i
t
y
checkFirebaseConnectivity
2
t
o
k
e
n
token
1.invokeSuspend(FirebaseUtils.kt:83)
at com.example.ammar2.utils.FirebaseUtils
c
h
e
c
k
F
i
r
e
b
a
s
e
C
o
n
n
e
c
t
i
v
i
t
y
checkFirebaseConnectivity
2
t
o
k
e
n
token
1.invoke(Unknown Source:8)
at com.example.ammar2.utils.FirebaseUtils
c
h
e
c
k
F
i
r
e
b
a
s
e
C
o
n
n
e
c
t
i
v
i
t
y
checkFirebaseConnectivity
2
t
o
k
e
n
token
1.invoke(Unknown Source:4)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturnIgnoreTimeout(Undispatched.kt:89)
at kotlinx.coroutines.TimeoutKt.setupTimeout(Timeout.kt:151)
at kotlinx.coroutines.TimeoutKt.withTimeoutOrNull(Timeout.kt:107)
at com.example.ammar2.utils.FirebaseUtils
c
h
e
c
k
F
i
r
e
b
a
s
e
C
o
n
n
e
c
t
i
v
i
t
y
checkFirebaseConnectivity
2.invokeSuspend(FirebaseUtils.kt:82)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.internal.ScopeCoroutine.afterResume(Scopes.kt:32)
at kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:102)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)
at kotlinx.coroutines.internal.LimitedDispatcher
W
o
r
k
e
r
.
r
u
n
(
L
i
m
i
t
e
d
D
i
s
p
a
t
c
h
e
r
.
k
t
:
115
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
s
c
h
e
d
u
l
i
n
g
.
T
a
s
k
I
m
p
l
.
r
u
n
(
T
a
s
k
s
.
k
t
:
103
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
s
c
h
e
d
u
l
i
n
g
.
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
.
r
u
n
S
a
f
e
l
y
(
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
.
k
t
:
584
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
s
c
h
e
d
u
l
i
n
g
.
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
Worker.run(LimitedDispatcher.kt:115)atkotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)atkotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)atkotlinx.coroutines.scheduling.CoroutineScheduler
Worker.executeTask(CoroutineScheduler.kt:793)
at kotlinx.coroutines.scheduling.CoroutineScheduler
W
o
r
k
e
r
.
r
u
n
W
o
r
k
e
r
(
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
.
k
t
:
697
)
a
t
k
o
t
l
i
n
x
.
c
o
r
o
u
t
i
n
e
s
.
s
c
h
e
d
u
l
i
n
g
.
C
o
r
o
u
t
i
n
e
S
c
h
e
d
u
l
e
r
Worker.runWorker(CoroutineScheduler.kt:697)atkotlinx.coroutines.scheduling.CoroutineScheduler
Worker.run(CoroutineScheduler.kt:684)
Caused by: java.io.IOException: java.util.concurrent.ExecutionException: java.io.IOException: AUTHENTICATION_FAILED
at com.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:626)
at com.google.firebase.messaging.FirebaseMessaging.lambda
g
e
t
T
o
k
e
n
getToken
4
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
m
e
s
s
a
g
i
n
g
−
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
382
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
com−google−firebase−messaging−FirebaseMessaging(FirebaseMessaging.java:382)atcom.google.firebase.messaging.FirebaseMessaging
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
10.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
o
r
s
ExternalSyntheticLambda10.run(UnknownSource:4)atjava.util.concurrent.Executors
RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor
S
c
h
e
d
u
l
e
d
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
307
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644)
at com.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms: play-services-basement@ @18.3.0:2)
at java.lang.Thread.run(Thread.java:1012)
Caused by: java.util.concurrent.ExecutionException: java.io.IOException: AUTHENTICATION_FAILED
at com.google.android.gms.tasks.Tasks.zza(com.google.android.gms: play-services-tasks@ @18.1.0:5)
at com.google.android.gms.tasks.Tasks.await(com.google.android.gms: play-services-tasks@ @18.1.0:9)
at com.google.firebase.messaging.FirebaseMessaging.blockingGetToken(FirebaseMessaging.java:624)
at com.google.firebase.messaging.FirebaseMessaging.lambda
g
e
t
T
o
k
e
n
getToken
4
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
m
e
s
s
a
g
i
n
g
−
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
(
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
.
j
a
v
a
:
382
)
 
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
F
i
r
e
b
a
s
e
M
e
s
s
a
g
i
n
g
com−google−firebase−messaging−FirebaseMessaging(FirebaseMessaging.java:382) atcom.google.firebase.messaging.FirebaseMessaging
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
10.
r
u
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
4
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
E
x
e
c
u
t
o
r
s
ExternalSyntheticLambda10.run(UnknownSource:4) atjava.util.concurrent.Executors
RunnableAdapter.call(Executors.java:487) 
at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
at java.util.concurrent.ScheduledThreadPoolExecutor
S
c
h
e
d
u
l
e
d
F
u
t
u
r
e
T
a
s
k
.
r
u
n
(
S
c
h
e
d
u
l
e
d
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
307
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
r
u
n
W
o
r
k
e
r
(
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
.
j
a
v
a
:
1145
)
 
a
t
j
a
v
a
.
u
t
i
l
.
c
o
n
c
u
r
r
e
n
t
.
T
h
r
e
a
d
P
o
o
l
E
x
e
c
u
t
o
r
ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) atjava.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) atjava.util.concurrent.ThreadPoolExecutor
Worker.run(ThreadPoolExecutor.java:644) 
at com.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms: play-services-basement@ @18.3.0:2) 
at java.lang.Thread.run(Thread.java:1012) 
Caused by: java.io.IOException: AUTHENTICATION_FAILED
at com.google.firebase.messaging.GmsRpc.handleResponse(GmsRpc.java:309)
at com.google.firebase.messaging.GmsRpc.lambda
e
x
t
r
a
c
t
R
e
s
p
o
n
s
e
W
h
e
n
C
o
m
p
l
e
t
e
extractResponseWhenComplete
0
c
o
m
−
g
o
o
g
l
e
−
f
i
r
e
b
a
s
e
−
m
e
s
s
a
g
i
n
g
−
G
m
s
R
p
c
(
G
m
s
R
p
c
.
j
a
v
a
:
320
)
a
t
c
o
m
.
g
o
o
g
l
e
.
f
i
r
e
b
a
s
e
.
m
e
s
s
a
g
i
n
g
.
G
m
s
R
p
c
com−google−firebase−messaging−GmsRpc(GmsRpc.java:320)atcom.google.firebase.messaging.GmsRpc
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
t
h
e
n
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
2
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
c
.
r
u
n
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
3
)
a
t
a
n
d
r
o
i
d
x
.
b
i
o
m
e
t
r
i
c
.
a
u
t
h
.
C
l
a
s
s
2
B
i
o
m
e
t
r
i
c
A
u
t
h
E
x
t
e
n
s
i
o
n
s
K
t
ExternalSyntheticLambda0.then(UnknownSource:2)atcom.google.android.gms.tasks.zzc.run(com.google.android.gms:play−services−tasks@@18.1.0:3)atandroidx.biometric.auth.Class2BiometricAuthExtensionsKt
E
x
t
e
r
n
a
l
S
y
n
t
h
e
t
i
c
L
a
m
b
d
a
0.
e
x
e
c
u
t
e
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
0
)
2025
−
03
−
0504
:
05
:
53.0658295
−
8332
F
i
r
e
b
a
s
e
U
t
i
l
s
c
o
m
.
e
x
a
m
p
l
e
.
a
m
m
a
r
2
E
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
d
.
z
z
d
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
1
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
r
.
z
z
b
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
5
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
w
.
z
z
b
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
3
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
c
.
r
u
n
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
8
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
l
o
u
d
m
e
s
s
a
g
i
n
g
.
z
z
y
.
e
x
e
c
u
t
e
(
U
n
k
n
o
w
n
S
o
u
r
c
e
:
0
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
d
.
z
z
d
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
1
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
r
.
z
z
b
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
5
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
z
z
w
.
z
z
b
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
3
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
t
a
s
k
s
.
T
a
s
k
C
o
m
p
l
e
t
i
o
n
S
o
u
r
c
e
.
s
e
t
R
e
s
u
l
t
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
t
a
s
k
s
@
@
18.1.0
:
1
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
l
o
u
d
m
e
s
s
a
g
i
n
g
.
z
z
r
.
z
z
d
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
c
l
o
u
d
−
m
e
s
s
a
g
i
n
g
@
@
17.1.0
:
3
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
l
o
u
d
m
e
s
s
a
g
i
n
g
.
z
z
t
.
z
z
a
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
c
l
o
u
d
−
m
e
s
s
a
g
i
n
g
@
@
17.1.0
:
2
)
a
t
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
.
c
l
o
u
d
m
e
s
s
a
g
i
n
g
.
z
z
k
.
h
a
n
d
l
e
M
e
s
s
a
g
e
(
c
o
m
.
g
o
o
g
l
e
.
a
n
d
r
o
i
d
.
g
m
s
:
p
l
a
y
−
s
e
r
v
i
c
e
s
−
c
l
o
u
d
−
m
e
s
s
a
g
i
n
g
@
@
17.1.0
:
14
)
a
t
a
n
d
r
o
i
d
.
o
s
.
H
a
n
d
l
e
r
.
d
i
s
p
a
t
c
h
M
e
s
s
a
g
e
(
H
a
n
d
l
e
r
.
j
a
v
a
:
103
)
a
t
a
n
d
r
o
i
d
.
o
s
.
L
o
o
p
e
r
.
l
o
o
p
O
n
c
e
(
L
o
o
p
e
r
.
j
a
v
a
:
232
)
a
t
a
n
d
r
o
i
d
.
o
s
.
L
o
o
p
e
r
.
l
o
o
p
(
L
o
o
p
e
r
.
j
a
v
a
:
317
)
a
t
a
n
d
r
o
i
d
.
a
p
p
.
A
c
t
i
v
i
t
y
T
h
r
e
a
d
.
m
a
i
n
(
A
c
t
i
v
i
t
y
T
h
r
e
a
d
.
j
a
v
a
:
8705
)
a
t
j
a
v
a
.
l
a
n
g
.
r
e
f
l
e
c
t
.
M
e
t
h
o
d
.
i
n
v
o
k
e
(
N
a
t
i
v
e
M
e
t
h
o
d
)
a
t
c
o
m
.
a
n
d
r
o
i
d
.
i
n
t
e
r
n
a
l
.
o
s
.
R
u
n
t
i
m
e
I
n
i
t
ExternalSyntheticLambda0.execute(UnknownSource:0)2025−03−0504:05:53.0658295−8332FirebaseUtilscom.example.ammar2Eatcom.google.android.gms.tasks.zzd.zzd(com.google.android.gms:play−services−tasks@@18.1.0:1)atcom.google.android.gms.tasks.zzr.zzb(com.google.android.gms:play−services−tasks@@18.1.0:5)atcom.google.android.gms.tasks.zzw.zzb(com.google.android.gms:play−services−tasks@@18.1.0:3)atcom.google.android.gms.tasks.zzc.run(com.google.android.gms:play−services−tasks@@18.1.0:8)atcom.google.android.gms.cloudmessaging.zzy.execute(UnknownSource:0)atcom.google.android.gms.tasks.zzd.zzd(com.google.android.gms:play−services−tasks@@18.1.0:1)atcom.google.android.gms.tasks.zzr.zzb(com.google.android.gms:play−services−tasks@@18.1.0:5)atcom.google.android.gms.tasks.zzw.zzb(com.google.android.gms:play−services−tasks@@18.1.0:3)atcom.google.android.gms.tasks.TaskCompletionSource.setResult(com.google.android.gms:play−services−tasks@@18.1.0:1)atcom.google.android.gms.cloudmessaging.zzr.zzd(com.google.android.gms:play−services−cloud−messaging@@17.1.0:3)atcom.google.android.gms.cloudmessaging.zzt.zza(com.google.android.gms:play−services−cloud−messaging@@17.1.0:2)atcom.google.android.gms.cloudmessaging.zzk.handleMessage(com.google.android.gms:play−services−cloud−messaging@@17.1.0:14)atandroid.os.Handler.dispatchMessage(Handler.java:103)atandroid.os.Looper.loopOnce(Looper.java:232)atandroid.os.Looper.loop(Looper.java:317)atandroid.app.ActivityThread.main(ActivityThread.java:8705)atjava.lang.reflect.Method.invoke(NativeMethod)atcom.android.internal.os.RuntimeInit
MethodAndArgsCaller.run(RuntimeInit.java:580)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:886)
2025-03-05 04:05:53.129  8295-8295  AmmarApplication        com.example.ammar2                   D  Firebase connectivity check results:
2025-03-05 04:05:53.143  8295-8295  AmmarApplication        com.example.ammar2                   D  Firebase Connectivity Report:
- Initialized: true
- Firebase Host Reachable: false
- Firebase Auth Host Reachable: false
- Firestore Connected: false (Failed to get document because the client is offline.)
- Auth Connected: true
- FCM Connected: false (java.io.IOException: java.util.concurrent.ExecutionException: java.io.IOException: AUTHENTICATION_FAILED)
- Fully Connected: false
2025-03-05 04:05:54.571  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:05:55.491  8295-8365  ProfileInstaller        com.example.ammar2                   D  Installing profile for com.example.ammar2
2025-03-05 04:05:56.913  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:06:00.762  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:06:07.331  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:06:18.010  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:06:38.105  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:07:03.837  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}
2025-03-05 04:07:46.864  8295-8347  ManagedChannelImpl      com.example.ammar2                   W  [{0}] Failed to resolve name. status={1}

This log output shows a detailed trace of an Android application (com.example.ammar2) startup and its interaction with Firebase services. Here's a breakdown of the key events and issues, categorized for clarity:

1. Application Startup and Initialization:

PROCESS STARTED: The log begins with the process starting for the application.

CompatChangeReporter: Reports compatibility changes. These are likely related to Android's backward compatibility mechanisms. ENABLED state suggests features are being adjusted for compatibility.

ziparchive: Multiple warnings about "Unable to open" .dm files. These .dm files are Dex (Dalvik Executable) metadata files used for ahead-of-time (AOT) compilation and optimization. Their absence isn't necessarily a fatal error; the app will likely fall back to just-in-time (JIT) compilation or interpreting the bytecode. It might indicate a problem with the build process or deployment.

nativeloader: Configures native libraries. This shows how the app is loading and linking native code (libraries like libEGL.so, libGLESv1_CM.so, etc.) from various locations (APK, system frameworks, product partitions). This is a crucial step for any app using native components.

GraphicsEnvironment: Logs graphics settings. The empty angle_gl_driver_selection_pkgs and angle_gl_driver_selection_values and the "invalid" Global.Settings indicate that the app isn't using ANGLE (Almost Native Graphics Layer Engine) for OpenGL ES rendering. It also indicates no updatable or prerelease graphics drivers are found.

FirebaseApp: Firebase initializes. Device unlocked: initializing all Firebase APIs shows that Firebase is starting up its services.

FirebaseInitProvider: Confirms successful Firebase initialization.

AmmarApplication: Custom application class logs. Crucially, it detects "No active network found, assuming disconnected" and warns "Network unavailable - Firebase services may not connect properly." This is the root cause of most of the subsequent errors.

Firestore offline persistence enabled: Indicates that Firestore is configured to work offline, caching data locally. This is good, but it won't help initial connections.

DynamiteModule: Loads modules from Google Play Services. It's selecting a remote version of com.google.android.gms.measurement.dynamite. More .dm file warnings appear here, again related to optimized code.

FA (Firebase Analytics): Firebase Analytics is initialized. It provides instructions on enabling debug logging.

ProviderInstaller: Attempts to load a security provider, but fails because no acceptable module is found. This is a potential security issue, as it might mean the app is using older, potentially vulnerable security protocols. It later successfully installs GmsCore_OpenSSL.

Choreographer: Skipped 36 frames!. This is a performance warning. The app is doing too much work on the main thread, causing UI jank (dropped frames). This is likely due to the combination of initialization tasks and network-related retries.

MainActivity: The main activity's onCreate method is called.

2. Firebase Connectivity Issues (The Core Problem):

ManagedChannelImpl: Repeatedly shows Failed to resolve name. status={1}. This is a gRPC (Google Remote Procedure Call) error, indicating that the app cannot resolve the hostname for Firebase services (specifically, firestore.googleapis.com). This is a DNS resolution failure.

Firestore: Numerous warnings and errors from Firestore:

Stream closed with status: Status{code=UNAVAILABLE, description=Unable to resolve host firestore.googleapis.com...}. This is the core Firestore connection error, stemming directly from the DNS resolution problem.

Could not reach Cloud Firestore backend. Connection failed 1 times. It confirms the connection failure and retries.

This typically indicates that your device does not have a healthy Internet connection... Firestore correctly diagnoses the likely cause.

FirebaseUtils: Custom utility class. It checks Firebase connectivity and reports failures:

Network is not available, skipping host reachability checks.

Firestore connectivity check failed: Failed to get document because the client is offline..

FCM connectivity check failed: ... AUTHENTICATION_FAILED. This is a secondary consequence of the network issue. FCM (Firebase Cloud Messaging) can't authenticate because it can't reach the servers.

FirebaseMessaging: Failed to get FIS auth token with a java.util.concurrent.ExecutionException and nested FirebaseInstallationsException. This is another symptom of the network problem; Firebase Installations can't get a token.

AmmarApplication FCM token retreival fails.

3. Authentication and User State:

GoogleSignInHelper: Initializes the Google Sign-In client.

LoginViewModel: Initializes a ViewModel for login-related logic.

AuthService: A custom service for handling authentication. It repeatedly refreshes the user state and finds "No Firebase user," which is expected since the user hasn't logged in and the network is down.

AuthScreen: A composable representing the authentication screen. It shows the initial screen and transitions to the "login" screen.

4. UI and Rendering:

.example.ammar2 (Likely Jetpack Compose): Warnings about SnapshotStateList methods failing lock verification. This is a performance issue within Jetpack Compose. It means some Compose code isn't optimized and might run slower. This is often due to ProGuard (code shrinking and obfuscation) misconfiguration.

HWUI: Hardware UI renderer logs:

Unknown dataspace 0. This might indicate a problem with color space handling.

Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without... and Failed to initialize 101010-2 format. These are EGL (Embedded-System Graphics Library) errors, indicating problems setting up the graphics context. The app likely recovers, but it might affect rendering quality or performance.

Gralloc4: mapper 4.x is not supported. Gralloc is the Android graphics memory allocator. This message means the newer version 4.x isn't available.

Davey!: A long "Davey!" log from HWUI, indicating a very long frame rendering time (2000ms and 2383ms). This confirms the severe UI jank. The main thread is blocked for a significant amount of time. The flags and other detailed timings show where the time is being spent.

Choreographer: Skipped 124 frames. This is a clear sign of severe UI unresponsiveness

5. Profile Installation

ProfileInstaller: Installs a profile for com.example.ammar2

Summary of Key Issues and Recommendations:

Network Connectivity: The primary problem is the lack of network connectivity. The app cannot resolve firestore.googleapis.com and other Firebase service hostnames. This needs to be addressed first. Verify:

Device Network: Ensure the device has a working internet connection (Wi-Fi or mobile data). Check signal strength, airplane mode, and any network restrictions.

DNS Settings: If the device's network is otherwise working, there might be a DNS issue. Try changing the DNS server (e.g., to Google Public DNS: ******* and *******) in the device's network settings.

Firewall/Proxy: If the device is on a corporate or restricted network, a firewall or proxy server might be blocking access to Google services.

Emulator: If the emulator is not properly connected, try restarting it, checking the network configurations, or using a physical device.

Firebase Authentication Failures: The AUTHENTICATION_FAILED errors for FCM are a consequence of the network problem. Once the network is restored, these should resolve themselves.

UI Jank (Dropped Frames): The "Skipped frames" warnings and the "Davey!" logs indicate significant UI performance problems. While the network issues are the primary culprit (blocking the main thread), there are other potential contributing factors:

Main Thread Overload: Reduce the amount of work done on the main thread. Move network operations, database access, and other long-running tasks to background threads (using Kotlin Coroutines, for example).

Compose Optimization: Address the SnapshotStateList lock verification warnings. Ensure that your Compose code is optimized and that ProGuard rules are correctly configured to avoid stripping necessary Compose runtime classes.

Provider Installer: The initial failure to load the security provider is a potential concern. While it falls back to GmsCore_OpenSSL, investigate why the preferred provider couldn't be loaded.

Missing .dm Files: While not fatal, the missing .dm files suggest a build or deployment issue. Ensure your build process is correctly generating these optimization files.

Graphics Issues: The HWUI and Gralloc4 warnings may cause performance degradation.

In short, fix the network connectivity first. That will likely resolve the Firebase connection and authentication problems. Then, address the UI performance issues by optimizing main thread operations and Jetpack Compose code. The graphics warnings are a lower priority but should be investigated.