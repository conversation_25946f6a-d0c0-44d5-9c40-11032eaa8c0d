package com.ammar.medical.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.ammar.medical.presentation.ui.screens.analysis.AnalysisResultScreen
import com.ammar.medical.presentation.ui.screens.camera.CameraScreen
import com.ammar.medical.presentation.ui.screens.document.DocumentDetailsScreen
import com.ammar.medical.presentation.ui.screens.document.DocumentEditScreen
import com.ammar.medical.presentation.ui.screens.home.HomeScreen
import com.ammar.medical.presentation.ui.screens.settings.SettingsScreen
import com.ammar.medical.presentation.ui.screens.scanner.QrScannerScreen
import com.ammar.medical.presentation.ui.screens.category.CategoryManagementScreen
import com.ammar.medical.presentation.ui.screens.export.ExportScreen
import com.ammar.medical.presentation.ui.screens.category.CategoryStatisticsScreen
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.Modifier

sealed class Screen(val route: String) {
    data object Home : Screen("home")
    object Camera : Screen("camera")
    object Analysis : Screen("analysis/{uri}/{documentType}/{patientInfo}/{doctorInfo}/{date}/{medicalDetails}") {
        fun createRoute(
            uri: String,
            documentType: String,
            patientInfo: String,
            doctorInfo: String,
            date: String,
            medicalDetails: String
        ) = "analysis/$uri/$documentType/$patientInfo/$doctorInfo/$date/$medicalDetails"
    }
    object Settings : Screen("settings")
    object QrScanner : Screen("qr_scanner")
    object DocumentDetails : Screen("document/{documentId}") {
        fun createRoute(documentId: Long) = "document/$documentId"
    }
    object DocumentEdit : Screen("document/{documentId}/edit") {
        fun createRoute(documentId: Long) = "document/$documentId/edit"
    }
    data object CategoryManagement : Screen("category_management")
    data object CategoryStatistics : Screen("category_statistics")
    data object Export : Screen("export")
}

@Composable
fun NavGraph(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    startDestination: String = Screen.Home.route
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        composable(Screen.Home.route) {
            HomeScreen(
                onNavigateToCamera = {
                    navController.navigate(Screen.Camera.route)
                },
                onNavigateToSettings = {
                    navController.navigate(Screen.Settings.route)
                },
                onNavigateToQrScanner = {
                    navController.navigate(Screen.QrScanner.route)
                },
                onNavigateToDocument = { documentId ->
                    navController.navigate(Screen.DocumentDetails.createRoute(documentId))
                }
            )
        }

        composable(Screen.Camera.route) {
            CameraScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onImageCaptured = { uri, documentType, patientInfo, doctorInfo, date, medicalDetails ->
                    navController.navigate(
                        Screen.Analysis.createRoute(
                            uri = uri.toString(),
                            documentType = documentType,
                            patientInfo = patientInfo,
                            doctorInfo = doctorInfo,
                            date = date,
                            medicalDetails = medicalDetails
                        )
                    )
                }
            )
        }

        composable(
            route = Screen.Analysis.route,
            arguments = listOf(
                navArgument("uri") { type = NavType.StringType },
                navArgument("documentType") { type = NavType.StringType },
                navArgument("patientInfo") { type = NavType.StringType },
                navArgument("doctorInfo") { type = NavType.StringType },
                navArgument("date") { type = NavType.StringType },
                navArgument("medicalDetails") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val uri = backStackEntry.arguments?.getString("uri") ?: return@composable
            val documentType = backStackEntry.arguments?.getString("documentType") ?: return@composable
            val patientInfo = backStackEntry.arguments?.getString("patientInfo") ?: return@composable
            val doctorInfo = backStackEntry.arguments?.getString("doctorInfo") ?: return@composable
            val date = backStackEntry.arguments?.getString("date") ?: return@composable
            val medicalDetails = backStackEntry.arguments?.getString("medicalDetails") ?: return@composable

            AnalysisResultScreen(
                documentType = documentType,
                patientInfo = parseMap(patientInfo),
                doctorInfo = parseMap(doctorInfo),
                date = date,
                medicalDetails = parseList(medicalDetails),
                onNavigateBack = {
                    navController.popBackStack()
                },
                onShare = {
                    // TODO: Implement sharing functionality
                }
            )
        }

        composable(Screen.Settings.route) {
            SettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable(Screen.QrScanner.route) {
            QrScannerScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onQrCodeScanned = { documentId ->
                    navController.popBackStack()
                    navController.navigate(Screen.DocumentDetails.createRoute(documentId))
                }
            )
        }

        composable(
            route = Screen.DocumentDetails.route,
            arguments = listOf(
                navArgument("documentId") { type = NavType.LongType }
            )
        ) {
            DocumentDetailsScreen(
                documentId = it.arguments?.getLong("documentId") ?: return@composable,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToEdit = { documentId ->
                    navController.navigate(Screen.DocumentEdit.createRoute(documentId))
                }
            )
        }

        composable(
            route = Screen.DocumentEdit.route,
            arguments = listOf(
                navArgument("documentId") { type = NavType.LongType }
            )
        ) {
            DocumentEditScreen(
                documentId = it.arguments?.getLong("documentId") ?: return@composable,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onSave = {
                    navController.popBackStack()
                }
            )
        }

        composable(Screen.CategoryManagement.route) {
            CategoryManagementScreen(
                onNavigateBack = { navController.navigateUp() }
            )
        }

        composable(Screen.CategoryStatistics.route) {
            CategoryStatisticsScreen(
                viewModel = hiltViewModel(),
                onNavigateBack = { navController.navigateUp() }
            )
        }

        composable(Screen.Export.route) {
            ExportScreen(
                onNavigateBack = { navController.navigateUp() }
            )
        }
    }
}

private fun parseMap(mapString: String): Map<String, String> {
    return mapString.split(",").associate { pair ->
        val (key, value) = pair.split("=")
        key to value
    }
}

private fun parseList(listString: String): List<String> {
    return listString.split(",")
} 