package io.ammar.medical.ui.screens

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import io.ammar.medical.QRScannerActivity
import io.ammar.medical.R
import io.ammar.medical.TextSharing
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import com.google.zxing.integration.android.IntentIntegrator
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log
import android.net.http.HttpResponseCache
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanOptions
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeoutException
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TextRephraserScreen(
    onNavigateBack: () -> Unit
) {
    var inputText by remember { mutableStateOf("") }
    var rephrasedText by remember { mutableStateOf<String?>(null) }
    var isProcessing by remember { mutableStateOf(false) }
    var error by remember { mutableStateOf<String?>(null) }
    
    // PC connection state
    var pcServerUrl by rememberSaveable { mutableStateOf<String?>(null) }
    var isPcConnected by rememberSaveable { mutableStateOf(false) }
    var isSendingToPC by rememberSaveable { mutableStateOf(false) }
    var pcConnectionStatus by rememberSaveable { mutableStateOf<String?>(null) }
    
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    
    // QR Code scanner launcher
    val qrCodeLauncher = rememberLauncherForActivityResult(ScanContract()) { result ->
        if (result.contents != null) {
            Log.d("TextRephraserScreen", "QR Code scanned: ${result.contents}")
            
            val qrContent = result.contents
            
            // Extract server URL from QR code
            try {
                if (qrContent.startsWith("PC_")) {
                    // This is a PC connection code from the sync script
                    val sessionId = qrContent.substringBefore(":")
                    pcServerUrl = "http://${context.resources.getString(R.string.server_ip)}:5000/send_text/default"
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC Transfer Service", Toast.LENGTH_SHORT).show()
                    Log.d("TextRephraserScreen", "Connected to PC with session ID: $sessionId")
                } else if (qrContent.startsWith("http://") || qrContent.startsWith("https://")) {
                    // This is a regular URL
                    pcServerUrl = qrContent
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC: $qrContent", Toast.LENGTH_SHORT).show()
                } else {
                    // Try to interpret as a direct IP address
                    pcServerUrl = "http://${qrContent}:5000/send_text/default"
                    isPcConnected = true
                    Toast.makeText(context, "Connected to PC at $qrContent", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("TextRephraserScreen", "Error processing QR code", e)
                Toast.makeText(context, "Error connecting to PC: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // Function to send text to PC via HTTP with retry mechanism
    suspend fun sendTextToPC(text: String, serverUrl: String): Result<String> = withContext(Dispatchers.IO) {
        var retries = 0
        val maxRetries = 3
        
        while (retries < maxRetries) {
            try {
                Log.d("TextRephraserScreen", "Sending text to PC (attempt ${retries+1}/$maxRetries): $serverUrl")
                
                val url = URL(serverUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8")
                connection.setRequestProperty("Accept", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 10000 // Increased timeout
                connection.readTimeout = 10000 // Increased timeout
                
                // Create JSON payload with proper JSON escaping
                val jsonObject = JSONObject()
                jsonObject.put("text", text)
                
                val jsonPayload = jsonObject.toString()
                Log.d("TextRephraserScreen", "Sending JSON payload length: ${jsonPayload.length}")
                
                // Send the request
                val outputStream = connection.outputStream
                val writer = OutputStreamWriter(outputStream, "UTF-8")
                writer.write(jsonPayload)
                writer.flush()
                writer.close()
                
                // Check response
                val responseCode = connection.responseCode
                if (responseCode in 200..299) { // Accept any 2xx status code
                    val inputStream = connection.inputStream
                    val response = inputStream.bufferedReader().use { it.readText() }
                    Log.d("TextRephraserScreen", "Server response: $response")
                    inputStream.close()
                    connection.disconnect()
                    return@withContext Result.success("Text sent successfully to PC")
                } else {
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e("TextRephraserScreen", "HTTP Error: $responseCode, Details: $errorResponse")
                    errorStream?.close()
                    connection.disconnect()
                    
                    // If we got a 500 error, retry
                    if (responseCode == 500 && retries < maxRetries - 1) {
                        retries++
                        delay(1000) // Wait 1 second before retrying
                        continue
                    }
                    
                    return@withContext Result.failure(Exception("Failed to send text. HTTP Error: $responseCode"))
                }
            } catch (e: Exception) {
                Log.e("TextRephraserScreen", "Connection error: ${e.message}")
                
                // If it's a network-related exception and we have retries left
                if ((e is IOException || e is TimeoutException) && retries < maxRetries - 1) {
                    retries++
                    delay(1000) // Wait 1 second before retrying
                    continue
                }
                
                return@withContext Result.failure(e)
            }
        }
        
        return@withContext Result.failure(Exception("Failed after $maxRetries attempts"))
    }
    
    // Initialize Gemini model
    val geminiModel = remember {
        GenerativeModel(
            modelName = "gemini-2.0-flash",
            apiKey = "AIzaSyCIvcmHA3ioub4qG_--ujkou702MfXYogk"
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.text_rephraser)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack, 
                            contentDescription = stringResource(R.string.back),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                actions = {
                    // Add QR code scan button to connect to PC
                    IconButton(
                        onClick = {
                            // Launch QR code scanner
                            val scanOptions = ScanOptions().apply {
                                setPrompt("Align QR code within the frame.")
                                setBeepEnabled(true)
                                setOrientationLocked(false)
                                setDesiredBarcodeFormats(ScanOptions.QR_CODE) // Focus only on QR codes
                                setBarcodeImageEnabled(true)
                                setTimeout(30000) // 30 second timeout
                                setCameraId(0) // Use back camera
                            }
                            qrCodeLauncher.launch(scanOptions)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.QrCodeScanner,
                            contentDescription = "Connect to PC",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp)
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // PC connection status card
            if (isPcConnected) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.tertiaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "Connected to PC",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "Server: $pcServerUrl",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                        
                        // Show connection status if available
                        pcConnectionStatus?.let { status ->
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = status,
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (status.contains("Error") || status.contains("Failed"))
                                        MaterialTheme.colorScheme.error
                                    else
                                        MaterialTheme.colorScheme.onTertiaryContainer
                            )
                        }
                    }
                }
            }
            
            // Input text field
            OutlinedTextField(
                value = inputText,
                onValueChange = { inputText = it },
                label = { Text("Enter text to rephrase") },
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 120.dp),
                maxLines = 10
            )
            
            // Rephrase button
            Button(
                onClick = {
                    if (inputText.isBlank()) {
                        error = "Please enter some text to rephrase"
                        return@Button
                    }
                    
                    coroutineScope.launch {
                        try {
                            isProcessing = true
                            error = null
                            
                            // Create prompt for rephrasing
                            val prompt = """
                                Please rephrase the following text to make it more formal, clear, and professional.
                                Maintain the original meaning but improve the language and structure.
                                
                                Text to rephrase:
                                $inputText
                            """.trimIndent()
                            
                            // Generate content with Gemini
                            val content = content { text(prompt) }
                            val response = geminiModel.generateContent(content)
                            rephrasedText = response.text
                            
                        } catch (e: Exception) {
                            error = "Error: ${e.message}"
                        } finally {
                            isProcessing = false
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isProcessing && inputText.isNotBlank()
            ) {
                if (isProcessing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(Icons.Default.Refresh, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Rephrase Text")
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Arabic explanation button
            Button(
                onClick = {
                    if (inputText.isBlank()) {
                        error = "Please enter some text to analyze"
                        return@Button
                    }
                    
                    coroutineScope.launch {
                        try {
                            isProcessing = true
                            error = null
                            
                            // Create prompt for Arabic explanation
                            val prompt = """
                                Please explain the following text in Egyptian Arabic dialect (using Arabic script).
                                Maintain the original meaning but make it easy to understand for an Egyptian Arabic speaker.
                                Use common Egyptian Arabic expressions and terminology.
                                
                                Text to explain:
                                $inputText
                            """.trimIndent()
                            
                            // Generate content with Gemini
                            val content = content { text(prompt) }
                            val response = geminiModel.generateContent(content)
                            rephrasedText = response.text
                            
                        } catch (e: Exception) {
                            error = "Error: ${e.message}"
                        } finally {
                            isProcessing = false
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isProcessing && inputText.isNotBlank(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                if (isProcessing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onSecondary,
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(Icons.Default.Refresh, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("شرح الكلام بالعربي")
                }
            }
            
            // Error message
            if (error != null) {
                Text(
                    text = error!!,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            // Rephrased text result
            if (rephrasedText != null) {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Rephrased Text:",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        
                        Text(
                            text = rephrasedText!!,
                            style = MaterialTheme.typography.bodyLarge
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Share buttons
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // Copy button
                            Button(
                                onClick = {
                                    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                    val clip = ClipData.newPlainText("Rephrased Text", rephrasedText)
                                    clipboardManager.setPrimaryClip(clip)
                                    Toast.makeText(context, "Text copied to clipboard", Toast.LENGTH_SHORT).show()
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(Icons.Default.ContentCopy, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Copy")
                            }
                            
                            // Share button
                            Button(
                                onClick = {
                                    TextSharing.shareText(rephrasedText!!, context, "Rephrased Text")
                                },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.secondary
                                )
                            ) {
                                Icon(Icons.Default.Share, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Share")
                            }
                            
                            // Send to PC button (if connected)
                            if (isPcConnected) {
                                Button(
                                    onClick = {
                                        pcServerUrl?.let { serverUrl ->
                                            isSendingToPC = true
                                            pcConnectionStatus = "Sending to PC..."
                                            
                                            coroutineScope.launch {
                                                val result = sendTextToPC(rephrasedText!!, serverUrl)
                                                isSendingToPC = false
                                                pcConnectionStatus = result.fold(
                                                    onSuccess = { it },
                                                    onFailure = { "Error: ${it.message}" }
                                                )
                                            }
                                        }
                                    },
                                    enabled = !isSendingToPC,
                                    modifier = Modifier.weight(1f),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.tertiary
                                    )
                                ) {
                                    if (isSendingToPC) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(24.dp),
                                            strokeWidth = 2.dp,
                                            color = MaterialTheme.colorScheme.onTertiary
                                        )
                                    } else {
                                        Icon(Icons.Default.Send, contentDescription = null)
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text("To PC")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
} 