package io.ammar.medical.ui.components

import android.widget.Toast
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import io.ammar.medical.data.Contact
import com.google.firebase.auth.FirebaseAuth

/**
 * A button that opens a contacts dialog
 */
@Composable
fun ContactsButton(
    onSelectContact: (Contact) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showContactsDialog by remember { mutableStateOf(false) }
    var showAddContactDialog by remember { mutableStateOf(false) }
    
    // Get current user
    val auth = FirebaseAuth.getInstance()
    val currentUser = auth.currentUser
    
    // Show contacts dialog when requested
    if (showContactsDialog) {
        ContactsListDialog(
            onDismiss = { showContactsDialog = false },
            onSelectContact = { contact ->
                onSelectContact(contact)
                showContactsDialog = false
            },
            onAddContact = { showAddContactDialog = true }
        )
    }
    
    // Show add contact dialog when requested
    if (showAddContactDialog) {
        AddContactDialog(
            onDismiss = { showAddContactDialog = false },
            onContactAdded = { /* Refresh contacts list */ }
        )
    }
    
    IconButton(
        onClick = { 
            if (currentUser != null) {
                showContactsDialog = true
            } else {
                Toast.makeText(context, "You must be signed in to view contacts", Toast.LENGTH_SHORT).show()
            }
        },
        modifier = modifier
    ) {
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = "View Contacts",
            tint = MaterialTheme.colorScheme.primary
        )
    }
} 